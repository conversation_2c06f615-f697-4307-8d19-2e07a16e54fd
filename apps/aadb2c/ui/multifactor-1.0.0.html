
<!DOCTYPE html>
<html lang="en-US">
<head>

    <title>Multi Factor Authentication</title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <meta name="locale" content="en-US">
    <meta name="ROBOTS" content="NONE, NOARCHIVE">
    <meta name="GOOGLEBOT" content="NOARCHIVE">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes">
    <style id="common">
        /* Following are generated styles via gulp. */
        /* inject: css */
        @charset "utf-8";.pageLevel,body{text-align:left}#panel,.pageLevel,.panel li,label{display:block}button{width:auto;min-width:50px;height:32px;margin-top:2px;-moz-border-radius:0;-webkit-border-radius:0;border-radius:0;background:#0078d4;border:1px solid #fff;color:#fff;transition:background 1s ease 0s;font-size:100%;padding:0 2px}button:hover{background:#0f3e83;border:1px solid #3079ed;-moz-box-shadow:0 0 0;-webkit-box-shadow:0 0 0;box-shadow:0 0 0}.buttons button{-moz-user-select:none;cursor:pointer;margin-right:4px;margin-left:0;padding:6px 12px;font-size:100%}.buttons button:disabled,button.disabled,button[aria-disabled=true],button[disabled]{background-color:#767676}.buttons button:focus{outline:#8a8886 solid 1px}.accountButton,button{-moz-user-select:none;user-select:none}.accountButton,a,button{cursor:pointer}::-webkit-input-placeholder{color:#6d6d6d}:-moz-placeholder{color:#6d6d6d}::-moz-placeholder{color:#6d6d6d}:-ms-input-placeholder{color:#6d6d6d!important}h1,h2{font-weight:400}.panel li{list-style:none}.panel .companyLogo{width:160px;margin-bottom:20px}#api ul li{display:inline;list-style-type:none;margin-left:0}.heading h1{margin-bottom:20px}html{background-color:#00abec}.normaltext,.smalltext,.tinytext{font-family:'Segoe UI',Segoe,SegoeUI-Regular-final,Tahoma,Helvetica,Arial,sans-serif}div#background_branding_container,div#background_page_overlay{width:100%;z-index:0;position:fixed;left:0;overflow:hidden;top:0}div#background_branding_container{background:#00abec;height:100%;-webkit-animation:fadeIn 1s;animation:fadeIn 1s}*,.panel,body{margin:0;padding:0}body{z-index:-999}.panel,.panel_layout,.panel_layout_row,body,html{height:100%}#panel{border-left:1px solid #fff}a{background-color:transparent;color:#2872dd;text-decoration:none}a:hover{text-decoration:underline}a:focus{outline:#8a8886 solid 1px}img{border:0}form{height:auto;width:auto}h2{font-size:1em}.normaltext{font-size:.9em}input:focus,textarea:focus{outline:0}.error{color:#a61e0c}.pageLevel{width:293px;margin-top:5px;font-size:1.1em;height:auto}.highlightError{border:1px solid #a61e0c!important}img#background_background_image{height:100%;width:100%}div#background_page_overlay{background:left top no-repeat fixed #f7f7f7;height:100%;opacity:1}.panel{background:#fff;float:right;height:100%;overflow-x:hidden;overflow-y:auto;position:fixed;right:0;width:500px;z-index:1}.accountButton,.accountButton:hover{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAh1BMVEX///9QUFBOTk5LS0tERERCQkI/Pz9ISEg6OjpGRkZNTU08PDyAgID09PSlpaWWlpZxcXFgYGBZWVlUVFT6+vrx8fHt7e3s7Ozo6Oji4uLJycnGxsa4uLiqqqqgoKCNjY2JiYmGhoZra2tmZmb7+/vu7u7d3d3U1NTNzc2+vr67u7usrKx7e3vprNQnAAAA8klEQVQ4y63Q127DMAxAUZpDwyMeSdqsNqu7/f/va6zahgGJKAr0vgk6DyQh+6V/BiTOOeNRA9zuAWBdM6WBlPDTvaUUoAuMrT0mgNvA1IJjQB3MKjACvp6DK0WAH+agtH8H9jQHLUUgz7Uhx8xOXzNESxirLCYA2mw8tacI5FyIYXq8A9ge2Qs6oTnw2e2ruho2rjBcXJ4ADh3jBOQLQnVhRFx2gNDZ4ACogbHXj/ft9Dj5AcgbJFu5AThQWuYBIGmgtAFQo4EFB+CPGthJAPypgY3BHsheA5UNwLyAvsYNoDyroKUe4EoFTQ/yDtTONvsGUJ8KTUYyH+UAAAAASUVORK5CYII=);background-repeat:no-repeat}.inner_container{max-height:90%;min-height:90%;width:100%}#panel_center,#panel_left,#panel_right{display:inline-block;border:0;height:100%;margin:0}#panel_left{padding:0;width:50px}#panel_center{min-height:100%;padding:0;width:378px}.hide{opacity:0}.api_container{display:inline-block;padding-left:0;padding-top:120px;position:relative;transition:padding .6s ease 0s;width:100%;height:100%}select{height:28px}input[type=email],input[type=number],input[type=password],input[type=text]{-moz-border-radius:0;-webkit-border-radius:0;border-radius:0;height:28px;width:300px;border:1px solid #696969;z-index:3;color:#000;padding:0 0 0 3px;-moz-box-shadow:0 0 0;-webkit-box-shadow:0 0 0;box-shadow:0 0 0;margin-right:3px}input[type=email]:focus,input[type=number]:focus,input[type=password]:focus,input[type=text]:focus,select:focus{outline:1px solid #4d90fe}.accountButton{border:1px solid #fff;color:#fff;margin-left:0;margin-right:2px;transition:background-color 1s ease 0s;-moz-border-radius:0;-webkit-border-radius:0;border-radius:0;text-align:center;word-wrap:break-word;height:34px;width:158px;padding-left:30px;background-color:#505050}.accountButton:hover{background-color:#b9b9b9;border:1px solid #fff;-moz-box-shadow:0 0 0;-webkit-box-shadow:0 0 0;box-shadow:0 0 0}#GoogleExchange:focus,.accountButton:focus{outline:#8a8886 solid 1px}#AppleExchange,#AppleManagedExchange,#GitHubExchange,#GoogleExchange,#MicrosoftAccountExchange{border:#aaa solid 1px;color:#000;background-color:#fff}#MicrosoftAccountExchange{background-image:url(data:image/jpeg;base64,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)}#AppleExchange:hover,#AppleManagedExchange:hover,#GitHubExchange:hover,#GoogleExchange:hover,#MicrosoftAccountExchange:hover{background-color:#eee}#GoogleExchange{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAhCAMAAACP+FljAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAABOFBMVEX////62NXyi4Pual/rUEPqSz3tXlLyi4L5y8f86OfvcmjqQzXub2T85uT61dLrU0fvdWv86+nrT0LtX1PxgnnwgHfsXVHuaF398vHvdWnsWEv4xsL4wr7wenD+7sP0jRfqRjPsVkn85eT81mf7vAX3pQ7sUy/3ubT7xzH5uBj+/PyGsfjC1/v7vxP7yz9ChfSSuPiMtPj7xzDzwR78/fxel/VZlPWcv/n51GTVuBNOqki03r+vy/q60vv3672vtCI6qFA0qFNIsGTj8+jw9f5clvVDhfRrv4JLsWbC5cvY7t5gnt6Gsffp9exBrV5StGx4xY17xo9cuXU1pls9j8FNjPTo8P3S69lGr2JLoaPO3/zn9Opnvn5UtW7X7t3V7duCyZVeundCrl87q1lOs2l6xo+84sb9/v2qdWS0AAAAAWJLR0QAiAUdSAAAAAd0SU1FB+EIGQ4HDEDExrUAAADWSURBVDjLY2AYBZiAkYmZhZWNnQOHNCcXNxTw8GKT5+PnhgMBLPKCQiAZYRFRMW5ucQksCiSB0lLSIJaMLDZ5OXkFbkUlPB5QVlFVU8fnQw0VFU0tEEMbCnTQFOiqqOiBGfpQYEBIgSGaAiMVFWMTEMMUCMyACszRFFioWFpZwzg2QAW2aArs7B0cnZwhbBdXfX03F3RvuDs6Onp4glhe3kADfDD86esHVOHoHxAY5Bgcoh8ahhkS4RGOcBAZhS2somNg8rFxOIIzPiExKTklNS2dYaQBADviJWzWK7IJAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE3LTA4LTI1VDE0OjA3OjEyLTA0OjAwBQDEvwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxNy0wOC0yNVQxNDowNzoxMi0wNDowMHRdfAMAAAAASUVORK5CYII=)}#GitHubExchange{background-image:url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgaGVpZ2h0PSIxNzkyIiB2aWV3Qm94PSIwIDAgMTc5MiAxNzkyIiB3aWR0aD0iMTc5MiIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMTY2NCA4OTZxMCAyNTEtMTQ2LjUgNDUxLjV0LTM3OC41IDI3Ny41cS0yNyA1LTM5LjUtN3QtMTIuNS0zMHYtMjExcTAtOTctNTItMTQyIDU3LTYgMTAyLjUtMTh0OTQtMzkgODEtNjYuNSA1My0xMDUgMjAuNS0xNTAuNXEwLTEyMS03OS0yMDYgMzctOTEtOC0yMDQtMjgtOS04MSAxMXQtOTIgNDRsLTM4IDI0cS05My0yNi0xOTItMjZ0LTE5MiAyNnEtMTYtMTEtNDIuNS0yN3QtODMuNS0zOC41LTg2LTEzLjVxLTQ0IDExMy03IDIwNC03OSA4NS03OSAyMDYgMCA4NSAyMC41IDE1MHQ1Mi41IDEwNSA4MC41IDY3IDk0IDM5IDEwMi41IDE4cS00MCAzNi00OSAxMDMtMjEgMTAtNDUgMTV0LTU3IDUtNjUuNS0yMS41LTU1LjUtNjIuNXEtMTktMzItNDguNS01MnQtNDkuNS0yNGwtMjAtM3EtMjEgMC0yOSA0LjV0LTUgMTEuNSA5IDE0IDEzIDEybDcgNXEyMiAxMCA0My41IDM4dDMxLjUgNTFsMTAgMjNxMTMgMzggNDQgNjEuNXQ2NyAzMCA2OS41IDcgNTUuNS0zLjVsMjMtNHEwIDM4IC41IDg5dC41IDU0cTAgMTgtMTMgMzB0LTQwIDdxLTIzMi03Ny0zNzguNS0yNzcuNXQtMTQ2LjUtNDUxLjVxMC0yMDkgMTAzLTM4NS41dDI3OS41LTI3OS41IDM4NS41LTEwMyAzODUuNSAxMDMgMjc5LjUgMjc5LjUgMTAzIDM4NS41eiIvPjwvc3ZnPg==);background-size:26px;background-position-x:5px;background-position-y:4px}#AppleExchange,#AppleManagedExchange{background-image:url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDIwIDIwIj48dGl0bGU+YXNzZXRzPC90aXRsZT48cGF0aCBkPSJNMTUuNDU0LDEwLjYzOEE0LjU0Nyw0LjU0NywwLDAsMSwxNy42Miw2LjgyM2E0LjY1Niw0LjY1NiwwLDAsMC0zLjY2Ni0xLjk4MmMtMS41NDMtLjE2Mi0zLjAzOC45MjMtMy44MjUuOTIzcy0yLjAxMS0uOTA3LTMuMzE0LS44ODNBNC44ODIsNC44ODIsMCwwLDAsMi43MDYsNy4zODdjLTEuNzc2LDMuMDc2LS40NTEsNy42LDEuMjUxLDEwLjA4M0M0LjgwOSwxOC42ODcsNS44LDIwLjA0Nyw3LjEwNiwyMGMxLjI3My0uMDUzLDEuNzUtLjgxMywzLjI4OC0uODEzLDEuNTIzLDAsMS45NjkuODEzLDMuMy43ODIsMS4zNjgtLjAyMiwyLjIyOS0xLjIyMywzLjA1LTIuNDUyYTEwLjA3MSwxMC4wNzEsMCwwLDAsMS4zOTUtMi44NDFBNC4zOTMsNC4zOTMsMCwwLDEsMTUuNDU0LDEwLjYzOFoiLz48cGF0aCBkPSJNMTIuOTQyLDMuMjA3QTQuNDc1LDQuNDc1LDAsMCwwLDEzLjk2NiwwLDQuNTUyLDQuNTUyLDAsMCwwLDExLjAyLDEuNTI1LDQuMjYxLDQuMjYxLDAsMCwwLDkuOTY5LDQuNjE3LDMuNzcsMy43NywwLDAsMCwxMi45NDIsMy4yMDdaIi8+PC9zdmc+);background-size:24px;background-position-x:5px;background-position-y:3px}#TwitterExchange{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAdVBMVEVgqd3///9Ypdtdp9xaptxSotpQodlNn9lWo9pUo9rX6Pa+2vGTw+iLvuZlqt79/P7K4PO62O+y0+6hyutysuD2+fzi7vne6/fT5PTE3fKs0O2lzeuZx+l7tuJqrd71+Pzz9vzn8PnQ4/SCueSAueNsrt9InNh7sQwBAAAAwklEQVQ4y92PRw6EMAwAXeIkdBbY3uv/n7gSAoLDD5hbPCPZgZVihEgYgNSUpmfS7bfbtHS2nReyL2Qoc+yp8ZRAwCEWjgGAPQ7sssKoAGsWBrrgyMZCwD77Uel+59E3Tt14xZ7qlY7BRf1CDgeMKMw8sBXGlKxWtLGvHCgkQ80m0YHpjjq4sQ74pn1mISLJVSAMiwJO98l/TWSNF1eGKzqKfZ7Vj0mnHHwodpP+WIYlZP373DTtVWxYr2FD3pOBdfIHhOAHYHQI9VgAAAAASUVORK5CYII=);background-color:#60a9dd}#TwitterExchange:hover{background-color:#bfdcf1}#FacebookExchange{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAaVBMVEU7W5z///85Wps3WJsiRo8xU5fw8vYyUpY0VZiAj70pS5OBkb0vUpb7+fwsTpTR1ud6irllerBPaqX09fnx8vfs7fSQoMZxg7VsgLNGY6FCX58ZP4v++/7r7vTZ3OupstGIlsFWcalDYaCK3qwDAAAAnklEQVQ4y+XQyw7CIBAFUBgc5VUoWGtb3/7/RyoYkyZAiSsXvdt7kstA/hRg/B0GpZ6byQ3Dw0NBaH+lMYRle3T0kwayACRdBrr/gnN+QtpQWv8cR4DswiUAjozlz4RdF8AmlnmwjaDQImoZwQkRedoToUS7D+ColGoTwQidx8oEQDMHN1MBva5MOL70SCHuE1TOhOpHrRt0FWAOP4IX8PsG2qEOR30AAAAASUVORK5CYII=);background-color:#3b5b9c}#FacebookExchange:hover{background-color:#b0bdd7}#LinkedInExchange{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAb1BMVEUAe7b///8AdrMklscAc7EAeLUAcbB5ttifzeMqmckAdLIAaqz7+/6PxeAShr0CgLkAba4nmMctksTv9Puw1eij0OWGvNtfrNJNo80YjMAeib/D4vGt3Oy82+yfzOOCvtyJvdx3tddirtI/ncoxmMj9KsrQAAAAw0lEQVQ4y9WSVw7DIAxAG8CkjJDVzO5x/zMWk0RNJaB/kfo+sGUeCMvstgI4J7F9aS5NxSLnTWLpZVDgexTqIiycUNBhgTxRyCKPYJ3dl7sITCkO+FyLXaWU310DscASOesf3ahWChGJ5cb4ASO5Joiu2EegWEmZa1c3yUwOHmHNuQgJup4CgF8YlKpcMhKvkNmb1REz6hdetsyziIBldv8lpH8ouGm28zQFCu2SOSAXlJYGYCgpFThEMFPm/zCryja8Acy7CRfMrcKPAAAAAElFTkSuQmCC);background-color:#0077b5}#LinkedInExchange:hover{background-color:#99cae1}#AmazonExchange{background-image:url(data:image/gif;base64,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);background-color:#fff;color:transparent}.working{background:url(data:image/gif;base64,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) no-repeat;height:10px;width:auto}@-webkit-keyframes fadeIn{from{opacity:0}to{opacity:1}}@media only screen and (min-height:250px) and (max-width:450px),(min-device-height:250px) and (max-device-width:450px){html{overflow:hidden}.tinytext{font-size:.6em}.smalltext{font-size:.7em}.normaltext{font-size:.8em}.bigtext{font-size:.9em}.gianttext{font-size:1.2em}div#background_branding_container{display:none;opacity:0;z-index:-999}div#background_page_overlay{display:none;z-index:-999}.panel_layout,.panel_layout_row{width:100%}#panel_left{width:40px}#panel_center{width:calc(100% - 80px)}.inner_container,.panel{width:100%}#panel_right{display:none}.panel{float:none;height:100%;margin:0;min-width:220px;overflow:auto;padding:0;z-index:1}}@media only screen and (max-width:501px),(max-device-width:501px){*{-webkit-transition:all 1s ease;-moz-transition:all 1s ease;-o-transition:all 1s ease;transition:all 1s ease}#panel{width:100%}#panel_layout_row{width:100%}#panel_left{display:none}#panel_center{width:100%}.panel_layout{width:100%}.attrEntry input[type=text],input[type=email],input[type=password]{width:calc(100% - 5px)!important}.attrEntry .dropdown_single{width:100%!important}}
        /* endinject */
    </style>

    <style>
        /* Following are generated styles via gulp. */
        /* inject: css */
        #api ul li{display:inline;list-style-type:none;margin-left:0}span.placeholder{color:#696969;margin:6px 0 0 -6px;padding-left:10px;width:380px;z-index:-1}input[type=text],select{-moz-border-radius:0;-webkit-border-radius:0;border-radius:0;height:28px;margin:0;border:1px solid #b8b8b8;z-index:3;background:0 0;color:#000;padding:0 0 0 3px;-moz-box-shadow:0 0 0;-webkit-box-shadow:0 0 0;box-shadow:0 0 0}input[type=text]:focus{border:1px solid #6b6b6b}input[type=text]:invalid{outline:0}#countryCode{width:auto;margin:0 100px 0 0}#localNumber{width:135px}#oneWaySmsControls,#phoneEntry,#phoneNumbers{padding:5px 0}.phoneNumber{padding-top:3px}#retryCode{width:auto}.errorText{padding:20px;width:310px}#phoneEntry,#phoneNumbers{margin:18px 0}.phoneNumber div{display:inline;line-height:32px}.phoneNumber .number{font-weight:500}.numberSelector{padding-right:5px}.type:after{content:"-";padding-left:2px;padding-right:2px}.buttons{margin-top:3px}.buttons button{min-width:90px;margin-right:3px}.textButton{display:inline;font:inherit;margin:0;outline:0;outline-offset:0;color:#00f;cursor:pointer;text-decoration:underline}.textButton::-moz-focus-inner{border:none;padding:0}.textButton,.textButton:hover{background:0 0;border:none;padding:0}.working{display:none}.actionLabel label{display:inline-block;vertical-align:baseline}.codePart{height:32px;width:28px}#verificationCode{width:85px}
        /* endinject */
    </style>

</head>
<body>
    <div id="background_branding_container" data-tenant-branding-background-color="true">
        <img id="background_background_image" data-tenant-branding-background="true" src="https://login.microsoftonline.com/static/tenant/default/img/default_signin_illustration_optimized.png" alt="Illustration" aria-hidden="true" />
    </div>
    <div class="panel" id="panel">
        <table class="panel_layout" role="presentation">
            <tbody>
                <tr class="panel_layout_row">
                    <td id="panel_left" />
                    <td id="panel_center">
                        <div class="inner_container">
                            <div class="api_container normaltext">
                                <img alt="Company Logo" class="companyLogo" style="display: none;" data-tenant-branding-logo="true" />
                                <div id="api" role="main"></div>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <script>"use strict";$(document).ready(function () { if (navigator.userAgent.match(/IEMobile\/10\.0/)) { var t = document.createElement("style"); t.appendChild(document.createTextNode("@-ms-viewport{width:auto!important}")), t.appendChild(document.createTextNode("@-ms-viewport{height:auto!important}")), document.getElementsByTagName("head")[0].appendChild(t) } if (navigator.userAgent.match(/MSIE 10/i)) { var e = $("#footer_links_container"); $(e).css("padding-top", "100px") } var o, i = $("#background_background_image"), n = function () { document.body.style.overflow = "hidden", ($(window).width() - 500) / $(window).height() < o ? (i.height($(window).height()), i.width("auto")) : (i.width($(window).width() - 500), i.height("auto")), document.body.style.overflow = "" }; $("<img>").attr("src", i.attr("src")).on("load", function () { o = this.width / this.height, n() }), $(window).resize(function () { n() }), "undefined" != typeof $("#MicrosoftAccountExchange") && $("#MicrosoftAccountExchange").text("Microsoft"), $("*").removeAttr("placeholder") });</script>
</body>
</html>
