<!DOCTYPE html>
<html>
  <head>
    <title>Sign up or sign in</title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <meta name="ROBOTS" content="NONE, NOARCHIVE" />
    <meta name="GOOGLEBOT" content="NOARCHIVE" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes" />
    <style id="common">
      @charset "utf-8";

      @font-face {
        font-family: 'Helvetica Now Var';
        src: url('./fonts/HelveticaNowVar/HelveticaNowVarW05-Regular.woff') format('woff-variations');
        src: url('./fonts/HelveticaNowVar/HelveticaNowVarW05-Regular.woff2') format('woff2-variations');
        font-weight: 100 900;
        font-display: swap;
      }

      *,
      *::before,
      *::after {
        box-sizing: border-box;
      }

      body,
      h1,
      h2,
      h3,
      h4,
      p {
        margin: 0;
      }

      body {
        font-family: 'Helvetica Now Var', sans-serif;
        color: #333;
        font-size: 18px;
        line-height: 1.33;
        font-weight: 300;
      }

      input,
      button {
        font: inherit;
      }

      .outer-container {
        display: block;
      }

      .login-container {
        margin: 0 auto;
        width: 343px;
        display: flex;
        padding: 0px;
        gap: 28px;
        flex-direction: column;
        justify-content: center;
        margin-top: 28px;
      }

      .companyLogo {
        width: 93px;
      }

      .heading {
        margin-bottom: 28px;
      }

      h1 {
        font-size: 28px;
        font-weight: 200;
        line-height: 1.14;
        letter-spacing: normal;
      }

      h2 {
        display: none;
      }

      label {
        display: inline-block;
      }

      input[type='password'],
      input[type='text'] {
        height: 56px;
        width: 100%;
        outline: none;
        border-radius: 2px;
        border: 1px solid #333;
        padding: 16px 20px;
        font-weight: 300;
        background-color: #fff;
      }

      input[type='text']::placeholder,
      input[type='password']::placeholder {
        color: #666;
      }

      input[type='password']:focus-visible,
      input[type='text']:focus-visible {
        outline: #4b96fe 2px solid;
        outline-offset: 2px;
        border-radius: 2px;
      }

      input[type='password']:hover,
      input[type='text']:hover {
        background-color: #e5e5e5;
        color: #000;
        border: 1px solid #000;
      }

      button {
        cursor: pointer;
        background: #000;
				color: #fff;
        width: 100%;
        height: 56px;
        border: solid 1px transparent;
        border-radius: 2px;
        padding: 16px 28px;
        margin-top: 28px;
      }

      button:hover {
				background: #333;
        color: #fff;
      }

      #next:active {
        border: 2px solid #000;
        padding: 15px 27px;
      }

      .buttons {
        margin-top: 28px;
      }

      .buttons button:focus-visible {
        outline: none;
        outline: #4b96fe 2px solid;
        outline-offset: 2px;
        border-radius: 2px;
      }

      .buttons button:disabled,
      button.disabled,
      button[aria-disabled='true'],
      button[disabled] {
        background-color: #e5e5e5;
        border: solid 1px transparent;
        color: #666;
      }

      .divider {
        width: 100%;
        height: 2px;
        margin: 28px 0;
        background-color: #ddd;
      }

      #createAccount {
        box-sizing: border-box;
        display: inline-block;
        height: 56px;
        width: 100%;
        border: 1px solid #333333;
        border-radius: 2px;
        padding: 16px 28px;
        text-align: center;
        text-decoration: none;
        color: #333;
        font-size: 18px;
        font-weight: 300;
        line-height: 1.33;
      }

      #createAccount:hover {
        border: 1px solid #000;
        color: #000;
        background-color: #e5e5e5;
      }

      #createAccount:active {
        border: 2px solid #000;
        color: #000;
        background-color: #e5e5e5;
        padding: 15px 27px;
      }

      #createAccount:focus-visible {
        outline: none;
        outline: #4b96fe 2px solid;
        outline-offset: 2px;
        border-radius: 2px;
      }

      .pageLevel {
        color: #b52626;
        margin-top: 8px;
        font-size: 14px;
        line-height: 1.43;
      }

      .entry-item {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 16px;
      }

      .itemLevel {
        order: 3;
        font-size: 14px;
        line-height: 1.43;
      }

      .create p {
        display: flex;
        flex-direction: column;
        gap: 28px;
        font-size: 22px;
        font-weight: 300;
        line-height: 1.27;
      }

      .highlightError {
        border: 1px solid #b52626 !important;
      }

      #background_branding_container {
        display: none;
        width: 60%;
        overflow: hidden;
      }

      .buttons a:link,
      .buttons a:visited {
        font-size: 18px;
        font-weight: 300;
        line-height: 1.33;
        color: #333;
        outline: none;
        cursor: pointer;
      }

      .buttons a:hover,
      .buttons a:active {
        color: #000;
      }

      .buttons a:focus-visible {
        color: #333;
        outline: #4b96fe 2px solid;
        outline-offset: 2px;
        border-radius: 2px;
      }

      @media (min-width: 768px) {
        .login-container {
          width: 41.5%;
          margin-top: 36px;
          gap: 36px;
        }

        .divider {
          margin: 36px 0;
        }
      }

      @media (min-width: 1200px) {
        .login-container {
          width: 509px;
        }
      }

      @media (min-width: 1920px) {
        .login-container {
          width: 509px;
          gap: 44px;
        }

        .divider {
          margin: 44px 0;
        }

        #background_branding_container {
          display: block;
        }

        .outer-container {
          display: flex;
          justify-content: space-between;
          height: 100vh;
          width: 100%;
        }
      }

      .asterisk {
        display: inline-block;
        color: #b52626;
        padding-left: 2px;
      }

      form {
        display: flex;
        flex-direction: column;
      }

      .intro {
        order: -2;
      }

      .entry {
        order: -1;
      }
    </style>
  </head>
  <body>
    <div class="outer-container">
      <div class="login-container">
        <img alt="Company Logo" class="companyLogo" style="display: none" data-tenant-branding-logo="true" />
        <div id="api" role="main"></div>
      </div>
      <div id="background_branding_container" data-tenant-branding-background-color="true">
        <img
          id="background_background_image"
          data-tenant-branding-background="true"
          src="https://login.microsoftonline.com/static/tenant/default/img/default_signin_illustration_optimized.png"
          alt="Illustration"
          aria-hidden="true"
        />
      </div>
    </div>

    <script>
      'use strict';
      $(document).ready(function () {
        if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
          var t = document.createElement('style');
          t.appendChild(document.createTextNode('@-ms-viewport{width:auto!important}')),
            t.appendChild(document.createTextNode('@-ms-viewport{height:auto!important}')),
            document.getElementsByTagName('head')[0].appendChild(t);
        }
        if (navigator.userAgent.match(/MSIE 10/i)) {
          var e = $('#footer_links_container');
          $(e).css('padding-top', '100px');
        }
        var o,
          i = $('#background_background_image'),
          n = function () {
            (document.body.style.overflow = 'hidden'),
              ($(window).width() - 500) / $(window).height() < o
                ? (i.height($(window).height()), i.width('auto'))
                : (i.width($(window).width() - 500), i.height('auto')),
              (document.body.style.overflow = '');
          };
        $('<img>')
          .attr('src', i.attr('src'))
          .on('load', function () {
            (o = this.width / this.height), n();
          }),
          $(window).resize(function () {
            n();
          });
      });

      //SET PASSWORD TITLE
      const currentLanguage = document.getElementsByTagName('html')[0].getAttribute('lang');
      const passwordInput = document.querySelector('#password');
      if (currentLanguage.startsWith('de')) {
        passwordInput.setAttribute('title', 'Passwort eingeben');
      } else if (currentLanguage.startsWith('fr')) {
        passwordInput.setAttribute('title', 'Saisir le mot de passe');
      } else if (currentLanguage.startsWith('it')) {
        passwordInput.setAttribute('title', 'Inserire la password');
      } else if (currentLanguage.startsWith('es')) {
        passwordInput.setAttribute('title', 'Introduzca la contraseña');
      } else if (currentLanguage.startsWith('en')) {
        passwordInput.setAttribute('title', 'Enter password	');
      }

      // CHANGE PLACEHOLDER
      function changePlaceholder(id) {
        let input = document.getElementById(id);

        if (!input) {
          return;
        }
        input.placeholder = input.title;
      }
      changePlaceholder('signInName');
      changePlaceholder('password');

      //ADD ASTERISK
      let input1 = document.getElementById('signInName');
      let input2 = document.getElementById('password');
      let label1 = document.querySelector('label[for="signInName"]');
      let label2 = document.querySelector('label[for="password"]');
      let labelText1 = label1.innerHTML;
      let labelText2 = label2.innerHTML;
      let errorBlock = document.querySelector('.pageLevel');

      function addAsterisk() {
        setTimeout(() => {
          if (input1.className === 'highlightError') {
            label1.innerHTML = labelText1 + '<span class="asterisk">*</span>';
          } else label1.innerHTML = labelText1;

          if (input2.className === 'highlightError') {
            label2.innerHTML = labelText2 + '<span class="asterisk">*</span>';
          } else label2.innerHTML = labelText2;
        }, 0);
      }

      const button = document.querySelector('#next').addEventListener('click', addAsterisk);
      //
      let observer = new MutationObserver(function (mutations) {
        for (let mutation of mutations) {
          if (mutation.type === 'attributes') {
            if (errorBlock.getAttribute('aria-hidden') == 'false') {
              input1.style.border = '1px solid #b52626';
              input2.style.border = '1px solid #b52626';
              label1.innerHTML = labelText1 + '<span class="asterisk">*</span>';
              label2.innerHTML = labelText2 + '<span class="asterisk">*</span>';
            }
          }
        }
      });

      observer.observe(errorBlock, { attributes: true });

      //MOVE LINK PASSORD FORGOTTEN
      $('.buttons').prepend($('.password-label>a'));
    </script>
  </body>
</html>
