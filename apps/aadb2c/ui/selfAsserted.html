<!DOCTYPE html>
<html>
  <head>
    <title>User details</title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <meta name="ROBOTS" content="NONE, NOARCHIVE" />
    <meta name="GOOGLEBOT" content="NOARCHIVE" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes" />
    <style id="common">
      /* Following are generated styles via gulp. */
      /* inject: css */
      @charset "utf-8";

      @font-face {
        font-family: 'Helvetica Now Var';
        src: url('./fonts/HelveticaNowVar/HelveticaNowVarW05-Regular.woff') format('woff-variations');
        src: url('./fonts/HelveticaNowVar/HelveticaNowVarW05-Regular.woff2') format('woff2-variations');
        font-weight: 100 900;
        font-display: swap;
      }
      *,
      *::before,
      *::after {
        box-sizing: border-box;
      }

      body,
      h1,
      h2,
      h3,
      h4,
      p {
        margin: 0;
      }

      body {
        font-family: 'Helvetica Now Var', sans-serif;
        color: #333;
        font-size: 18px;
        line-height: 1.33;
        font-weight: 300;
      }

      input,
      select,
      button {
        font: inherit;
      }
      .outer-container {
        display: block;
      }

      .inner_container {
        margin: 0 auto;
        width: 343px;
        display: flex;
        padding: 0px;
        gap: 28px;
        flex-direction: column;
        justify-content: center;
        margin-top: 28px;
      }

      .companyLogo {
        width: 93px;
      }

      h1 {
        font-size: 28px;
        font-weight: 200;
        line-height: 1.14;
      }

      a:link,
      a:visited {
        color: #333;
        outline: none;
        cursor: pointer;
      }

      a:hover,
      a:active {
        color: #000;
      }

      a:focus-visible {
        color: #333;
        outline: #4b96fe 2px solid;
        outline-offset: 2px;
        border-radius: 2px;
      }

      .form_legend_personal_data_li .itemLevel {
        display: none;
      }

      .form_legend_personal_data_li #form_legend_personal_data_label {
        margin-top: 44px;
        margin-bottom: 28px;
        font-size: 22px;
        line-height: 1.27;
      }

      .form_legend_contact_li #form_legend_contact_label {
        margin-top: 60px;
        margin-bottom: 12px;
        font-size: 22px;
        line-height: 1.27;
      }

      .newPassword_li {
        margin-top: 44px;
      }

      label#email_label {
        margin-top: 44px;
        margin-bottom: 8px;
      }

      label#VerificationCode_label {
        margin-bottom: 8px;
        margin-top: 8px;
      }

      label#extension_crmLeadGenderCodeId_label,
      label#email_ver_input_label,
      label#newPassword_label,
      label#givenName_label,
      label#surname_label,
      label#extension_company_label,
      label#extension_crmLeadTargetGroupId_label,
      label#country_label,
      label#state_label,
      label#city_label,
      label#postalCode_label,
      label#streetAddress_label,
      label#extension_phone_label,
      label#reenterPassword_label {
        margin-bottom: 8px;
        margin-top: 16px;
      }

      label#readonlyEmail_label {
        margin-bottom: 8px;
      }

      #extension_agreeTerms_label {
        display: none;
      }

      div#emailVerificationControl_info_message {
        display: block !important;
      }

      div#emailVerificationControl_error_message {
        color: #b52626;
        background-color: #ffc9c9;
        padding: 8px 16px;
        border-radius: 2px;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.12), 0 0 2px 0 rgba(0, 0, 0, 0.14);
      }

      div.verificationInfoText {
        margin-top: 12px;
      }

      input[type='checkbox'] {
        align-self: flex-start;
        cursor: pointer;
        outline: none;
      }

      input[type='email'],
      input[type='number'],
      input[type='password'],
      input[type='text'] {
        height: 56px;
        width: 100%;
        outline: none;
        border-radius: 2px;
        border: 1px solid #333;
        padding: 16px 20px;
      }

      input[type='text']::placeholder {
        color: #666;
      }
      input[type='checkbox']:focus-visible,
      input[type='password']:focus-visible,
      input[type='text']:focus-visible {
        outline: #4b96fe 2px solid;
        outline-offset: 2px;
        border-radius: 2px;
      }

      input[type='password']:hover,
      input[type='text']:hover {
        background-color: #e5e5e5;
        color: #000;
        border: 1px solid #000;
      }

      input[type='checkbox']:hover {
        background-color: (#e5e5e5);
        box-shadow: 0 0 0 4px #e5e5e5;
      }

      input[type='password']:disabled,
      input[type='text']:disabled {
        border: solid 2px #e5e5e5;
        color: #666;
        background-color: #fff;
      }

      button {
        cursor: pointer;
        width: 100%;
        height: 56px;
        border: solid 1px transparent;
        border-radius: 2px;
        -moz-border-radius: 2p;
        -webkit-border-radius: 2px;
        padding: 16px 28px;
      }

      button:focus-visible {
        outline: none;
        outline: #4b96fe 2px solid;
        outline-offset: 2px;
        border-radius: 2px;
      }

      .pageLevel {
        /* margin-bottom: 16px; */
        margin-top: 12px;
      }

      #email_ver_but_send,
      #email_ver_but_verify,
      #email_ver_but_edit {
        margin-top: 44px;
      }

      #email_ver_but_resend {
        margin-top: 16px;
      }
      button#continue,
      button.editButton,
      button.verifyButton,
      button.sendButton,
      button.sendCode,
      button.verifyCode {
				background: #000;
				color: #fff;
				border: 2px solid transparent;
      }

			button#continue:hover,
      button.editButton:hover,
      button.verifyButton:hover,
      button.sendButton:hover,
      button.sendCode:hover,
      button.verifyCode:hover,
			#continue:hover,
			.editButton:hover,
			.verifyButton:hover,
			.sendButton:hover {
				background: #333;
      }

			button#continue:active,
      button.editButton:active,
      button.verifyButton:active,
      button.sendButton:active,
      button.sendCode:active,
      button.verifyCode:active {
				background: #333;
				border: 2px solid #000;
      }

      button.sendCode,
      button.verifyCode,
      button.changeClaims {
        margin-top: 44px;
      }
      button#continue,
      button.sendNewCode {
        margin-top: 16px;
      }

			button.changeClaims,
      button.sendNewCode {
				background: #F2F2F2;
      }

			button.changeClaims:hover,
      button.sendNewCode:hover {
				color: #333;
				background: #D9D9D9;
				border: 1px solid #000;
      }
			button.changeClaims:active,
      button.sendNewCode:active {
				color: #000;
				background: #D9D9D9;
				border: 2px solid #000;
				padding: 15px 27px;
      }

      #continue:active,
      .editButton:active,
      .verifyButton:active,
      .sendButton:active {
        border: 2px solid #000;
      }
      #continue:disabled,
      .editButton:disabled,
      .verifyButton:disabled,
      .sendButton:disabled {
        background-color: #e5e5e5;
        border: solid 1px transparent;
        color: #666;
      }
      #email_ver_but_resend,
      #cancel {
        background-color: #fff;
        border: solid 1px #333;
      }

      button#cancel {
        margin: 0 auto;
        margin-top: 44px;
        width: 149px;
      }

      #email_ver_but_resend:hover,
      #cancel:hover {
        background-color: #e5e5e5;
        border: solid 1px #000;
				color: #333;
      }
      #email_ver_but_resend:active,
      #cancel:active {
        border: 2px solid #000;
				padding: 15px 27px;
				color: #000;
      }
      #email_ver_but_resend:disabled,
      #cancel:disabled {
        color: #666;
        border: 1px solid #e5e5e5;
      }

      .buttons {
        display: flex;
        flex-direction: column;
      }

      .highlightError {
        border: 1px solid #b52626 !important;
      }

      #background_branding_container {
        display: none;
        width: 60%;
        overflow: hidden;
      }

      .attrEntry {
        display: flex;
        flex-direction: column;
      }

      .attrEntry .verify {
        order: 5;
      }

      /* input helper text */
      .attrEntry .itemLevel {
        color: #333;
        font-size: 14px;
        line-height: 1.43;
        order: 4;
      }
      /* input helper text visible */
      .attrEntry .show {
        margin-top: 8px;
      }

      .attrEntry.validate input:invalid {
        border: #b52626 solid 1px;
        background-color: #fff;
      }

      select {
        height: 56px;
        width: 100%;
        outline: none;
        border-radius: 2px;
        border: 1px solid #333;
        padding: 16px 20px;
        border-radius: 2px;
      }

      select:focus-visible {
        outline: #4b96fe 2px solid;
        outline-offset: 2px;
        border-radius: 2px;
      }

      select:hover {
        background-color: #e5e5e5;
        color: #000;
      }

      select:active {
        background-color: #e5e5e5;
        color: #000;
        border: 2px solid #000;
      }

      select:disabled {
        pointer-events: none;
        color: #666;
        background-color: #fff;
      }

      .CheckboxMultiSelect .attrEntry {
        display: grid;
        justify-items: start;
        grid-template-columns: 20px 1fr;
        column-gap: 12px;
        margin-top: 60px;
      }

      .CheckboxMultiSelect input {
        grid-column: 1 / 2;
      }

      .CheckboxMultiSelect label {
        grid-column: 2 / 3;
      }

      .CheckboxMultiSelect div {
        grid-column: 2 / 3;
        grid-row: 2/3;
      }

      #panel,
      .pageLevel,
      .panel li,
      label {
        display: block;
      }

      .panel li {
        list-style: none;
      }
      .panel {
        width: 160px;
        margin-bottom: 20px;
      }
      #api ul li {
        display: inline;
        list-style-type: none;
        margin-left: 0;
      }

      *,
      .panel,
      body {
        margin: 0;
        padding: 0;
      }
      body {
        z-index: -999;
      }
      .panel,
      .panel_layout,
      .panel_layout_row,
      body,
      html {
        height: 100%;
      }
      #panel {
        border-left: 1px solid #fff;
      }
      img {
        border: 0;
      }
      form {
        height: auto;
        width: auto;
      }

      .pageLevel {
        height: auto;
      }

      img#background_background_image {
        height: 100%;
        width: 100%;
      }
      div#background_page_overlay {
        background: left top no-repeat fixed #f7f7f7;
        height: 100%;
        opacity: 1;
      }
      .panel {
        background: #fff;
        float: right;
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        position: fixed;
        right: 0;
        width: 500px;
        z-index: 1;
      }
      .accountButton,
      .accountButton:hover {
        background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAAh1BMVEX///9QUFBOTk5LS0tERERCQkI/Pz9ISEg6OjpGRkZNTU08PDyAgID09PSlpaWWlpZxcXFgYGBZWVlUVFT6+vrx8fHt7e3s7Ozo6Oji4uLJycnGxsa4uLiqqqqgoKCNjY2JiYmGhoZra2tmZmb7+/vu7u7d3d3U1NTNzc2+vr67u7usrKx7e3vprNQnAAAA8klEQVQ4y63Q127DMAxAUZpDwyMeSdqsNqu7/f/va6zahgGJKAr0vgk6DyQh+6V/BiTOOeNRA9zuAWBdM6WBlPDTvaUUoAuMrT0mgNvA1IJjQB3MKjACvp6DK0WAH+agtH8H9jQHLUUgz7Uhx8xOXzNESxirLCYA2mw8tacI5FyIYXq8A9ge2Qs6oTnw2e2ruho2rjBcXJ4ADh3jBOQLQnVhRFx2gNDZ4ACogbHXj/ft9Dj5AcgbJFu5AThQWuYBIGmgtAFQo4EFB+CPGthJAPypgY3BHsheA5UNwLyAvsYNoDyroKUe4EoFTQ/yDtTONvsGUJ8KTUYyH+UAAAAASUVORK5CYII=);
        background-repeat: no-repeat;
      }

      #panel_center,
      #panel_left,
      #panel_right {
        display: inline-block;
        border: 0;
        height: 100%;
        margin: 0;
      }
      #panel_left {
        padding: 0;
        width: 50px;
      }
      #panel_center {
        min-height: 100%;
        padding: 0;
        width: 378px;
      }
      .hide {
        opacity: 0;
      }

      .working {
        background: url(data:image/gif;base64,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)
          no-repeat;
        height: 10px;
        width: auto;
      }

      #panel {
        width: 100%;
      }
      #panel_layout_row {
        width: 100%;
      }
      #panel_left {
        display: none;
      }
      #panel_center {
        width: 100%;
      }
      .panel_layout {
        width: 100%;
      }

      .div.buttons.verify label {
        display: none !important;
      }

      .tiny {
        display: none;
      }

      .intro {
        margin-top: 12px;
      }

      .attrEntry.validate .error.itemLevel.show,
      .attrEntry.validate .helpText.show,
      .helpText.show {
        display: block;
      }

      .attrEntry .helpText {
        display: none;
        padding-bottom: 10px;
        padding-top: 10px;
        color: #a61e0c;
        font-size: 0.9em;
      }

      .attrEntry .day {
        width: 100px;
        margin-right: 10px;
      }
      .attrEntry .month {
        width: 120px;
        margin-right: 10px;
      }
      .attrEntry .year {
        width: 115px;
        margin-right: 10px;
      }

      .errorText {
        padding: 20px;
        width: 310px;
      }
      .alert-modal,
      .verifying-modal {
        display: none;
      }
      .verifying-modal {
        color: #fff;
        font-size: 24px;
        display: none;
      }

      @media (min-width: 768px) {
        .inner_container {
          width: 41.5%;
          margin-top: 36px;
          gap: 36px;
        }
      }

      @media (min-width: 1200px) {
        .inner_container {
          width: 509px;
          gap: 44px;
        }
      }

      @media (min-width: 1920px) {
        .inner_container {
          width: 509px;
        }

        #background_branding_container {
          display: block;
        }

        .outer-container {
          display: flex;
          justify-content: space-between;
          height: 100vh;
          width: 100%;
        }
      }

      div[aria-hidden='false'] + input,
      div[aria-hidden='false'] + select {
        border: 1px solid #b52626;
      }

      div[aria-hidden='false'] ~ label#true_option,
      div[aria-hidden='false'] ~ label#true_option a:visited,
      div[aria-hidden='false'] ~ label#true_option a:link {
        color: #b52626;
      }

      /* div.validate #email_ver_input {
        border: 1px solid #b52626;
      } */
    </style>
  </head>
  <body>
    <div class="outer-container">
      <div class="inner_container">
        <img alt="Company Logo" class="companyLogo" style="display: none" data-tenant-branding-logo="true" />
        <div id="api" role="main"></div>
      </div>
      <div id="background_branding_container" data-tenant-branding-background-color="true">
        <img
          id="background_background_image"
          data-tenant-branding-background="true"
          src="https://login.microsoftonline.com/static/tenant/default/img/default_signin_illustration_optimized.png"
          alt="Illustration"
          aria-hidden="true"
        />
      </div>
    </div>
    <script>
      'use strict';
      $(document).ready(function () {
        if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
          var t = document.createElement('style');
          t.appendChild(document.createTextNode('@-ms-viewport{width:auto!important}')),
            t.appendChild(document.createTextNode('@-ms-viewport{height:auto!important}')),
            document.getElementsByTagName('head')[0].appendChild(t);
        }
        if (navigator.userAgent.match(/MSIE 10/i)) {
          var e = $('#footer_links_container');
          $(e).css('padding-top', '100px');
        }
        var o,
          i = $('#background_background_image'),
          n = function () {
            (document.body.style.overflow = 'hidden'),
              ($(window).width() - 500) / $(window).height() < o
                ? (i.height($(window).height()), i.width('auto'))
                : (i.width($(window).width() - 500), i.height('auto')),
              (document.body.style.overflow = '');
          };
        $('<img>')
          .attr('src', i.attr('src'))
          .on('load', function () {
            (o = this.width / this.height), n();
          }),
          $(window).resize(function () {
            n();
          });
      });

      //CHANGE PLACEHOLDER
      function changePlaceholder(id) {
        let input = document.getElementById(id);

        if (!input) {
          return;
        }
        input.placeholder = input.title;
      }

      changePlaceholder('givenName');
      changePlaceholder('surname');
      changePlaceholder('extension_company');
      changePlaceholder('extension_phone');
      changePlaceholder('newPassword');
      changePlaceholder('reenterPassword');
      changePlaceholder('email');

      //ADD ASTERISK
      function addAsterisk(selector) {
        let label = document.querySelector(selector);
        if (!label) {
          return;
        }
        label.innerHTML = label.innerHTML + '<span class="asterisk">*</span>';
      }

      addAsterisk('#extension_crmLeadGenderCodeId_label');
      addAsterisk('#givenName_label');
      addAsterisk('#surname_label');
      addAsterisk('#extension_company_label');
      addAsterisk('#extension_crmLeadTargetGroupId_label');
      addAsterisk('#extension_phone_label');
      addAsterisk('#newPassword_label');
      addAsterisk('#reenterPassword_label');
      addAsterisk('#country_label');
      addAsterisk('#api label[for="extension_agreeTerms_true"]');
      addAsterisk('#email_ver_input_label');

      //CHANGE SELECT OPTION
      function changeOption(selectSelector, anchorSelector) {
        let select = document.querySelector(selectSelector);
        if (!select) {
          return;
        }
        select.options[0].innerHTML = document.querySelector(anchorSelector).dataset.help;
      }
      changeOption('#country', '.country_li .helpLink');
      changeOption('#extension_crmLeadTargetGroupId', '.extension_crmLeadTargetGroupId_li .helpLink');
      changeOption('#extension_crmLeadGenderCodeId', '.extension_crmLeadGenderCodeId_li .helpLink');

      //CHANGE TERMS AND CONDITIONS
      (function addtermsOfUseLink() {
        let termsOfUseLabel = document.querySelector('#api label[for="extension_agreeTerms_true"]');
        if (!termsOfUseLabel) {
          return;
        }

        const replaceWith = {
          '{Nutzungsbedingungen}':
            '<a href="https://www.dornbracht.com/de-de/allgemeine-nutzungsbedingungen" target="_blank">Nutzungsbedingungen</a>',
          '{Datenschutzbestimmungen}':
            '<a href="https://www.dornbracht.com/de-de/datenschutzbestimmungen" target="_blank">Datenschutzbestimmungen</a>',
          '{terms and conditions}': '<a href="https://www.dornbracht.com/en/terms-and-conditions" target="_blank">terms and conditions</a>',
          '{data protection policy}': '<a href="https://www.dornbracht.com/en/privacy-policy" target="_blank">data protection policy</a>',
          '{conditions d’utilisation}':
            '<a href="https://www.dornbracht.com/fr-fr/conditions-generales-utilisation" target="_blank">conditions d’utilisation</a>',
          '{politique de confidentialité}':
            '<a href="https://www.dornbracht.com/fr-fr/declaration-de-confidentialite" target="_blank">politique de confidentialité</a>',
          '{condizioni di utilizzo}':
            '<a href="https://www.dornbracht.com/it-it/condizioni-generali-di-utilizzo" target="_blank">condizioni di utilizzo</a>',
          '{disposizioni sulla privacy}':
            '<a href="https://www.dornbracht.com/it-it/dichiarazione-di-protezione-dei-dati" target="_blank">disposizioni sulla privacy</a>',
          '{condiciones de uso}':
            '<a href="https://www.dornbracht.com/es-es/condiciones-generales-de-uso" target="_blank">condiciones de uso</a>',
          '{disposiciones de protección de datos}':
            '<a href="https://www.dornbracht.com/es-es/politica-de-privacidad" target="_blank">disposiciones de protección de datos</a>'
        };

        termsOfUseLabel.innerHTML = termsOfUseLabel.innerHTML.replace(
          /{Nutzungsbedingungen}|{Datenschutzbestimmungen}|{terms and conditions}|{data protection policy}|{conditions d’utilisation}|{politique de confidentialité}|{condizioni di utilizzo}|{disposizioni sulla privacy}|{condiciones de uso}|{disposiciones de protección de datos}/g,
          function (char) {
            return replaceWith[char];
          }
        );
      })();

      //CHANGE ASTERISK
      let emailHelpertext = document.querySelector('.email_li .attrEntry .itemLevel');
      let emailInput = document.querySelector('#email');
      let emailLabel = document.querySelector('#email_label');
      let emailLabelText;
      if (emailLabel) {
        emailLabelText = emailLabel.innerHTML;
      }

      let observer = new MutationObserver(function (mutations) {
        for (let mutation of mutations) {
          if (mutation.type === 'attributes') {
            if (emailHelpertext.getAttribute('aria-hidden') == 'false') {
              emailInput.style.border = '1px solid #b52626';
              emailLabel.innerHTML = emailLabelText + '<span style="color: #b52626; padding-left: 3px;">*</span>';
            } else {
              emailInput.style.border = '1px solid #333';
              emailLabel.innerHTML = emailLabelText;
            }
          }
        }
      });

      if (emailHelpertext) {
        observer.observe(emailHelpertext, { attributes: true });
      }

      function changeAsteriskColor(targetSelector, asteriskSelector) {
        const target = document.querySelector(targetSelector);
        let asterisk = document.querySelector(asteriskSelector);

        if (!target) {
          return;
        }

        const observer = new MutationObserver(function (mutations) {
          for (let mutation of mutations) {
            if (mutation.type === 'attributes') {
              if (target.getAttribute('aria-hidden') == 'false') {
                asterisk.style.color = '#b52626';
              } else {
                asterisk.style.color = '#333';
              }
            }
          }
        });
        observer.observe(target, { attributes: true });
      }

      changeAsteriskColor('.extension_crmLeadGenderCodeId_li .attrEntry .itemLevel', '#extension_crmLeadGenderCodeId_label .asterisk');
      changeAsteriskColor('.givenName_li .attrEntry .itemLevel', '#givenName_label .asterisk');
      changeAsteriskColor('.surname_li .attrEntry .itemLevel', '#surname_label .asterisk');
      changeAsteriskColor('.extension_company_li .attrEntry .itemLevel', '#extension_company_label .asterisk');
      changeAsteriskColor('.extension_crmLeadTargetGroupId_li .attrEntry .itemLevel', '#extension_crmLeadTargetGroupId_label .asterisk');
      changeAsteriskColor('.extension_phone_li .attrEntry .itemLevel', '#extension_phone_label .asterisk');
      changeAsteriskColor('.newPassword_li .attrEntry .itemLevel', '#newPassword_label .asterisk');
      changeAsteriskColor('.reenterPassword_li .attrEntry .itemLevel', '#reenterPassword_label .asterisk');
      changeAsteriskColor('.country_li .attrEntry .itemLevel', '#country_label .asterisk');

      function changeAsteriskColor2(targetSelector, asteriskSelector, inputSelektor) {
        const target = document.querySelector(targetSelector);
        let asterisk = document.querySelector(asteriskSelector);
        let input = document.querySelector(inputSelektor);

        if (!target) {
          return;
        }

        const observer = new MutationObserver(function (mutations) {
          for (let mutation of mutations) {
            if (mutation.type === 'attributes') {
              if (target.getAttribute('aria-hidden') == 'false') {
                asterisk.style.color = '#b52626';
                input.style.border = '1px solid #b52626';
              } else {
                if (asterisk) {
                  asterisk.style.color = '#333';
                }
                if (input) {
                  input.style.border = '1px solid #333';
                }
              }
            }
          }
        });
        observer.observe(target, { attributes: true });
      }
      changeAsteriskColor2('.email_li .attrEntry div .verificationErrorText', '#email_ver_input_label .asterisk', '#email_ver_input');
      changeAsteriskColor2('#claimVerificationServerError', '#email_ver_input_label .asterisk', '#email_ver_input');
      // HIDE BLOCK INTRO
      const emailVerificationControl = document.querySelector('#emailVerificationControl');
      const introBlock = document.querySelector('.intro');

      if (emailVerificationControl) {
        introBlock.style.display = 'none';
      }

      // CHANGE MARGIN-TOP ON THE REGISTER/CONTINUE BUTTON
      const continueButton = document.querySelector('#continue');
      if (!emailVerificationControl) {
        continueButton.style.marginTop = '44px';
      }

      // HIDE BUTTON CONTINUE
      (function () {
        const continueButton = document.querySelector('#continue');
        const changeEmailButton = document.querySelector('.changeClaims');

        if (!changeEmailButton) {
          return;
        }

        const observer = new MutationObserver(function (mutations) {
          for (let mutation of mutations) {
            if (mutation.type === 'attributes') {
              if (changeEmailButton.getAttribute('aria-hidden') == 'true') {
                continueButton.style.display = 'none';
              } else {
                continueButton.style.display = 'block';
              }
            }
          }
        });
        observer.observe(changeEmailButton, { attributes: true });
      })();
      // ADD MARGIN-TOP TO VERIFICATION MESSAGE DIVS
      function hideVerfificationInfoDiv(targetSelector, divSelector) {
        const target = document.querySelector(targetSelector);
        let div = document.querySelector(divSelector);

        if (!target) {
          return;
        }

        const observer = new MutationObserver(function (mutations) {
          for (let mutation of mutations) {
            if (mutation.type === 'attributes') {
              if (target.getAttribute('aria-hidden') == 'false') {
                div.style.marginTop = '12px';
                target.style.display = 'block';
              } else {
                div.style.marginTop = '0px';
              }
            }
          }
        });
        observer.observe(target, { attributes: true });
      }

      hideVerfificationInfoDiv('#emailVerificationControl_success_message', '.verificationSuccessText');
      hideVerfificationInfoDiv('#emailVerificationControl_error_message', '.verificationErrorText');
    </script>
  </body>
</html>
