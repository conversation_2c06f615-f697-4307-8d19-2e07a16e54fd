<TrustFrameworkPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" PolicySchemaVersion="0.3.0.0" TenantId="identityb2c0pd50.onmicrosoft.com" PolicyId="B2C_1A_DisplayControl_TrustFrameworkExtensions" PublicPolicyUri="http://identityb2c0pd50.onmicrosoft.com/B2C_1A_DisplayControl_TrustFrameworkExtensions">

  <BasePolicy>
    <TenantId>identityb2c0pd50.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_TrustFrameworkExtensions</PolicyId>
  </BasePolicy>

  <!--###
    For Custom OTP Email Verification Display Control via Mailjet Transactional Template:
    => https://github.com/azure-ad-b2c/samples/tree/master/policies/custom-email-verifcation-displaycontrol
   ###-->

  <BuildingBlocks>
    <ClaimsSchema>
      <ClaimType Id="objectIdNotFound">
        <DisplayName>Used for comparison</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="Otp">
        <DisplayName>Secondary One-time password</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="emailRequestBody">
        <DisplayName>Email request body</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="VerificationCode">
        <DisplayName>Verification Code</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Enter your email verification code</UserHelpText>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="subject">
        <DisplayName>Email subject</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="heading">
        <DisplayName>Email heading</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="message">
        <DisplayName>Email message</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="codeIntro">
        <DisplayName>Email code introduction</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="greeting">
        <DisplayName>Email greeting</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="signature">
        <DisplayName>Email signature</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
    </ClaimsSchema>

    <ClaimsTransformations>
      <ClaimsTransformation Id="AssertObjectIdObjectIdNotFoundAreEqual" TransformationMethod="AssertStringClaimsAreEqual">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="objectId" TransformationClaimType="inputClaim1" />
          <InputClaim ClaimTypeReferenceId="objectIdNotFound" TransformationClaimType="inputClaim2" />
        </InputClaims>
        <InputParameters>
          <InputParameter Id="stringComparison" DataType="string" Value="ordinalIgnoreCase" />
        </InputParameters>
      </ClaimsTransformation>
      <ClaimsTransformation Id="GetLocalizedStringsForEmail" TransformationMethod="GetLocalizedStringsTransformation">
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="subject" TransformationClaimType="email_subject" />
          <OutputClaim ClaimTypeReferenceId="heading" TransformationClaimType="email_heading" />
          <OutputClaim ClaimTypeReferenceId="message" TransformationClaimType="email_message" />
          <OutputClaim ClaimTypeReferenceId="codeIntro" TransformationClaimType="email_code" />
          <OutputClaim ClaimTypeReferenceId="greeting" TransformationClaimType="email_greeting" />
          <OutputClaim ClaimTypeReferenceId="signature" TransformationClaimType="email_signature" />
        </OutputClaims>
      </ClaimsTransformation>
      <ClaimsTransformation Id="GenerateEmailRequestBody" TransformationMethod="GenerateJson">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="email" TransformationClaimType="Messages.0.To.0.Email" />
          <InputClaim ClaimTypeReferenceId="subject" TransformationClaimType="Messages.0.Subject" />
          <InputClaim ClaimTypeReferenceId="otp" TransformationClaimType="Messages.0.Variables.otp" />
          <InputClaim ClaimTypeReferenceId="email" TransformationClaimType="Messages.0.Variables.email" />
          <InputClaim ClaimTypeReferenceId="heading" TransformationClaimType="Messages.0.Variables.otpheading" />
          <InputClaim ClaimTypeReferenceId="message" TransformationClaimType="Messages.0.Variables.otpmessage" />
          <InputClaim ClaimTypeReferenceId="codeIntro" TransformationClaimType="Messages.0.Variables.otpcodeIntro" />
          <InputClaim ClaimTypeReferenceId="greeting" TransformationClaimType="Messages.0.Variables.otpgreeting" />
          <InputClaim ClaimTypeReferenceId="signature" TransformationClaimType="Messages.0.Variables.otpsignature" />
        </InputClaims>
        <InputParameters>
          <!-- Update the template_id value with the ID of your Mailjet template. -->
          <InputParameter Id="Messages.0.TemplateID" DataType="int" Value="4536411"/>
          <InputParameter Id="Messages.0.TemplateLanguage" DataType="boolean" Value="true"/>
          <!-- Update with an email appropriate for your organization. -->
          <InputParameter Id="Messages.0.From.Email" DataType="string" Value="<EMAIL>"/>
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="emailRequestBody" TransformationClaimType="outputClaim"/>
        </OutputClaims>
      </ClaimsTransformation>
    </ClaimsTransformations>

    <ContentDefinitions>
      <ContentDefinition Id="api.localaccountsignup">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.14</DataUri>
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.custom-email.en" />
          <LocalizedResourcesReference Language="es" LocalizedResourcesReferenceId="api.custom-email.es" />
          <LocalizedResourcesReference Language="de" LocalizedResourcesReferenceId="api.custom-email.de" />
          <LocalizedResourcesReference Language="fr" LocalizedResourcesReferenceId="api.custom-email.fr" />
          <LocalizedResourcesReference Language="it" LocalizedResourcesReferenceId="api.custom-email.it" />
        </LocalizedResourcesReferences>
      </ContentDefinition>
      <ContentDefinition Id="api.localaccountpasswordreset">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.14</DataUri>
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.custom-email.en" />
          <LocalizedResourcesReference Language="es" LocalizedResourcesReferenceId="api.custom-email.es" />
          <LocalizedResourcesReference Language="de" LocalizedResourcesReferenceId="api.custom-email.de" />
          <LocalizedResourcesReference Language="fr" LocalizedResourcesReferenceId="api.custom-email.fr" />
          <LocalizedResourcesReference Language="it" LocalizedResourcesReferenceId="api.custom-email.it" />
        </LocalizedResourcesReferences>
      </ContentDefinition>
    </ContentDefinitions>

    <!--
      ###
      See Verification display control user interface elements
      https://learn.microsoft.com/en-us/azure/active-directory-b2c/localization-string-ids#verification-display-control-user-interface-elements
      ###
    -->
    <Localization Enabled="true">
      <SupportedLanguages DefaultLanguage="en" MergeBehavior="ReplaceAll">
        <SupportedLanguage>en</SupportedLanguage>
        <SupportedLanguage>es</SupportedLanguage>
        <SupportedLanguage>de</SupportedLanguage>
        <SupportedLanguage>fr</SupportedLanguage>
        <SupportedLanguage>it</SupportedLanguage>
      </SupportedLanguages>
      <LocalizedResources Id="api.custom-email.en">
        <LocalizedStrings>
          <!--Email template parameters-->
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_subject">Your Code</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_heading">Check your email address</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_message">Thank you for verifying your account</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_code">Your code is</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_greeting">With kind regards</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_signature">Your Dornbracht Team</LocalizedString>
          <!-- Display control Sign Up UI elements-->
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="intro_msg">Before you can get started, verification is necessary.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="success_send_code_msg">Verification code has been sent to your inbox. Please copy it to the input box below.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="failure_send_code_msg">We are having trouble verifying your email address. Please enter a valid email address and try again.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="success_verify_code_msg">Email address verified. You can now continue.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="failure_verify_code_msg">We are having trouble verifying your email address. Please try again.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_send_code">Send verification code</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_verify_code">Verify code</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_send_new_code">Send new code</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_change_claims">Change email</LocalizedString>
          <!-- Display control Password Reset UI elements-->
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="intro_msg">Before you can get started, verification is necessary.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="success_send_code_msg">Verification code has been sent to your inbox. Please copy it to the input box below.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="failure_send_code_msg">We are having trouble verifying your email address. Please enter a valid email address and try again.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="success_verify_code_msg">Email address verified. You can now continue.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="failure_verify_code_msg">We are having trouble verifying your email address. Please try again.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_send_code">Send verification code</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_verify_code">Verify code</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_send_new_code">Send new code</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_change_claims">Change email</LocalizedString>
          <!-- Claims-->
          <LocalizedString ElementType="ClaimType" ElementId="verificationCode" StringId="DisplayName">Verification Code</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="verificationCode" StringId="UserHelpText">Enter your email verification code.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="DisplayName">Email address</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="UserHelpText">Enter email address</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="PatternHelpText">Please enter a valid email address.</LocalizedString>
          <!-- OTP Email validation error messages => https://learn.microsoft.com/en-us/azure/active-directory-b2c/localization-string-ids#one-time-password-error-messages-->
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfSessionDoesNotExist">That code is expired. Please request a new code.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMaxRetryAttempted">You've made too many incorrect attempts. Please try again later.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidCode">That code is incorrect. Please try again.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfVerificationFailedRetryAllowed">The verification has failed, please try again.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfSessionConflict">Cannot verify the code, please try again later.</LocalizedString>
          <!--Custom Error Message: Send verification code only if email is not yet registered -->
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsTransformationStringsAreNotEqual">A user with the specified ID already exists. Please choose a different one.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <LocalizedResources Id="api.custom-email.de">
        <LocalizedStrings>
          <!--Email template parameters-->
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_subject">Ihr Code</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_heading">Überprüfen Sie Ihre E-Mail-Adresse</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_message">Vielen Dank für das Überprüfen Ihres Kontos</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_code">Ihr Code lautet</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_greeting">Mit freundlichen Grüßen</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_signature">Ihr Dornbracht Team</LocalizedString>
          <!-- Display control Sign Up UI elements-->
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="intro_msg">Bevor es losgehen kann, ist eine Verifizierung erforderlich.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="success_send_code_msg">Der Verifizierungscode wurde an Ihr Postfach gesendet. Kopieren Sie den Code in das nachstehende Eingabefeld.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="failure_send_code_msg">Wir haben Probleme bei der Verifizierung Ihrer E-Mail-Adresse. Bitte geben Sie eine gültige E-Mail-Adresse ein und versuchen Sie es erneut.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="success_verify_code_msg">Die E-Mail-Adresse wurde verifiziert. Sie können den Vorgang jetzt fortsetzen.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="failure_verify_code_msg">Wir haben Probleme bei der Verifizierung Ihrer E-Mail-Adresse. Bitte versuchen Sie es erneut.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_send_code">Verifizierungscode senden</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_verify_code">Code überprüfen</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_send_new_code">Neuen Code senden</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_change_claims">E-Mail-Adresse ändern</LocalizedString>
          <!-- Display control Password Reset UI elements-->
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="intro_msg">Bevor es losgehen kann, ist eine Verifizierung erforderlich.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="success_send_code_msg">Der Verifizierungscode wurde an Ihr Postfach gesendet. Kopieren Sie den Code in das nachstehende Eingabefeld.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="failure_send_code_msg">Wir haben Probleme bei der Verifizierung Ihrer E-Mail-Adresse. Bitte geben Sie eine gültige E-Mail-Adresse ein und versuchen Sie es erneut.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="success_verify_code_msg">Die E-Mail-Adresse wurde verifiziert. Sie können den Vorgang jetzt fortsetzen.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="failure_verify_code_msg">Wir haben Probleme bei der Verifizierung Ihrer E-Mail-Adresse. Bitte versuchen Sie es erneut.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_send_code">Verifizierungscode senden</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_verify_code">Code überprüfen</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_send_new_code">Neuen Code senden</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_change_claims">E-Mail-Adresse ändern</LocalizedString>
          <!-- Claims-->
          <LocalizedString ElementType="ClaimType" ElementId="verificationCode" StringId="DisplayName">Prüfcode</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="verificationCode" StringId="UserHelpText">Prüfcode eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="DisplayName">E-Mail-Adresse</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="UserHelpText">E-Mail-Adresse eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="PatternHelpText">Bitte geben Sie eine gültige E-Mail-Adresse ein.</LocalizedString>
          <!-- OTP Email validation error messages => https://learn.microsoft.com/en-us/azure/active-directory-b2c/localization-string-ids#one-time-password-error-messages-->
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfSessionDoesNotExist">Dieser Code ist abgelaufen. Fordern Sie einen neuen Code an.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMaxRetryAttempted">Es wurden zu viele ungültige Versuche durchgeführt. Versuchen Sie es später noch mal.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidCode">Dieser Code ist falsch. Wiederholen Sie den Vorgang.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfVerificationFailedRetryAllowed">Fehler bei der Überprüfung. Versuchen Sie es noch einmal.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfSessionConflict">Der Code konnte nicht verifiziert werden, bitte versuchen Sie es später noch einmal.</LocalizedString>
          <!--Custom Error Message: Send verification code only if email is not yet registered -->
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsTransformationStringsAreNotEqual">Es ist bereits ein Benutzer mit der angegebenen ID vorhanden. Wählen Sie eine andere ID.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <LocalizedResources Id="api.custom-email.es">
        <LocalizedStrings>
          <!--Email template parameters-->
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_subject">Su código</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_heading">Compruebe su dirección de correo electrónico</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_message">Gracias por verificar su cuenta</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_code">Su código es</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_greeting">Saludos cordiales</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_signature">Su equipo Dornbracht</LocalizedString>
          <!-- Display control Sign Up UI elements-->
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="intro_msg">Se requiere verificación antes de poder empezar.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="success_send_code_msg">El código de verificación ha sido enviado a su buzón. Copie el código en el campo de entrada de abajo.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="failure_send_code_msg">Tenemos problemas para comprobar la dirección de correo electrónico. Escriba una dirección de correo electrónico válida y vuelva a intentarlo.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="success_verify_code_msg">Dirección de correo electrónico comprobada. Puede continuar.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="failure_verify_code_msg">Tenemos problemas para verificar su dirección de correo electrónico. Por favor, inténtelo de nuevo.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_send_code">Enviar código de verificación</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_verify_code">Verificar código</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_send_new_code">Enviar nuevo código</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_change_claims">Cambiar correo electrónico</LocalizedString>
          <!-- Display control Password Reset UI elements-->
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="intro_msg">Se requiere verificación antes de poder empezar.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="success_send_code_msg">El código de verificación ha sido enviado a su buzón. Copie el código en el campo de entrada de abajo.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="failure_send_code_msg">Tenemos problemas para comprobar la dirección de correo electrónico. Escriba una dirección de correo electrónico válida y vuelva a intentarlo.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="success_verify_code_msg">Dirección de correo electrónico comprobada. Puede continuar.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="failure_verify_code_msg">Tenemos problemas para verificar su dirección de correo electrónico. Por favor, inténtelo de nuevo.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_send_code">Enviar código de verificación</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_verify_code">Verificar código</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_send_new_code">Enviar nuevo código</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_change_claims">Cambiar correo electrónico</LocalizedString>
          <!-- Claims-->
          <LocalizedString ElementType="ClaimType" ElementId="verificationCode" StringId="DisplayName">Código de verificación</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="verificationCode" StringId="UserHelpText">Introduzca el código de verificación</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="DisplayName">Dirección de correo electrónico</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="UserHelpText">Introduzca la dirección de correo electrónico</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="PatternHelpText">Introduzca una dirección de correo electrónico válida.</LocalizedString>
          <!-- OTP Email validation error messages => https://learn.microsoft.com/en-us/azure/active-directory-b2c/localization-string-ids#one-time-password-error-messages-->
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfSessionDoesNotExist">El código ha expirado. Solicite otro nuevo.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMaxRetryAttempted">Ha realizado demasiados intentos incorrectos. Vuelva a intentarlo más tarde.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidCode">Ese código es incorrecto. Inténtelo de nuevo.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfVerificationFailedRetryAllowed">Error de comprobación. Vuelva a intentarlo.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfSessionConflict">No se puede verificar el código, por favor inténtelo más tarde.</LocalizedString>
          <!--Custom Error Message: Send verification code only if email is not yet registered -->
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsTransformationStringsAreNotEqual">Ya existe un usuario con el id. especificado. Elija otro diferente.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <LocalizedResources Id="api.custom-email.fr">
        <LocalizedStrings>
          <!--Email template parameters-->
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_subject">Votre code</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_heading">Vérifiez votre adresse e-mail</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_message">Merci d'avoir vérifié votre compte</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_code">Votre code est</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_greeting">Avec nos meilleures salutations</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_signature">Votre équipe Dornbracht</LocalizedString>
          <!-- Display control Sign Up UI elements-->
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="intro_msg">Avant de pouvoir commencer, une vérification est nécessaire.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="success_send_code_msg">Le code de vérification a été envoyé à votre boîte de réception. Veuillez le copier dans la zone d’entrée ci-dessous.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="failure_send_code_msg">Impossible d’envoyer le code, réessayez plus tard.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="success_verify_code_msg">Adresse e-mail vérifiée. Vous pouvez maintenant continuer.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="failure_verify_code_msg">Nous rencontrons des problèmes de la vérification de votre adresse e-mail. Entrez une adresse e-mail valide et réessayez.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_send_code">Envoyer le code de vérification</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_verify_code">Vérifier le code</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_send_new_code">Envoyer le nouveau code</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_change_claims">Modifier l'adresse e-mail</LocalizedString>
          <!-- Display control Password Reset UI elements-->
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="intro_msg">Avant de pouvoir commencer, une vérification est nécessaire.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="success_send_code_msg">Le code de vérification a été envoyé à votre boîte de réception. Veuillez le copier dans la zone d’entrée ci-dessous.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="failure_send_code_msg">Impossible d’envoyer le code, réessayez plus tard.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="success_verify_code_msg">Adresse e-mail vérifiée. Vous pouvez maintenant continuer.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="failure_verify_code_msg">Nous rencontrons des problèmes de la vérification de votre adresse e-mail. Entrez une adresse e-mail valide et réessayez.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_send_code">Envoyer le code de vérification</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_verify_code">Vérifier le code</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_send_new_code">Envoyer le nouveau code</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_change_claims">Modifier l'adresse e-mail</LocalizedString>
          <!-- Claims-->
          <LocalizedString ElementType="ClaimType" ElementId="verificationCode" StringId="DisplayName">Code de vérification</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="verificationCode" StringId="UserHelpText">Saisir le code de contrôle</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="DisplayName">E-Mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="UserHelpText">Saisir l'adresse e-mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="PatternHelpText">Veuillez saisir une adresse électronique valide.</LocalizedString>
          <!-- OTP Email validation error messages => https://learn.microsoft.com/en-us/azure/active-directory-b2c/localization-string-ids#one-time-password-error-messages-->
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfSessionDoesNotExist">Le code a expiré. Veuillez demander un nouveau code.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMaxRetryAttempted">Il y a eu trop de demandes pour vérifier cette adresse e-mail. Veuillez patienter quelques instants, puis réessayez.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidCode">Ce code est incorrect. Réessayez.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfVerificationFailedRetryAllowed">La vérification a échoué. Réessayez.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfSessionConflict">Le code n'a pas pu être vérifié, veuillez réessayer plus tard.</LocalizedString>
          <!--Custom Error Message: Send verification code only if email is not yet registered -->
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsTransformationStringsAreNotEqual">Un utilisateur avec l’ID spécifié existe déjà. Choisissez-en un autre.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <LocalizedResources Id="api.custom-email.it">
        <LocalizedStrings>
          <!--Email template parameters-->
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_subject">Il codice</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_heading">Controllare l'indirizzo e-mail</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_message">Grazie per aver verificato il vostro account</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_code">Il codice è</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_greeting">Con i migliori saluti</LocalizedString>
          <LocalizedString ElementType="GetLocalizedStringsTransformationClaimType" StringId="email_signature">Il vostro team Dornbracht</LocalizedString>
          <!-- Display control Sign Up UI elements-->
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="intro_msg">Prima di iniziare, è necessaria una verifica.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="success_send_code_msg">Il codice di verifica è stato inviato alla Posta in arrivo. Copiarlo nella casella di input qui di seguito.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="failure_send_code_msg">Si sono verificati problemi nella verifica dell'indirizzo di posta elettronica. Immettere un indirizzo di posta elettronica valido e riprovare.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="success_verify_code_msg">Indirizzo di posta elettronica verificato. È ora possibile continuare.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="failure_verify_code_msg">Si sono verificati problemi nella verifica dell'indirizzo di posta elettronica. Riprova.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_send_code">Inviare il codice di verifica</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_verify_code">Verifica il codice</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_send_new_code">Inviare un nuovo codice</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationControl" StringId="but_change_claims">Cambiare l'indirizzo di posta elettronica</LocalizedString>
          <!-- Display control Password Reset UI elements-->
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="intro_msg">Prima di iniziare, è necessaria una verifica.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="success_send_code_msg">Il codice di verifica è stato inviato alla Posta in arrivo. Copiarlo nella casella di input qui di seguito.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="failure_send_code_msg">Si sono verificati problemi nella verifica dell'indirizzo di posta elettronica. Immettere un indirizzo di posta elettronica valido e riprovare.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="success_verify_code_msg">Indirizzo di posta elettronica verificato. È ora possibile continuare.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="failure_verify_code_msg">Si sono verificati problemi nella verifica dell'indirizzo di posta elettronica. Riprova.</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_send_code">Inviare il codice di verifica</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_verify_code">Verifica il codice</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_send_new_code">Inviare un nuovo codice</LocalizedString>
          <LocalizedString ElementType="DisplayControl" ElementId="emailVerificationSSPRControl" StringId="but_change_claims">Cambiare l'indirizzo di posta elettronica</LocalizedString>
          <!-- Claims-->
          <LocalizedString ElementType="ClaimType" ElementId="verificationCode" StringId="DisplayName">Codice di verifica</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="verificationCode" StringId="UserHelpText">Inserire il codice di verifica</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="DisplayName">E-Mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="UserHelpText">Inserire l'indirizzo e-mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="PatternHelpText">Inserire un indirizzo e-mail valido.</LocalizedString>
          <!-- OTP Email validation error messages => https://learn.microsoft.com/en-us/azure/active-directory-b2c/localization-string-ids#one-time-password-error-messages-->
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfSessionDoesNotExist">Il codice è scaduto. Please request a new code.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMaxRetryAttempted">Sono stati effettuati troppi tentativi non validi. Riprovare più tardi.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidCode">Il codice non è corretto. Riprovare.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfVerificationFailedRetryAllowed">La verifica non è riuscita, riprovare.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfSessionConflict">Non è stato possibile verificare il codice. Riprovare più tardi.</LocalizedString>
          <!--Custom Error Message: Send verification code only if email is not yet registered -->
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsTransformationStringsAreNotEqual">Un utente con il nome specificato esiste già. Sceglierne un altro.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
    </Localization>
    <DisplayControls>
      <DisplayControl Id="emailVerificationControl" UserInterfaceControlType="VerificationControl">
        <DisplayClaims>
          <DisplayClaim ClaimTypeReferenceId="email" Required="true" />
          <DisplayClaim ClaimTypeReferenceId="verificationCode" ControlClaimType="VerificationCode" Required="true" />
        </DisplayClaims>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="email" />
        </OutputClaims>
        <Actions>
          <Action Id="SendCode">
            <ValidationClaimsExchange>
              <ValidationClaimsExchangeTechnicalProfile TechnicalProfileReferenceId="AAD-UserReadUsingEmailAddress-RaiseIfExists" />
              <ValidationClaimsExchangeTechnicalProfile TechnicalProfileReferenceId="GenerateOtp" />
              <ValidationClaimsExchangeTechnicalProfile TechnicalProfileReferenceId="SendOtp">
                <Preconditions>
                  <Precondition Type="ClaimEquals" ExecuteActionsIf="false">
                    <Value>objectId</Value>
                    <Value>NOTFOUND</Value>
                    <Action>SkipThisValidationTechnicalProfile</Action>
                  </Precondition>
                </Preconditions>
              </ValidationClaimsExchangeTechnicalProfile>
            </ValidationClaimsExchange>
          </Action>
          <Action Id="VerifyCode">
            <ValidationClaimsExchange>
              <ValidationClaimsExchangeTechnicalProfile TechnicalProfileReferenceId="VerifyOtp" />
            </ValidationClaimsExchange>
          </Action>
        </Actions>
      </DisplayControl>
      <DisplayControl Id="emailVerificationSSPRControl" UserInterfaceControlType="VerificationControl">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="email" DefaultValue="{OIDC:LoginHint}" AlwaysUseDefaultValue="true" />
        </InputClaims>
        <DisplayClaims>
          <DisplayClaim ClaimTypeReferenceId="email" Required="true" />
          <DisplayClaim ClaimTypeReferenceId="verificationCode" ControlClaimType="VerificationCode" Required="true" />
        </DisplayClaims>
        <OutputClaims></OutputClaims>
        <Actions>
          <Action Id="SendCode">
            <ValidationClaimsExchange>
              <ValidationClaimsExchangeTechnicalProfile TechnicalProfileReferenceId="AAD-CheckEmailAddressExists" />
              <ValidationClaimsExchangeTechnicalProfile TechnicalProfileReferenceId="GenerateOtp" />
              <ValidationClaimsExchangeTechnicalProfile TechnicalProfileReferenceId="SendOtp">
                <Preconditions>
                  <Precondition Type="ClaimsExist" ExecuteActionsIf="false">
                    <Value>objectId</Value>
                    <Action>SkipThisValidationTechnicalProfile</Action>
                  </Precondition>
                </Preconditions>
              </ValidationClaimsExchangeTechnicalProfile>
            </ValidationClaimsExchange>
          </Action>
          <Action Id="VerifyCode">
            <ValidationClaimsExchange>
              <ValidationClaimsExchangeTechnicalProfile TechnicalProfileReferenceId="VerifyOtp" />
            </ValidationClaimsExchange>
          </Action>
        </Actions>
      </DisplayControl>
    </DisplayControls>
  </BuildingBlocks>

  <ClaimsProviders>
    <ClaimsProvider>
      <DisplayName>One time password technical profiles</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="GenerateOtp">
          <DisplayName>Generate one time password</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.OneTimePasswordProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="Operation">GenerateCode</Item>
            <Item Key="CodeExpirationInSeconds">600</Item>
            <Item Key="CodeLength">6</Item>
            <Item Key="CharacterSet">0-9</Item>
            <Item Key="NumRetryAttempts">5</Item>
            <Item Key="NumCodeGenerationAttempts">10</Item>
            <Item Key="ReuseSameCode">false</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="email" PartnerClaimType="identifier" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="otp" PartnerClaimType="otpGenerated" />
          </OutputClaims>
        </TechnicalProfile>
        <TechnicalProfile Id="VerifyOtp">
          <DisplayName>Verify one time password</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.OneTimePasswordProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="Operation">VerifyCode</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="email" PartnerClaimType="identifier" />
            <InputClaim ClaimTypeReferenceId="verificationCode" PartnerClaimType="otpToVerify" />
          </InputClaims>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>

    <ClaimsProvider>
      <DisplayName>RestfulProvider</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="SendOtp">
          <DisplayName>Use email API to send the code the the user</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.RestfulProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="ServiceUrl">https://api.mailjet.com/v3.1/send</Item>
            <Item Key="AuthenticationType">Basic</Item>
            <Item Key="SendClaimsIn">Body</Item>
            <Item Key="ClaimUsedForRequestPayload">emailRequestBody</Item>
          </Metadata>
          <CryptographicKeys>
            <Key Id="BasicAuthenticationUsername" StorageReferenceId="B2C_1A_MailjetApiKey" />
            <Key Id="BasicAuthenticationPassword" StorageReferenceId="B2C_1A_MailjetSecretKey" />
          </CryptographicKeys>
          <InputClaimsTransformations>
            <InputClaimsTransformation ReferenceId="GenerateEmailRequestBody" />
          </InputClaimsTransformations>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="emailRequestBody" />
          </InputClaims>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>

    <ClaimsProvider>
      <DisplayName>Local Account</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="LocalAccountDiscoveryUsingEmailAddress">
          <InputClaimsTransformations>
            <InputClaimsTransformation ReferenceId="GetLocalizedStringsForEmail" />
          </InputClaimsTransformations>
          <DisplayClaims>
            <DisplayClaim DisplayControlReferenceId="emailVerificationSSPRControl" />
          </DisplayClaims>
        </TechnicalProfile>
        <TechnicalProfile Id="AAD-CheckEmailAddressExists">
          <Metadata>
            <Item Key="Operation">Read</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalDoesNotExist">true</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="email" PartnerClaimType="signInNames.emailAddress" Required="true"/>
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="objectId"/>
          </OutputClaims>
          <IncludeTechnicalProfile ReferenceId="AAD-Common"/>
        </TechnicalProfile>
        <!-- Custom Workaround to check if user already exists via https://stackoverflow.com/questions/59867036/how-to-check-user-exists-in-ad-b2c-using-custom-policy -->
        <TechnicalProfile Id="AAD-UserReadUsingEmailAddress-RaiseIfExists">
          <Metadata>
            <Item Key="Operation">Read</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalDoesNotExist">false</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="email" PartnerClaimType="signInNames.emailAddress" Required="true" />
          </InputClaims>
          <OutputClaims>
            <!-- Required claims -->
            <OutputClaim ClaimTypeReferenceId="objectId" DefaultValue="NOTFOUND" />
            <OutputClaim ClaimTypeReferenceId="objectIdNotFound" DefaultValue="NOTFOUND" AlwaysUseDefaultValue="true" />
          </OutputClaims>
          <OutputClaimsTransformations>
            <OutputClaimsTransformation ReferenceId="AssertObjectIdObjectIdNotFoundAreEqual" />
          </OutputClaimsTransformations>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
  </ClaimsProviders>
</TrustFrameworkPolicy>