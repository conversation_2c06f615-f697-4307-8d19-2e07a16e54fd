<TrustFrameworkPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" PolicySchemaVersion="0.3.0.0" TenantId="identityb2c0dd50.onmicrosoft.com" PolicyId="B2C_1A_SignUp_SignIn_SplitEmailVerificationAndSignUp" PublicPolicyUri="http://identityb2c0dd50.onmicrosoft.com/B2C_1A_Demo_SignUp_SignIn_SplitEmailVerificationAndSignUp" DeploymentMode="Development" UserJourneyRecorderEndpoint="urn:journeyrecorder:applicationinsights">

  <BasePolicy>
    <TenantId>identityb2c0dd50.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_DisplayControl_TrustFrameworkExtensions</PolicyId>
  </BasePolicy>

  <!--###
    To Separate the Email Verification and User Registration into 2 separate screens via:
    https://github.com/azure-ad-b2c/samples/tree/master/policies/split-email-verification-and-signup
   ###-->

  <BuildingBlocks>
    <ClaimsSchema>
      <!-- Read only email address to present to the user-->
      <ClaimType Id="readonlyEmail">
        <DisplayName>E-mail Address</DisplayName>
        <DataType>string</DataType>
        <UserInputType>Readonly</UserInputType>
      </ClaimType>
      <ClaimType Id="displayName">
        <DisplayName>Autogenerated Displayname</DisplayName>
        <DataType>string</DataType>
        <UserInputType>Readonly</UserInputType>
      </ClaimType>
    </ClaimsSchema>
    <ClaimsTransformations>
      <ClaimsTransformation Id="CreateReadonlyEmailClaim" TransformationMethod="FormatStringClaim">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="email" TransformationClaimType="inputClaim" />
        </InputClaims>
        <InputParameters>
          <InputParameter Id="stringFormat" DataType="string" Value="{0}" />
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="readonlyEmail" TransformationClaimType="outputClaim" />
        </OutputClaims>
      </ClaimsTransformation>
      <ClaimsTransformation Id="TransformToUserHasSpecListAttr" TransformationMethod="FormatStringClaim">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="anonymousUserHasWatchList" TransformationClaimType="inputClaim"/>
        </InputClaims>
        <InputParameters>
          <InputParameter Id="stringFormat" DataType="string" Value="{0}" />
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="extension_userHasSpecList" TransformationClaimType="outputClaim" />
        </OutputClaims>
      </ClaimsTransformation>
    </ClaimsTransformations>
  </BuildingBlocks>

  <ClaimsProviders>
    <ClaimsProvider>
      <DisplayName>Email Verification</DisplayName>
      <TechnicalProfiles>
        <!--Email verification only-->
        <TechnicalProfile Id="EmailVerification">
          <DisplayName>Initiate Email Address Verification For Local Account</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="ContentDefinitionReferenceId">api.localaccountsignup</Item>
            <Item Key="language.button_continue">Continue</Item>
            <Item Key="UserMessageIfClaimsTransformationStringsAreNotEqual">A user with the specified email address already exists. Please choose a different one.</Item>
          </Metadata>
          <InputClaimsTransformations>
            <InputClaimsTransformation ReferenceId="GetLocalizedStringsForEmail" />
          </InputClaimsTransformations>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="email" />
          </InputClaims>
          <DisplayClaims>
            <DisplayClaim DisplayControlReferenceId="emailVerificationControl" />
          </DisplayClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="email" Required="true" />
          </OutputClaims>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>

    <!-- for DORSUP-175
          based on:     
          https://github.com/azure-ad-b2c/unit-tests/blob/main/claims-resolver/CR_OAuth-KV.xml 
    -->
    <ClaimsProvider>
      <DisplayName>Processing Custom Query Parameters</DisplayName>
      <TechnicalProfiles>
              
        <TechnicalProfile Id="GetCustomQueryParameters">
          <DisplayName>Technical profile to fill claims with custom query parameter values</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="ContentDefinitionReferenceId">api.selfasserted</Item>
            <Item Key="IncludeClaimResolvingInClaimsHandling">true</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="anonymousUserHasWatchList" AlwaysUseDefaultValue="true" DefaultValue="{OAUTH-KV:anon_user_has_watchlist}" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="anonymousUserHasWatchList" />
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
        </TechnicalProfile>

        <TechnicalProfile Id="Validate-userHasSpecList">
          <DisplayName>TP Validate userHasSpecList-Attribute</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.ClaimsTransformationProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="IncludeClaimResolvingInClaimsHandling">true</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="anonymousUserHasWatchList" AlwaysUseDefaultValue="true" DefaultValue="{OAUTH-KV:anon_user_has_watchlist}" />
            <InputClaim ClaimTypeReferenceId="extension_userHasSpecList" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="extension_userHasSpecList" />
          </OutputClaims>
          <OutputClaimsTransformations>
            <OutputClaimsTransformation ReferenceId="TransformToUserHasSpecListAttr" />
          </OutputClaimsTransformations>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
        </TechnicalProfile>

      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Local Account Sign Up</DisplayName>
      <TechnicalProfiles>
        <!--Sign-up self-asserted technical profile without Email verification-->
        <TechnicalProfile Id="LocalAccountSignUpWithReadOnlyEmail">
          <DisplayName>Email signup</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="IpAddressClaimReferenceId">IpAddress</Item>
            <Item Key="ContentDefinitionReferenceId">api.localaccountsignup</Item>
            <Item Key="language.button_continue">Register</Item>
            <!-- Remove sign-up email verification -->
            <Item Key="EnforceEmailVerification">False</Item>
          </Metadata>
          <InputClaimsTransformations>
            <InputClaimsTransformation ReferenceId="CreateReadonlyEmailClaim" />
          </InputClaimsTransformations>
          <InputClaims>
            <!-- Set input the ReadOnlyEmail claim type to prefilled the email address-->
            <InputClaim ClaimTypeReferenceId="readOnlyEmail" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="objectId" />
            <OutputClaim ClaimTypeReferenceId="executed-SelfAsserted-Input" DefaultValue="true" />
            <OutputClaim ClaimTypeReferenceId="authenticationSource" />
            <OutputClaim ClaimTypeReferenceId="newUser" />
            <OutputClaim ClaimTypeReferenceId="extension_crmLeadId"/>
            <OutputClaim ClaimTypeReferenceId="form_legend_personal_data" />
            <!-- Display the ReadOnlyEmail claim type (instead of email claim type)-->
            <OutputClaim ClaimTypeReferenceId="readOnlyEmail" Required="true" />
            <OutputClaim ClaimTypeReferenceId="extension_crmLeadGenderCodeId" Required="true" />
            <OutputClaim ClaimTypeReferenceId="givenName" Required="true" />
            <OutputClaim ClaimTypeReferenceId="surname" Required="true" />
            <OutputClaim ClaimTypeReferenceId="extension_company" Required="true"/>
            <OutputClaim ClaimTypeReferenceId="extension_crmLeadTargetGroupId" Required="true"/>
            <OutputClaim ClaimTypeReferenceId="form_legend_contact" />
            <OutputClaim ClaimTypeReferenceId="extension_phone" Required="true"/>
            <OutputClaim ClaimTypeReferenceId="newPassword" Required="true" />
            <OutputClaim ClaimTypeReferenceId="reenterPassword" Required="true" />
            <OutputClaim ClaimTypeReferenceId="country" Required="true"/>
            <OutputClaim ClaimTypeReferenceId="extension_agreeTerms" Required="true" />
          </OutputClaims>
          <ValidationTechnicalProfiles>
            <ValidationTechnicalProfile ReferenceId="AAD-CheckEmailAddressExists" ContinueOnError="true" />
            <ValidationTechnicalProfile ReferenceId="Validate-userHasSpecList" />
            <ValidationTechnicalProfile ReferenceId="REST-CrmConnector-Create-Lead">
              <Preconditions>
                <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
                  <Value>objectId</Value>
                  <Action>SkipThisValidationTechnicalProfile</Action>
                </Precondition>
              </Preconditions>
            </ValidationTechnicalProfile>
            <ValidationTechnicalProfile ReferenceId="Validate-DisplayName" />
            <ValidationTechnicalProfile ReferenceId="AAD-UserWriteUsingLogonEmail" />
          </ValidationTechnicalProfiles>
          <!-- Sample: Disable session management for sign-up page -->
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
  </ClaimsProviders>

  <UserJourneys>
    <UserJourney Id="SplitSignUp">
      <OrchestrationSteps>
        <OrchestrationStep Order="1" Type="CombinedSignInAndSignUp" ContentDefinitionReferenceId="api.signuporsignin">
          <ClaimsProviderSelections>
            <ClaimsProviderSelection ValidationClaimsExchangeId="LocalAccountSigninEmailExchange" />
          </ClaimsProviderSelections>
          <ClaimsExchanges>
            <ClaimsExchange Id="LocalAccountSigninEmailExchange" TechnicalProfileReferenceId="SelfAsserted-LocalAccountSignin-Email" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="2" Type="ClaimsExchange">
          <Preconditions>
            <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
              <Value>objectId</Value>
              <Action>SkipThisOrchestrationStep</Action>
            </Precondition>
          </Preconditions>
          <ClaimsExchanges>
            <ClaimsExchange Id="SignUpWithLogonEmailExchange" TechnicalProfileReferenceId="EmailVerification" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="3" Type="ClaimsExchange">
          <Preconditions>
            <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
              <Value>objectId</Value>
              <Action>SkipThisOrchestrationStep</Action>
            </Precondition>
          </Preconditions>
          <ClaimsExchanges>
            <ClaimsExchange Id="LocalAccountSignUpWithReadOnlyEmail" TechnicalProfileReferenceId="LocalAccountSignUpWithReadOnlyEmail" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="4" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="AADUserReadWithObjectId" TechnicalProfileReferenceId="AAD-UserReadUsingObjectId" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="5" Type="SendClaims" CpimIssuerTechnicalProfileReferenceId="JwtIssuer" />
      </OrchestrationSteps>
    </UserJourney>
  </UserJourneys>

  <RelyingParty>
    <DefaultUserJourney ReferenceId="SplitSignUp" />
    <UserJourneyBehaviors>
      <SingleSignOn Scope="Tenant" KeepAliveInDays="30" />
      <SessionExpiryType>Absolute</SessionExpiryType>
      <SessionExpiryInSeconds>900</SessionExpiryInSeconds>
      <ScriptExecution>Allow</ScriptExecution>
    </UserJourneyBehaviors>

    <!-- ### Use this Snippet to use Application Insights Monitoring for DEV Tenant ### -->
    <!--
    <UserJourneyBehaviors>
      <JourneyInsights TelemetryEngine="ApplicationInsights" InstrumentationKey="7ba67fbc-3f76-4d19-884e-b7bcb4396101" DeveloperMode="true" ClientEnabled="false" ServerEnabled="true" TelemetryVersion="1.0.0" />
    </UserJourneyBehaviors>
    -->

    <TechnicalProfile Id="PolicyProfile">
      <DisplayName>PolicyProfile</DisplayName>
      <Protocol Name="OpenIdConnect" />
      <OutputClaims>
        <OutputClaim ClaimTypeReferenceId="objectId" PartnerClaimType="sub"/>
        <OutputClaim ClaimTypeReferenceId="identityProvider" DefaultValue="" />
        <!--<OutputClaim ClaimTypeReferenceId="userLanguage" AlwaysUseDefaultValue="true" DefaultValue="{Culture:RFC5646}" />-->
        <OutputClaim ClaimTypeReferenceId="tenantId" AlwaysUseDefaultValue="true" DefaultValue="{Policy:TenantObjectId}" />
        <!--<OutputClaim ClaimTypeReferenceId="displayName" />-->
        <OutputClaim ClaimTypeReferenceId="extension_crmLeadId"/>
        <OutputClaim ClaimTypeReferenceId="extension_crmLeadGenderCodeId" />
        <OutputClaim ClaimTypeReferenceId="givenName" />
        <OutputClaim ClaimTypeReferenceId="surname" />
        <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" PartnerClaimType="email" />
        <OutputClaim ClaimTypeReferenceId="extension_company"/>
        <OutputClaim ClaimTypeReferenceId="extension_crmLeadTargetGroupId"/>
        <OutputClaim ClaimTypeReferenceId="streetAddress"/>
        <OutputClaim ClaimTypeReferenceId="postalCode"/>
        <OutputClaim ClaimTypeReferenceId="city"/>
        <OutputClaim ClaimTypeReferenceId="state"/>
        <OutputClaim ClaimTypeReferenceId="country"/>
        <OutputClaim ClaimTypeReferenceId="extension_phone"/>
        <OutputClaim ClaimTypeReferenceId="extension_agreeTerms"/>
        <OutputClaim ClaimTypeReferenceId="anonymousUserGuid" AlwaysUseDefaultValue="true" DefaultValue="{OAUTH-KV:anon_user_guid}" />
        <OutputClaim ClaimTypeReferenceId="anonymousUserHasWatchList" AlwaysUseDefaultValue="true" DefaultValue="{OAUTH-KV:anon_user_has_watchlist}" />
        <OutputClaim ClaimTypeReferenceId="extension_userHasSpecList"/>
      </OutputClaims>
      <SubjectNamingInfo ClaimType="sub" />
    </TechnicalProfile>
  </RelyingParty>
</TrustFrameworkPolicy>