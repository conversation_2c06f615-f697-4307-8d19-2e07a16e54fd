<TrustFrameworkPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" PolicySchemaVersion="0.3.0.0" TenantId="identityb2c0pd50.onmicrosoft.com" PolicyId="B2C_1A_ProfileEdit" PublicPolicyUri="http://identityb2c0pd50.onmicrosoft.com/B2C_1A_ProfileEdit" TenantObjectId="69144981-5787-4a27-94f4-ba2bfa0cd88e">
  <BasePolicy>
    <TenantId>identityb2c0pd50.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_TrustFrameworkExtensions</PolicyId>
  </BasePolicy>

  <ClaimsProviders>
    <ClaimsProvider>
      <DisplayName>Self Asserted</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="SelfAsserted-ProfileUpdate">
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="email" />
            <InputClaim ClaimTypeReferenceId="form_legend_personal_data" />
            <InputClaim ClaimTypeReferenceId="extension_crmLeadGenderCodeId"/>
            <InputClaim ClaimTypeReferenceId="givenName" />
            <InputClaim ClaimTypeReferenceId="surname" />
            <InputClaim ClaimTypeReferenceId="extension_company"/>
            <InputClaim ClaimTypeReferenceId="extension_crmLeadTargetGroupId"/>
            <InputClaim ClaimTypeReferenceId="form_legend_contact" />
            <InputClaim ClaimTypeReferenceId="extension_phone"/>
            <InputClaim ClaimTypeReferenceId="streetAddress" />
            <InputClaim ClaimTypeReferenceId="postalCode"/>
            <InputClaim ClaimTypeReferenceId="city"/>
            <InputClaim ClaimTypeReferenceId="state"/>
            <InputClaim ClaimTypeReferenceId="country"/>
            <InputClaim ClaimTypeReferenceId="extension_userHasSpecList"/>
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="form_legend_personal_data" />
            <OutputClaim ClaimTypeReferenceId="displayName"/>
            <OutputClaim ClaimTypeReferenceId="extension_crmLeadGenderCodeId" Required="true"/>
            <OutputClaim ClaimTypeReferenceId="givenName" Required="true" />
            <OutputClaim ClaimTypeReferenceId="surname" Required="true" />
            <OutputClaim ClaimTypeReferenceId="extension_company" Required="true"/>
            <OutputClaim ClaimTypeReferenceId="extension_crmLeadTargetGroupId" Required="true"/>
            <OutputClaim ClaimTypeReferenceId="form_legend_contact" />
            <OutputClaim ClaimTypeReferenceId="extension_phone" />
            <OutputClaim ClaimTypeReferenceId="streetAddress" />
            <OutputClaim ClaimTypeReferenceId="postalCode"/>
            <OutputClaim ClaimTypeReferenceId="city" />
            <OutputClaim ClaimTypeReferenceId="state"/>
            <OutputClaim ClaimTypeReferenceId="country" Required="true"/>
          </OutputClaims>
          <ValidationTechnicalProfiles>
            <ValidationTechnicalProfile ReferenceId="Validate-DisplayName" />
            <ValidationTechnicalProfile ReferenceId="AAD-UserWriteProfileUsingObjectId" />
          </ValidationTechnicalProfiles>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
  </ClaimsProviders>

  <UserJourneys>
    <UserJourney Id="ProfileEdit">
      <OrchestrationSteps>
        <OrchestrationStep Order="1" Type="ClaimsProviderSelection" ContentDefinitionReferenceId="api.idpselections">
          <ClaimsProviderSelections>
            <ClaimsProviderSelection TargetClaimsExchangeId="LocalAccountSigninEmailExchange" />
          </ClaimsProviderSelections>
        </OrchestrationStep>
        <OrchestrationStep Order="2" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="LocalAccountSigninEmailExchange" TechnicalProfileReferenceId="SelfAsserted-LocalAccountSignin-Email" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="3" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="AADUserReadWithObjectId" TechnicalProfileReferenceId="AAD-UserReadUsingObjectId" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="4" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="B2CUserProfileUpdateExchange" TechnicalProfileReferenceId="SelfAsserted-ProfileUpdate" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="5" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="RESTCrmConnectorUpdateLead" TechnicalProfileReferenceId="REST-CrmConnector-Update-Lead" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="6" Type="SendClaims" CpimIssuerTechnicalProfileReferenceId="JwtIssuer" />
      </OrchestrationSteps>
      <ClientDefinition ReferenceId="DefaultWeb" />
    </UserJourney>
  </UserJourneys>
  <RelyingParty>
    <DefaultUserJourney ReferenceId="ProfileEdit" />
    <UserJourneyBehaviors>
      <ScriptExecution>Allow</ScriptExecution>
    </UserJourneyBehaviors>
    <TechnicalProfile Id="PolicyProfile">
      <DisplayName>PolicyProfile</DisplayName>
      <Protocol Name="OpenIdConnect" />
      <OutputClaims>
        <OutputClaim ClaimTypeReferenceId="extension_crmLeadId" />
        <OutputClaim ClaimTypeReferenceId="objectId" PartnerClaimType="sub" />
        <OutputClaim ClaimTypeReferenceId="tenantId" AlwaysUseDefaultValue="true" DefaultValue="{Policy:TenantObjectId}" />
        <OutputClaim ClaimTypeReferenceId="extension_crmLeadGenderCodeId" />
        <OutputClaim ClaimTypeReferenceId="givenName" />
        <OutputClaim ClaimTypeReferenceId="surname"/>
        <OutputClaim ClaimTypeReferenceId="extension_company" />
        <OutputClaim ClaimTypeReferenceId="extension_crmLeadTargetGroupId"/>
        <OutputClaim ClaimTypeReferenceId="streetAddress"/>
        <OutputClaim ClaimTypeReferenceId="postalCode" />
        <OutputClaim ClaimTypeReferenceId="city"/>
        <OutputClaim ClaimTypeReferenceId="state"/>
        <OutputClaim ClaimTypeReferenceId="country"/>
        <OutputClaim ClaimTypeReferenceId="extension_phone"/>
        <OutputClaim ClaimTypeReferenceId="signInNames.emailAddress" PartnerClaimType="email" />
        <OutputClaim ClaimTypeReferenceId="extension_userHasSpecList"/>
      </OutputClaims>
      <SubjectNamingInfo ClaimType="sub" />
    </TechnicalProfile>
  </RelyingParty>
</TrustFrameworkPolicy>
