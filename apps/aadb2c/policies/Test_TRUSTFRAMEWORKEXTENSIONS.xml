<TrustFrameworkPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" PolicySchemaVersion="0.3.0.0" TenantId="identityb2c0td50.onmicrosoft.com" PolicyId="B2C_1A_TrustFrameworkExtensions" PublicPolicyUri="http://identityb2c0td50.onmicrosoft.com/B2C_1A_TrustFrameworkExtensions" TenantObjectId="7896185f-40e9-4b73-b442-0c737accb9a6">
  <BasePolicy>
    <TenantId>identityb2c0td50.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_TrustFrameworkLocalization</PolicyId>
  </BasePolicy>
  <BuildingBlocks>
    <ClaimsSchema>
      <ClaimType Id="userLanguage">
        <DisplayName>User UI language</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="policyName">
        <DisplayName>Policy Name</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="extension_crmLeadId">
        <DisplayName>CRM Lead Id</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <!--Custom Query Parameter for anonymous User Guid  -->      
      <ClaimType Id="anonymousUserGuid">
        <DisplayName>Custom Query Parameter with anonymous User Guid {OAUTH-KV:anon_user_guid}</DisplayName>
        <DataType>string</DataType>        
      </ClaimType>
      <!--Custom Query Parameter to identify if anonymous User has Product Watchlist -->
      <ClaimType Id="anonymousUserHasWatchList">
        <DisplayName>Custom Query Parameter indicating if an anonymous User has Product Watchlist {OAUTH-KV:anon_user_has_watchlist}</DisplayName>
        <DataType>string</DataType>        
      </ClaimType>
      <!--Custom User Attribute to persist if anonymous User had Product Watchlist -->      
      <ClaimType Id="extension_userHasSpecList">
        <DisplayName>userHasSpecList</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <!-- Holds the value of the migration status on the Azure AD B2C account -->
      <ClaimType Id="extension_requiresMigration">
        <DisplayName>extension_requiresMigration</DisplayName>
        <DataType>boolean</DataType>
        <AdminHelpText>extension_requiresMigration</AdminHelpText>
        <UserHelpText>extension_requiresMigration</UserHelpText>
      </ClaimType>
      <!-- Holds the value of whether the authentication succeeded at the legacy IdP -->
      <ClaimType Id="tokenSuccess">
        <DisplayName>tokenSuccess</DisplayName>
        <DataType>boolean</DataType>
        <AdminHelpText>tokenSuccess</AdminHelpText>
        <UserHelpText>tokenSuccess</UserHelpText>
      </ClaimType>
      <!-- Holds the value 'false' when the legacy IdP authentication succeeded -->
      <ClaimType Id="migrationRequired">
        <DisplayName>migrationRequired</DisplayName>
        <DataType>boolean</DataType>
        <AdminHelpText>migrationRequired</AdminHelpText>
        <UserHelpText>migrationRequired</UserHelpText>
      </ClaimType>
      <ClaimType Id="form_legend_personal_data">
        <DisplayName>Personal Data</DisplayName>
        <DataType>string</DataType>
        <UserInputType>Paragraph</UserInputType>
      </ClaimType>
      <ClaimType Id="form_legend_contact">
        <DisplayName>Contact</DisplayName>
        <DataType>string</DataType>
        <UserInputType>Paragraph</UserInputType>
      </ClaimType>
      <ClaimType Id="extension_crmLeadGenderCodeId">
        <DisplayName>Salutation</DisplayName>
        <DataType>string</DataType>
        <UserInputType>DropdownSingleSelect</UserInputType>
        <Restriction>
          <Enumeration Text="male" Value="1" />
          <Enumeration Text="female" Value="2" />
          <Enumeration Text="diverse" Value="100000000" />
        </Restriction>
      </ClaimType>
      <ClaimType Id="country">
        <DisplayName>Country</DisplayName>
        <DataType>string</DataType>
        <UserInputType>DropdownSingleSelect</UserInputType>
        <Restriction>
          <Enumeration Text="Austria" Value="AT" />
          <Enumeration Text="Belgium" Value="BE" />
          <Enumeration Text="Canada" Value="CA" />
          <Enumeration Text="China" Value="CN" />
          <Enumeration Text="Czech Republic" Value="CZ" />
          <Enumeration Text="Denmark" Value="DK" />
          <Enumeration Text="Finland" Value="FI" />
          <Enumeration Text="France" Value="FR" />
          <Enumeration Text="Germany" Value="DE" />
          <Enumeration Text="International" Value="XY" />
          <Enumeration Text="Italy" Value="IT" />
          <Enumeration Text="Mexico" Value="MX" />
          <Enumeration Text="Netherlands" Value="NL" />
          <Enumeration Text="Norway" Value="NO" />
          <Enumeration Text="Poland" Value="PL" />
          <Enumeration Text="Portugal" Value="PT" />
          <Enumeration Text="Russia" Value="RU" />
          <Enumeration Text="Spain" Value="ES" />
          <Enumeration Text="Sweden" Value="SE" />
          <Enumeration Text="Switzerland" Value="CH" />
          <Enumeration Text="Turkey" Value="TR" />
          <Enumeration Text="United Arab Emirates" Value="AE" />
          <Enumeration Text="United Kingdom" Value="GB" />
          <Enumeration Text="United States" Value="US" />
        </Restriction>
      </ClaimType>
      <ClaimType Id="extension_company">
        <DisplayName>Company</DisplayName>
        <DataType>string</DataType>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="streetAddress">
        <DisplayName>Street</DisplayName>
        <DataType>string</DataType>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="postalCode">
        <DisplayName>ZIP Code</DisplayName>
        <DataType>string</DataType>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="city">
        <DisplayName>City</DisplayName>
        <DataType>string</DataType>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="extension_phone">
        <DisplayName>Phone</DisplayName>
        <DataType>string</DataType>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="state">
        <DisplayName>State</DisplayName>
        <DataType>string</DataType>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="extension_crmLeadTargetGroupId">
        <DisplayName>Profession</DisplayName>
        <DataType>string</DataType>
        <UserInputType>DropdownSingleSelect</UserInputType>
        <Restriction>
          <Enumeration Text="Architecture / Design" Value="222390000" />
          <Enumeration Text="Plumbing" Value="222390007" />
          <Enumeration Text="Private use" Value="222390003" />
          <Enumeration Text="Investor / Developer" Value="222390004" />
          <Enumeration Text="Trade" Value="222390002" />
          <Enumeration Text="Hotel" Value="222390008" />
          <Enumeration Text="Other" Value="222390006" />
        </Restriction>
      </ClaimType>
      <ClaimType Id="extension_agreeTerms">
        <DisplayName>#Terms &amp; Conditions</DisplayName>
        <DataType>string</DataType>
        <UserInputType>CheckboxMultiSelect</UserInputType>
        <Restriction>
          <Enumeration Text="I agree to the {terms and conditions} and {data protection policy} of Dornbracht." Value="true" SelectByDefault="false" />
        </Restriction>
      </ClaimType>
    </ClaimsSchema>
    <ClaimsTransformations>
      <ClaimsTransformation Id="CreateDisplayNameFromFirstNameAndLastName" TransformationMethod="FormatStringMultipleClaims">
        <InputClaims>
          <InputClaim ClaimTypeReferenceId="givenName" TransformationClaimType="inputClaim1" />
          <InputClaim ClaimTypeReferenceId="surname" TransformationClaimType="inputClaim2" />
        </InputClaims>
        <InputParameters>
          <InputParameter Id="stringFormat" DataType="string" Value="{0} {1}" />
        </InputParameters>
        <OutputClaims>
          <OutputClaim ClaimTypeReferenceId="displayName" TransformationClaimType="outputClaim" />
        </OutputClaims>
      </ClaimsTransformation>
    </ClaimsTransformations>
    <ContentDefinitions>
      <ContentDefinition Id="api.error">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:globalexception:1.2.1</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.idpselections">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:providerselection:1.2.0</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.idpselections.signup">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:providerselection:1.2.0</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.signuporsignin">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:unifiedssp:2.1.7</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.14</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.profileupdate">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.14</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.localaccountsignup">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.14</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.localaccountpasswordreset">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.14</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.phonefactor">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:multifactor:1.2.0</DataUri>
      </ContentDefinition>
    </ContentDefinitions>
  </BuildingBlocks>
  <ClaimsProviders>
    <ClaimsProvider>
      <DisplayName>Azure Active Directory</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="AAD-Common">
          <DisplayName>Azure Active Directory</DisplayName>
          <Metadata>
            <Item Key="ApplicationObjectId">1bbfa9f7-9517-4b7c-a7b2-6ed9b13c725c</Item>
            <Item Key="ClientId">7f20b2e9-1d88-464a-b1c9-3f60ff9d28c6</Item>
          </Metadata>
          <PersistedClaims>
            <PersistedClaim ClaimTypeReferenceId="extension_crmLeadId"/>
            <PersistedClaim ClaimTypeReferenceId="extension_crmLeadGenderCodeId"/>
            <PersistedClaim ClaimTypeReferenceId="extension_company"/>
            <PersistedClaim ClaimTypeReferenceId="extension_crmLeadTargetGroupId"/>
            <PersistedClaim ClaimTypeReferenceId="streetAddress"/>
            <PersistedClaim ClaimTypeReferenceId="postalCode"/>
            <PersistedClaim ClaimTypeReferenceId="city"/>
            <PersistedClaim ClaimTypeReferenceId="state"/>
            <PersistedClaim ClaimTypeReferenceId="country"/>
            <PersistedClaim ClaimTypeReferenceId="extension_phone"/>
            <PersistedClaim ClaimTypeReferenceId="extension_agreeTerms"/>
            <PersistedClaim ClaimTypeReferenceId="extension_userHasSpecList"/>
          </PersistedClaims>
        </TechnicalProfile>
        <!-- TP responsible for reading user data by ID => Edit Profile-->
        <TechnicalProfile Id="AAD-UserReadUsingObjectId">
          <OutputClaims>
            <!-- Read Additional claims -->
            <OutputClaim ClaimTypeReferenceId="extension_crmLeadId"/>
            <OutputClaim ClaimTypeReferenceId="extension_crmLeadGenderCodeId"/>
            <OutputClaim ClaimTypeReferenceId="extension_company"/>
            <OutputClaim ClaimTypeReferenceId="extension_crmLeadTargetGroupId"/>
            <OutputClaim ClaimTypeReferenceId="streetAddress"/>
            <OutputClaim ClaimTypeReferenceId="postalCode"/>
            <OutputClaim ClaimTypeReferenceId="city"/>
            <OutputClaim ClaimTypeReferenceId="state"/>
            <OutputClaim ClaimTypeReferenceId="country"/>
            <OutputClaim ClaimTypeReferenceId="extension_phone"/>
            <OutputClaim ClaimTypeReferenceId="extension_agreeTerms"/>
            <OutputClaim ClaimTypeReferenceId="extension_userHasSpecList"/>
          </OutputClaims>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Local Account SignIn</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="login-NonInteractive">
          <Metadata>
            <Item Key="client_id">612e3e9e-1c38-40e5-92e4-502ea5556407</Item>
            <Item Key="IdTokenAudience">25b1bdb1-3fb6-440e-a9ae-8701c3337f95</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="client_id" DefaultValue="612e3e9e-1c38-40e5-92e4-502ea5556407" />
            <InputClaim ClaimTypeReferenceId="resource_id" PartnerClaimType="resource" DefaultValue="25b1bdb1-3fb6-440e-a9ae-8701c3337f95" />
          </InputClaims>
          <PersistedClaims>
            <PersistedClaim ClaimTypeReferenceId="extension_crmLeadId"/>
            <PersistedClaim ClaimTypeReferenceId="extension_crmLeadGenderCodeId"/>
            <PersistedClaim ClaimTypeReferenceId="extension_company"/>
            <PersistedClaim ClaimTypeReferenceId="extension_crmLeadTargetGroupId"/>
            <PersistedClaim ClaimTypeReferenceId="streetAddress"/>
            <PersistedClaim ClaimTypeReferenceId="postalCode"/>
            <PersistedClaim ClaimTypeReferenceId="city"/>
            <PersistedClaim ClaimTypeReferenceId="state"/>
            <PersistedClaim ClaimTypeReferenceId="country"/>
            <PersistedClaim ClaimTypeReferenceId="extension_phone"/>
            <PersistedClaim ClaimTypeReferenceId="extension_agreeTerms"/>
          </PersistedClaims>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>

    <!-- ### Custom TECHNICAL PROFILES for Seamlesss USER MIGRATION from Legacy Sitecore IDP ### -->
    <!-- https://github.com/azure-ad-b2c/user-migration/tree/master/seamless-account-migration#configuring-the-azure-ad-b2c-custom-policy -->

    <ClaimsProvider>
      <DisplayName>Local Account SignIn - Read migration status</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="Get-requiresMigration-status-signin">
          <Metadata>
            <Item Key="Operation">Read</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalDoesNotExist">true</Item>
            <Item Key="UserMessageIfClaimsPrincipalDoesNotExist">An account could not be found for the provided user ID.</Item>
          </Metadata>
          <IncludeInSso>false</IncludeInSso>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="signInName" PartnerClaimType="signInNames.emailAddress" Required="true" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="objectId" />
            <!-- Set a default value (false) in the case the account does not have this attribute defined -->
            <OutputClaim ClaimTypeReferenceId="extension_requiresMigration" DefaultValue="false" />
          </OutputClaims>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>REST API to communicate with Legacy IdP</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="UserMigrationViaLegacyIdp">
          <DisplayName>REST API call to communicate with Legacy IdP</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.RestfulProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="ServiceUrl">https://api-test.dornbracht.com/v1/util/checkpwdhash</Item>
            <Item Key="SendClaimsIn">Body</Item>
            <Item Key="AuthenticationType">ApiKeyHeader</Item>
            <Item Key="AllowInsecureAuthInProduction">false</Item>
          </Metadata>
          <CryptographicKeys>
            <Key Id="x-functions-key" StorageReferenceId="B2C_1A_checkPwdHash" />
          </CryptographicKeys>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="signInName" />
            <InputClaim ClaimTypeReferenceId="password" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="tokenSuccess" DefaultValue="false"/>
            <OutputClaim ClaimTypeReferenceId="migrationRequired"/>
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Local Account SignIn - Write new password and unmark for migration</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="AAD-WritePasswordAndFlipMigratedFlag">
          <Metadata>
            <Item Key="Operation">Write</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalAlreadyExists">false</Item>
          </Metadata>
          <IncludeInSso>false</IncludeInSso>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="objectId" Required="true" />
          </InputClaims>
          <PersistedClaims>
            <PersistedClaim ClaimTypeReferenceId="objectId" />
            <PersistedClaim ClaimTypeReferenceId="userPrincipalName" />
            <PersistedClaim ClaimTypeReferenceId="displayName" />
            <PersistedClaim ClaimTypeReferenceId="password" PartnerClaimType="password"/>
            <PersistedClaim ClaimTypeReferenceId="passwordPolicies" DefaultValue="DisablePasswordExpiration, DisableStrongPassword" AlwaysUseDefaultValue="true"/>
            <PersistedClaim ClaimTypeReferenceId="migrationRequired" PartnerClaimType="extension_requiresMigration"/>
          </PersistedClaims>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-AAD" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Adjusted "Local Account Sign In" due to User Migration</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="SelfAsserted-LocalAccountSignin-Email">
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="extension_requiresMigration" />
          </OutputClaims>
          <ValidationTechnicalProfiles>
            <!--Add user migration validation technical profiles before login-NonInteractive -->

            <!-- Populate extension_requireMigration into the claims pipeline -->
            <ValidationTechnicalProfile ReferenceId="Get-requiresMigration-status-signin" ContinueOnError="false" />

            <!-- If extension_requireMigration is true, call the legacy IdP via the REST API -->
            <ValidationTechnicalProfile ReferenceId="UserMigrationViaLegacyIdp" ContinueOnError="false">
              <Preconditions>
                <Precondition Type="ClaimEquals" ExecuteActionsIf="true">
                  <Value>extension_requiresMigration</Value>
                  <Value>False</Value>
                  <Action>SkipThisValidationTechnicalProfile</Action>
                </Precondition>
              </Preconditions>
            </ValidationTechnicalProfile>

            <!-- If the API returned 'tokensuccess', write the new password and unmark the account for migration -->
            <ValidationTechnicalProfile ReferenceId="AAD-WritePasswordAndFlipMigratedFlag" ContinueOnError="false">
              <Preconditions>
                <Precondition Type="ClaimsExist" ExecuteActionsIf="false">
                  <Value>tokenSuccess</Value>
                  <Action>SkipThisValidationTechnicalProfile</Action>
                </Precondition>
              </Preconditions>
            </ValidationTechnicalProfile>

            <!-- Initiate a normal logon against Azure AD B2C -->
            <ValidationTechnicalProfile ReferenceId="login-NonInteractive" />
          </ValidationTechnicalProfiles>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Local Account Password Reset - Read migration flag</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="Get-requiresMigration-status-password-reset">
          <Metadata>
            <Item Key="Operation">Read</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalDoesNotExist">true</Item>
            <Item Key="UserMessageIfClaimsPrincipalDoesNotExist">An account could not be found for the provided user ID.</Item>
          </Metadata>
          <IncludeInSso>false</IncludeInSso>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="objectId" Required="true" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="extension_requiresMigration" DefaultValue="false" />
          </OutputClaims>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Local Account Password Reset - Flip migration flag</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="AAD-FlipMigratedFlag">
          <Metadata>
            <Item Key="Operation">Write</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalAlreadyExists">false</Item>
          </Metadata>
          <IncludeInSso>false</IncludeInSso>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="objectId" Required="true" />
          </InputClaims>
          <PersistedClaims>
            <PersistedClaim ClaimTypeReferenceId="objectId" />
            <PersistedClaim ClaimTypeReferenceId="userPrincipalName" />
            <PersistedClaim ClaimTypeReferenceId="displayName" />
            <PersistedClaim ClaimTypeReferenceId="migrationRequired" PartnerClaimType="extension_requiresMigration" DefaultValue="False" AlwaysUseDefaultValue="true"/>
          </PersistedClaims>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-AAD" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Local Account Password Reset - Write Password</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="LocalAccountWritePasswordUsingObjectId">
          <ValidationTechnicalProfiles>
            <ValidationTechnicalProfile ReferenceId="Get-requiresMigration-status-password-reset" ContinueOnError="false" />
            <ValidationTechnicalProfile ReferenceId="AAD-FlipMigratedFlag" ContinueOnError="false">
              <Preconditions>
                <Precondition Type="ClaimEquals" ExecuteActionsIf="true">
                  <Value>extension_requiresMigration</Value>
                  <Value>False</Value>
                  <Action>SkipThisValidationTechnicalProfile</Action>
                </Precondition>
              </Preconditions>
            </ValidationTechnicalProfile>
            <ValidationTechnicalProfile ReferenceId="AAD-UserWritePasswordUsingObjectId" />
          </ValidationTechnicalProfiles>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>### Custom DOCOR Technical Profiles for Dynamics CRM Integration ###</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="Validate-DisplayName">
          <DisplayName>Validate displayName</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.ClaimsTransformationProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="displayName" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="displayName" />
          </OutputClaims>
          <OutputClaimsTransformations>
            <OutputClaimsTransformation ReferenceId="CreateDisplayNameFromFirstNameAndLastName" />
          </OutputClaimsTransformations>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
        </TechnicalProfile>

        <TechnicalProfile Id="REST-CrmConnector-Create-Lead">
          <DisplayName>CRM Connector Azure Function for Lead Registration</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.RestfulProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <!-- Set the ServiceUrl with your own REST API endpoint -->
            <Item Key="ServiceUrl">https://api-test.dornbracht.com/v1/crm/lead/create</Item>
            <Item Key="SendClaimsIn">Body</Item>
            <Item Key="AuthenticationType">ApiKeyHeader</Item>
            <Item Key="AllowInsecureAuthInProduction">false</Item>
          </Metadata>
          <CryptographicKeys>
            <Key Id="x-functions-key" StorageReferenceId="B2C_1A_crmRestApiKeyCreateLead" />
          </CryptographicKeys>
          <InputClaims>
            <!-- Claims sent to your REST API -->
            <InputClaim ClaimTypeReferenceId="objectId" />
            <InputClaim ClaimTypeReferenceId="tenantId" PartnerClaimType="tid"/>
            <InputClaim ClaimTypeReferenceId="policyName" DefaultValue="{Policy:PolicyId}" AlwaysUseDefaultValue="true" />
            <InputClaim ClaimTypeReferenceId="userLanguage" DefaultValue="{Culture:RFC5646}" AlwaysUseDefaultValue="true" />
            <InputClaim ClaimTypeReferenceId="anonymousUserGuid" DefaultValue="{OAUTH-KV:anon_user_guid}" AlwaysUseDefaultValue="true" />          
            <InputClaim ClaimTypeReferenceId="extension_crmLeadGenderCodeId" />
            <InputClaim ClaimTypeReferenceId="givenName" />
            <InputClaim ClaimTypeReferenceId="surname" />
            <InputClaim ClaimTypeReferenceId="email" />
            <InputClaim ClaimTypeReferenceId="extension_company"/>
            <InputClaim ClaimTypeReferenceId="extension_crmLeadTargetGroupId"/>
            <InputClaim ClaimTypeReferenceId="country"/>
            <InputClaim ClaimTypeReferenceId="extension_phone"/>
            <InputClaim ClaimTypeReferenceId="extension_userHasSpecList" />
          </InputClaims>
          <OutputClaims>
            <!-- Claims parsed from your REST API -->
            <OutputClaim ClaimTypeReferenceId="extension_crmLeadId" PartnerClaimType="crmLeadId" />
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
        </TechnicalProfile>

        <TechnicalProfile Id="REST-CrmConnector-Update-Lead">
          <DisplayName>CRM Connector Azure Function for Lead Update</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.RestfulProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <!-- Set the ServiceUrl with your own REST API endpoint -->
            <Item Key="ServiceUrl">https://api-test.dornbracht.com/v1/crm/lead/update</Item>
            <Item Key="SendClaimsIn">Body</Item>
            <Item Key="AuthenticationType">ApiKeyHeader</Item>
            <Item Key="AllowInsecureAuthInProduction">false</Item>
          </Metadata>
          <CryptographicKeys>
            <Key Id="x-functions-key" StorageReferenceId="B2C_1A_crmRestApiKeyUpdateLead" />
          </CryptographicKeys>
          <InputClaims>
            <!-- Claims sent to your REST API -->
            <InputClaim ClaimTypeReferenceId="extension_crmLeadId" />
            <InputClaim ClaimTypeReferenceId="objectId" />
            <InputClaim ClaimTypeReferenceId="tenantId" PartnerClaimType="tid"/>
            <InputClaim ClaimTypeReferenceId="policyName" DefaultValue="{Policy:PolicyId}" AlwaysUseDefaultValue="true" />
            <InputClaim ClaimTypeReferenceId="extension_crmLeadGenderCodeId" />
            <InputClaim ClaimTypeReferenceId="givenName" />
            <InputClaim ClaimTypeReferenceId="surname" />
            <InputClaim ClaimTypeReferenceId="email" />
            <InputClaim ClaimTypeReferenceId="extension_company"/>
            <InputClaim ClaimTypeReferenceId="extension_crmLeadTargetGroupId"/>
            <InputClaim ClaimTypeReferenceId="streetAddress"/>
            <InputClaim ClaimTypeReferenceId="postalCode"/>
            <InputClaim ClaimTypeReferenceId="city"/>
            <InputClaim ClaimTypeReferenceId="state"/>
            <InputClaim ClaimTypeReferenceId="country"/>
            <InputClaim ClaimTypeReferenceId="extension_phone"/>
            <InputClaim ClaimTypeReferenceId="extension_userHasSpecList" />
          </InputClaims>
          <OutputClaims>
            <!-- TBD. Claims parsed from your REST API -->
          </OutputClaims>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-Noop" />
        </TechnicalProfile>

      </TechnicalProfiles>
    </ClaimsProvider>
  </ClaimsProviders>

  <UserJourneys xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06">
    <UserJourney Id="SignUpOrSignIn">
      <OrchestrationSteps>
        <OrchestrationStep Order="1" Type="CombinedSignInAndSignUp" ContentDefinitionReferenceId="api.signuporsignin">
          <ClaimsProviderSelections>
            <ClaimsProviderSelection ValidationClaimsExchangeId="LocalAccountSigninEmailExchange" />
          </ClaimsProviderSelections>
          <ClaimsExchanges>
            <ClaimsExchange Id="LocalAccountSigninEmailExchange" TechnicalProfileReferenceId="SelfAsserted-LocalAccountSignin-Email" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="2" Type="ClaimsExchange">
          <Preconditions>
            <Precondition Type="ClaimsExist" ExecuteActionsIf="true">
              <Value>objectId</Value>
              <Action>SkipThisOrchestrationStep</Action>
            </Precondition>
          </Preconditions>
          <ClaimsExchanges>
            <ClaimsExchange Id="SignUpWithLogonEmailExchange" TechnicalProfileReferenceId="LocalAccountSignUpWithLogonEmail" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <!-- This step reads any user attributes that we may not have received when in the token. -->
        <OrchestrationStep Order="3" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="AADUserReadWithObjectId" TechnicalProfileReferenceId="AAD-UserReadUsingObjectId" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="4" Type="SendClaims" CpimIssuerTechnicalProfileReferenceId="JwtIssuer" />
      </OrchestrationSteps>
      <ClientDefinition ReferenceId="DefaultWeb" />
    </UserJourney>
    <UserJourney Id="ProfileEdit">
      <OrchestrationSteps>
        <OrchestrationStep Order="1" Type="ClaimsProviderSelection" ContentDefinitionReferenceId="api.idpselections">
          <ClaimsProviderSelections>
            <ClaimsProviderSelection TargetClaimsExchangeId="LocalAccountSigninEmailExchange" />
          </ClaimsProviderSelections>
        </OrchestrationStep>
        <OrchestrationStep Order="2" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="LocalAccountSigninEmailExchange" TechnicalProfileReferenceId="SelfAsserted-LocalAccountSignin-Email" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="3" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="AADUserReadWithObjectId" TechnicalProfileReferenceId="AAD-UserReadUsingObjectId" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="4" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="B2CUserProfileUpdateExchange" TechnicalProfileReferenceId="SelfAsserted-ProfileUpdate" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="5" Type="SendClaims" CpimIssuerTechnicalProfileReferenceId="JwtIssuer" />
      </OrchestrationSteps>
      <ClientDefinition ReferenceId="DefaultWeb" />
    </UserJourney>
    <UserJourney Id="PasswordReset">
      <OrchestrationSteps>
        <OrchestrationStep Order="1" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="PasswordResetUsingEmailAddressExchange" TechnicalProfileReferenceId="LocalAccountDiscoveryUsingEmailAddress" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="2" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="NewCredentials" TechnicalProfileReferenceId="LocalAccountWritePasswordUsingObjectId" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="3" Type="SendClaims" CpimIssuerTechnicalProfileReferenceId="JwtIssuer" />
      </OrchestrationSteps>
      <ClientDefinition ReferenceId="DefaultWeb" />
    </UserJourney>
  </UserJourneys>
</TrustFrameworkPolicy>
