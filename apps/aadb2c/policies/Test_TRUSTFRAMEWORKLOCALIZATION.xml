<TrustFrameworkPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:xsd="http://www.w3.org/2001/XMLSchema"
  xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" PolicySchemaVersion="0.3.0.0" TenantId="identityb2c0td50.onmicrosoft.com" PolicyId="B2C_1A_TrustFrameworkLocalization" PublicPolicyUri="http://identityb2c0td50.onmicrosoft.com/B2C_1A_TrustFrameworkLocalization" TenantObjectId="7896185f-40e9-4b73-b442-0c737accb9a6">
  <BasePolicy>
    <TenantId>identityb2c0td50.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_TrustFrameworkBase</PolicyId>
  </BasePolicy>
  <BuildingBlocks>
    <ContentDefinitions>
      <ContentDefinition Id="api.signuporsignin">
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.signuporsignin.en" />
          <LocalizedResourcesReference Language="de" LocalizedResourcesReferenceId="api.signuporsignin.de" />
          <LocalizedResourcesReference Language="es" LocalizedResourcesReferenceId="api.signuporsignin.es" />
          <LocalizedResourcesReference Language="fr" LocalizedResourcesReferenceId="api.signuporsignin.fr" />
          <LocalizedResourcesReference Language="it" LocalizedResourcesReferenceId="api.signuporsignin.it" />
        </LocalizedResourcesReferences>
      </ContentDefinition>
      <ContentDefinition Id="api.localaccountsignup">
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.localaccountsignup.en" />
          <LocalizedResourcesReference Language="de" LocalizedResourcesReferenceId="api.localaccountsignup.de" />
          <LocalizedResourcesReference Language="es" LocalizedResourcesReferenceId="api.localaccountsignup.es" />
          <LocalizedResourcesReference Language="fr" LocalizedResourcesReferenceId="api.localaccountsignup.fr" />
          <LocalizedResourcesReference Language="it" LocalizedResourcesReferenceId="api.localaccountsignup.it" />
        </LocalizedResourcesReferences>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted">
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.selfasserted.en" />
          <!-- Add more languages here -->
        </LocalizedResourcesReferences>
      </ContentDefinition>
      <ContentDefinition Id="api.localaccountpasswordreset">
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.localaccountpasswordreset.en" />
          <LocalizedResourcesReference Language="de" LocalizedResourcesReferenceId="api.localaccountpasswordreset.de" />
          <LocalizedResourcesReference Language="es" LocalizedResourcesReferenceId="api.localaccountpasswordreset.es" />
          <LocalizedResourcesReference Language="fr" LocalizedResourcesReferenceId="api.localaccountpasswordreset.fr" />
          <LocalizedResourcesReference Language="it" LocalizedResourcesReferenceId="api.localaccountpasswordreset.it" />
          <!-- Add more languages here -->
        </LocalizedResourcesReferences>
      </ContentDefinition>
      <ContentDefinition Id="api.idpselections">
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.idpselections.en" />
          <!-- Add more languages here -->
        </LocalizedResourcesReferences>
      </ContentDefinition>
      <ContentDefinition Id="api.localaccountsignin">
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.localaccountsignin.en" />
          <LocalizedResourcesReference Language="de" LocalizedResourcesReferenceId="api.localaccountsignin.de" />
          <LocalizedResourcesReference Language="es" LocalizedResourcesReferenceId="api.localaccountsignin.es" />
          <LocalizedResourcesReference Language="fr" LocalizedResourcesReferenceId="api.localaccountsignin.fr" />
          <LocalizedResourcesReference Language="it" LocalizedResourcesReferenceId="api.localaccountsignin.it" />
        </LocalizedResourcesReferences>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.profileupdate">
        <LocalizedResourcesReferences MergeBehavior="Prepend">
          <LocalizedResourcesReference Language="en" LocalizedResourcesReferenceId="api.selfasserted.profileupdate.en" />
          <LocalizedResourcesReference Language="de" LocalizedResourcesReferenceId="api.selfasserted.profileupdate.de" />
          <LocalizedResourcesReference Language="es" LocalizedResourcesReferenceId="api.selfasserted.profileupdate.es" />
          <LocalizedResourcesReference Language="fr" LocalizedResourcesReferenceId="api.selfasserted.profileupdate.fr" />
          <LocalizedResourcesReference Language="it" LocalizedResourcesReferenceId="api.selfasserted.profileupdate.it" />
        </LocalizedResourcesReferences>
      </ContentDefinition>
    </ContentDefinitions>

    <Localization Enabled="true">

      <SupportedLanguages DefaultLanguage="en" MergeBehavior="Append">
        <SupportedLanguage>en</SupportedLanguage>
        <SupportedLanguage>de</SupportedLanguage>
        <SupportedLanguage>es</SupportedLanguage>
        <SupportedLanguage>fr</SupportedLanguage>
        <SupportedLanguage>it</SupportedLanguage>
      </SupportedLanguages>

      <!--Local account sign-up or sign-in page English-->
      <LocalizedResources Id="api.signuporsignin.en">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Welcome to Dornbracht!</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="local_intro_generic">Sign in with your {0}</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="signInName" StringId="DisplayName">Email address</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="invalid_generic">Enter email address</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="createaccount_intro">New to Dornbracht?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="createaccount_one_link">Sign up now</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="password" StringId="DisplayName">Password</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="requiredField_password">Please enter your password</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="requiredField_generic">Please enter your {0}</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="forgotpassword_link">Forgot your password?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_signin">Sign in</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="divider_title">OR</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="unknown_error">We are having trouble signing you in. Please try again later.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidPassword">Your password is incorrect.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfPasswordExpired">Your password has expired.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalDoesNotExist">We can't seem to find your account.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfOldPasswordUsed">Looks like you used an old password.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="DefaultMessage">Invalid username or password.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfUserAccountDisabled">Your account has been locked. Contact your support person to unlock it, then try again.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfUserAccountLocked">Your account is temporarily locked to prevent unauthorized use. Try again later.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="AADRequestsThrottled">There are too many requests at this moment. Please wait for some time and try again.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!--Local account sign-up or sign-in page German-->
      <LocalizedResources Id="api.signuporsignin.de">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Willkommen bei Dornbracht!</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="signInName" StringId="DisplayName">E-Mail-Adresse</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="invalid_generic">E-Mail-Adresse eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="password" StringId="DisplayName">Passwort</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="createaccount_intro">Neu bei Dornbracht?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="createaccount_one_link">Jetzt registrieren</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="forgotpassword_link">Passwort vergessen?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="unknown_error">Wir können Sie nicht anmelden. Versuchen Sie es später noch mal.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidPassword">Ihr Kennwort ist falsch.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfPasswordExpired">Ihre Kennwort ist abgelaufen.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalDoesNotExist">Wir können Ihr Konto nicht finden.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfOldPasswordUsed">Sie verwenden offenbar ein altes Kennwort.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="DefaultMessage">Ungültiger Benutzername oder ungültiges Kennwort.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfUserAccountDisabled">Ihr Konto wurde gesperrt. Wenden Sie sich an einen Supportmitarbeiter, um die Sperre aufheben zu lassen, und wiederholen Sie den Vorgang.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfUserAccountLocked">Ihr Konto wurde vorübergehend gesperrt, um eine unbefugte Nutzung zu verhindern. Versuchen Sie es später noch einmal.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="AADRequestsThrottled">Derzeit sind zu viele Anforderungen vorhanden. Warten Sie einige Zeit, und versuchen Sie es erneut.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!--Local account sign-up or sign-in page Spanish-->
      <LocalizedResources Id="api.signuporsignin.es">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">¡Bienvenido a Dornbracht!</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="signInName" StringId="DisplayName">Correo electrónico</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="password" StringId="DisplayName">Contraseña</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="invalid_generic">Introduzca la dirección de correo electrónico</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="createaccount_intro">¿Eres nuevo en Dornbracht?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="createaccount_one_link">Registrarse ahora</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="forgotpassword_link">¿Ha olvidado su contraseña?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="unknown_error">Tenemos problemas para iniciar su sesión. Inténtelo de nuevo más tarde.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidPassword">La contraseña es incorrecta.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfPasswordExpired">La contraseña expiró.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalDoesNotExist">Parece que no encontramos su cuenta.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfOldPasswordUsed">Parece que ha usado una contraseña antigua.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="DefaultMessage">Nombre de usuario o contraseña no válidos.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfUserAccountDisabled">Su cuenta se ha bloqueado. Contacte con la persona responsable de soporte técnico para desbloquearla y vuelva a intentarlo.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfUserAccountLocked">Su cuenta se bloqueó temporalmente para impedir un uso no autorizado. Vuelva a intentarlo más tarde.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="AADRequestsThrottled">Hay demasiadas solicitudes en este momento. Espere un poco y vuelva a intentarlo.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!--Local account sign-up or sign-in page French-->
      <LocalizedResources Id="api.signuporsignin.fr">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Bienvenue chez Dornbracht!</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="signInName" StringId="DisplayName">E-Mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="password" StringId="DisplayName">Mot de passe</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="invalid_generic">Saisir l'adresse e-mail</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="createaccount_intro">Nouveau chez Dornbracht?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="createaccount_one_link">S'inscrire maintenant</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="forgotpassword_link">Vous avez oublié votre mot de passe?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="unknown_error">Nous rencontrons des problèmes pour vous connecter. Veuillez réessayer plus tard.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidPassword">Votre mot de passe est incorrect.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfPasswordExpired">Votre mot de passe a expiré.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalDoesNotExist">Nous ne trouvons pas votre compte.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfOldPasswordUsed">Apparemment, vous utilisez un ancien mot de passe.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="DefaultMessage">Nom d’utilisateur ou mot de passe non valide.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfUserAccountDisabled">Votre compte a été verrouillé. Contactez votre support technique pour le déverrouiller, puis réessayez.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfUserAccountLocked">Votre compte est temporairement verrouillé pour éviter toute utilisation non autorisée. Réessayez plus tard.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="AADRequestsThrottled">Il y a trop de demandes pour l’instant. Veuillez patienter quelques instants, puis réessayez.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!--Local account sign-up or sign-in page Italian-->
      <LocalizedResources Id="api.signuporsignin.it">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Benvenuti a Dornbracht!</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="signInName" StringId="DisplayName">E-Mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="password" StringId="DisplayName">Password</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="invalid_generic">Inserire l'indirizzo e-mail</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="createaccount_intro">Siete nuovi a Dornbracht?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="createaccount_one_link">Registrati ora</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="forgotpassword_link">Avete dimenticato la password?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="unknown_error">Si è verificato un errore durante l'accesso. Riprovare più tardi.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidPassword">La password non è corretta.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfPasswordExpired">La password è scaduta.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalDoesNotExist">L'account non è stato trovato.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfOldPasswordUsed">È stata usata una password non aggiornata.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="DefaultMessage">Nome utente o password non validi.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfUserAccountDisabled">L'account è stato bloccato. Contattare il supporto tecnico per sbloccarlo, quindi riprovare.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfUserAccountLocked">L'account è stato temporaneamente bloccato per impedirne l'uso non autorizzato. Riprovare più tardi.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="AADRequestsThrottled">	In questo momento sono presenti troppe richieste. Attendere qualche minuto e riprovare.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!--Local account sign-up page English-->
      <LocalizedResources Id="api.localaccountsignup.en">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Create your own account now.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="initial_intro">The email address has been verified. You can continue the process now.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_personal_data" StringId="DisplayName">Personal Data</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_contact" StringId="DisplayName">Contact</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="readonlyEmail" StringId="DisplayName">Email address</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="DisplayName">Salutation</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="UserHelpText">Select salutation</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="DisplayName">Last Name</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="UserHelpText">Enter last name</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="DisplayName">First Name</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="UserHelpText">Enter first name</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="DisplayName">Company</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="UserHelpText">Enter company</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="DisplayName">Profession</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="UserHelpText">Select profession</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="DisplayName">Country</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="UserHelpText">Select country</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="DisplayName">Telephone</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="UserHelpText">Enter phone number</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_agreeTerms" StringId="DisplayName">#EN Terms &amp; Conditions</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">New Password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Enter new password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="PatternHelpText">8-16 characters, containing 3 out of 4 of the following: Lowercase characters, uppercase characters, digits (0-9), and one or more of the following symbols: @ # $ % ^ &amp; * - _ + = [ ] { } | \ : ' , ? / ` ~ " ( ) ; .</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="DisplayName">Confirm New Password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="UserHelpText">Confirm new password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="PatternHelpText">8-16 characters, containing 3 out of 4 of the following: Lowercase characters, uppercase characters, digits (0-9), and one or more of the following symbols: @ # $ % ^ &amp; * - _ + = [ ] { } | \ : ' , ? / ` ~ " ( ) ; .</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="displayName" StringId="DisplayName">Display Name</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="displayName" StringId="UserHelpText">Your display name.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_fieldIncorrect">One or more fields are filled out incorrectly. Please check your entries and try again.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_passwordEntryMismatch">The password entry fields do not match. Please enter the same password in both fields and try again.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_requiredFieldMissing">A required field is missing. Please fill out all required fields and try again.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="helplink_text">What is this?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="preloader_alt">Please wait</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="required_field">This information is required.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Cancel</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="ServiceThrottled">There are too many requests at this moment. Please wait for some time and try again.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimNotVerified">Claim not verified: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalAlreadyExists">A user with the specified ID already exists. Please choose a different one.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfIncorrectPattern">Incorrect pattern for: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidInput">{0} has invalid input.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMissingRequiredElement">Missing required element: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfValidationError">Error in validation by: {0}</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!--Local account sign-up page German-->
      <LocalizedResources Id="api.localaccountsignup.de">
        <LocalizedCollections>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" TargetCollection="Restriction">
            <Item Text="männlich" Value="1" />
            <Item Text="weiblich" Value="2" />
            <Item Text="divers" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" TargetCollection="Restriction">
            <Item Text="Architektur/ Innenarchitektur/ Design" Value="*********" />
            <Item Text="Installationsbetriebe / Fachpartner" Value="*********" />
            <Item Text="Privater Bedarf" Value="*********" />
            <Item Text="Investition / Baugewerbe" Value="*********" />
            <Item Text="Handel" Value="*********" />
            <Item Text="Hotel" Value="*********" />
            <Item Text="Sonstiges" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="country" TargetCollection="Restriction">
            <Item Text="Österreich" Value="AT" />
            <Item Text="Belgien" Value="BE" />
            <Item Text="Kanada" Value="CA" />
            <Item Text="China" Value="CN" />
            <Item Text="Tschechische Republik" Value="CZ" />
            <Item Text="Dänemark" Value="DK" />
            <Item Text="Deutschland" Value="DE" />
            <Item Text="Finnland" Value="FI" />
            <Item Text="Frankreich" Value="FR" />
            <Item Text="International" Value="XY" />
            <Item Text="Italien" Value="IT" />
            <Item Text="Mexiko" Value="MX" />
            <Item Text="Niederlande" Value="NL" />
            <Item Text="Norwegen" Value="NO" />
            <Item Text="Polen" Value="PL" />
            <Item Text="Portugal" Value="PT" />
            <Item Text="Russland" Value="RU" />
            <Item Text="Spanien" Value="ES" />
            <Item Text="Schweden" Value="SE" />
            <Item Text="Schweiz" Value="CH" />
            <Item Text="Türkei" Value="TR" />
            <Item Text="Vereinigte Arabische Emirate" Value="AE" />
            <Item Text="Großbritannien" Value="GB" />
            <Item Text="Vereinigte Staaten" Value="US" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_agreeTerms" TargetCollection="Restriction">
            <Item Text="Hiermit stimme ich den {Nutzungsbedingungen} und {Datenschutzbestimmungen} von Dornbracht zu." Value="true" />
          </LocalizedCollection>
        </LocalizedCollections>
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Erstellen Sie jetzt Ihr eigenes Konto.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="initial_intro">Die E-Mail-Adresse wurde verifiziert. Sie können den Vorgang jetzt fortsetzen.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_personal_data" StringId="DisplayName">Persönliche Daten</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_contact" StringId="DisplayName">Kontakt</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="readonlyEmail" StringId="DisplayName">E-Mail-Adresse</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="DisplayName">Anrede</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="UserHelpText">Anrede auswählen</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="DisplayName">Nachname</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="UserHelpText">Nachname eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="DisplayName">Vorname</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="UserHelpText">Vorname eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="DisplayName">Firma</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="UserHelpText">Firma eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="DisplayName">Tätigkeit</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="UserHelpText">Tätigkeit auswählen</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="DisplayName">Land</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="UserHelpText">Land auswählen</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="DisplayName">Telefon</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="UserHelpText">Telefonnummer eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_agreeTerms" StringId="DisplayName">#DE Terms &amp; Conditions</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">Neues Passwort</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Neues Passwort eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="PatternHelpText">8–16 Zeichen, es müssen Zeichen aus mindestens 3 der folgenden 4 Gruppen enthalten sein: Kleinbuchstaben, Großbuchstaben, Ziffern (0–9) und mindestens eines der folgenden Symbole: @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="DisplayName">Passwortbestätigung</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="UserHelpText">Neues Passwort bestätigen</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="PatternHelpText">8–16 Zeichen, es müssen Zeichen aus mindestens 3 der folgenden 4 Gruppen enthalten sein: Kleinbuchstaben, Großbuchstaben, Ziffern (0–9) und mindestens eines der folgenden Symbole: @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Weiter</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_fieldIncorrect">Mindestens ein Feld ist nicht richtig ausgefüllt. Überprüfen Sie Ihre Eingaben, und versuchen Sie es noch mal.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_passwordEntryMismatch">Die Kennwörter stimmen nicht überein. Geben Sie in beiden Feldern das gleiche Kennwort ein, und versuchen Sie es noch mal.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_requiredFieldMissing">Ein erforderliches Feld fehlt. Füllen Sie alle erforderlichen Felder aus, und versuchen Sie es noch mal.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="helplink_text">Was ist das?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="preloader_alt">Bitte warten</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="required_field">Diese Informationen sind erforderlich.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="ServiceThrottled">Momentan sind zu viele Anforderungen vorhanden. Warten Sie einige Zeit, und versuchen Sie es noch mal.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimNotVerified">Anspruch nicht überprüft: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalAlreadyExists">Es ist bereits ein Benutzer mit der angegebenen ID vorhanden. Wählen Sie eine andere ID.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfIncorrectPattern">Ungültiges Muster für {0}.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidInput">{0} weist eine ungültige Eingabe auf.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMissingRequiredElement">Erforderliches Element fehlt: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfValidationError">Fehler bei der Überprüfung durch {0}.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!--Local account sign-up page Spanish-->
      <LocalizedResources Id="api.localaccountsignup.es">
        <LocalizedCollections>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" TargetCollection="Restriction">
            <Item Text="masculino" Value="1" />
            <Item Text="femenino" Value="2" />
            <Item Text="diversos" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" TargetCollection="Restriction">
            <Item Text="Arquitectura / Diseño" Value="*********" />
            <Item Text="Fontanería" Value="*********" />
            <Item Text="Uso privado" Value="*********" />
            <Item Text="Inversor / Promotor" Value="*********" />
            <Item Text="Comercio" Value="*********" />
            <Item Text="Hotel" Value="*********" />
            <Item Text="Otro" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="country" TargetCollection="Restriction">
            <Item Text="Alemania" Value="DE" />
            <Item Text="Austria" Value="AT" />
            <Item Text="Bélgica" Value="BE" />
            <Item Text="Canadá" Value="CA" />
            <Item Text="China" Value="CN" />
            <Item Text="República Checa" Value="CZ" />
            <Item Text="Dinamarca" Value="DK" />
            <Item Text="Finlandia" Value="FI" />
            <Item Text="Francia" Value="FR" />
            <Item Text="Internacional" Value="XY" />
            <Item Text="Italia" Value="IT" />
            <Item Text="México" Value="MX" />
            <Item Text="los Países Bajos" Value="NL" />
            <Item Text="Noruega" Value="NO" />
            <Item Text="Polonia" Value="PL" />
            <Item Text="Portugal" Value="PT" />
            <Item Text="Rusia" Value="RU" />
            <Item Text="España" Value="ES" />
            <Item Text="Suecia" Value="SE" />
            <Item Text="Suiza" Value="CH" />
            <Item Text="Turquía" Value="TR" />
            <Item Text="Emiratos Árabes Unidos" Value="AE" />
            <Item Text="Gran Bretaña" Value="GB" />
            <Item Text="Estados Unidos" Value="US" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_agreeTerms" TargetCollection="Restriction">
            <Item Text="Acepto las {condiciones de uso} y las {disposiciones de protección de datos} de Dornbracht." Value="true" />
          </LocalizedCollection>
        </LocalizedCollections>
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Cree su propia cuenta ahora.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="initial_intro">La dirección de correo electrónico ha sido verificada. Ahora puede continuar el proceso.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="readonlyEmail" StringId="DisplayName">Correo electrónico</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="DisplayName">Tratamiento</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="DisplayName">Apellidos</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="UserHelpText">Introduzca el apellido</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="DisplayName">Nombre</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="UserHelpText">Introduzca el nombre</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="DisplayName">Empresa</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="UserHelpText">Introduzca la empresa</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="DisplayName">Profesión</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="UserHelpText">Seleccione la profesión</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="DisplayName">Teléfono</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="UserHelpText">Introduzca el número de teléfono</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_agreeTerms" StringId="DisplayName">#ES Terms of Use</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">Nueva contraseña</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Introduzca la nueva contraseña</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="PatternHelpText">De 8 a 16 caracteres, que contengan 3 de los 4 tipos siguientes: caracteres en minúsculas, caracteres en mayúsculas, dígitos (0-9) y uno o más de los siguientes símbolos: @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="DisplayName">Confirmación de la contraseña</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="UserHelpText">Confirmar nueva contraseña</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="PatternHelpText">8 a 16 caracteres, que contengan 3 de los 4 tipos siguientes: caracteres en minúsculas, caracteres en mayúsculas, dígitos (0-9) y uno o más de los siguientes símbolos: @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Siguiente</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_fieldIncorrect">Hay uno o varios campos rellenados de forma incorrecta. Compruebe las entradas y vuelva a intentarlo.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_passwordEntryMismatch">Los campos de entrada de contraseña no coinciden. Escriba la misma contraseña en ambos campos y vuelva a intentarlo.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_requiredFieldMissing">Falta un campo obligatorio. Rellene todos los campos necesarios y vuelva a intentarlo.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="helplink_text">¿Qué es esto?</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="preloader_alt">Espere</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="required_field">Esta información es obligatoria.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="ServiceThrottled">Hay demasiadas solicitudes en este momento. Espere un momento y vuelva a intentarlo.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimNotVerified">Reclamación no comprobada: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalAlreadyExists">Ya existe un usuario con el id. especificado. Elija otro diferente.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfIncorrectPattern">Patrón incorrecto para: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidInput">{0} tiene una entrada no válida.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMissingRequiredElement">Falta un elemento obligatorio: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfValidationError">Error en la validación de: {0}</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!--Local account sign-up page French-->
      <LocalizedResources Id="api.localaccountsignup.fr">
        <LocalizedCollections>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" TargetCollection="Restriction">
            <Item Text="masculin" Value="1" />
            <Item Text="féminin" Value="2" />
            <Item Text="divers" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" TargetCollection="Restriction">
            <Item Text="Architecture / Design" Value="*********" />
            <Item Text="Plomberie" Value="*********" />
            <Item Text="Usage privé" Value="*********" />
            <Item Text="Investisseur / Développeur" Value="*********" />
            <Item Text="Commerce" Value="*********" />
            <Item Text="Hôtel" Value="*********" />
            <Item Text="Autre" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="country" TargetCollection="Restriction">
            <Item Text="Autriche" Value="AT" />
            <Item Text="Belgique" Value="BE" />
            <Item Text="Canada" Value="CA" />
            <Item Text="Chine" Value="CN" />
            <Item Text="République tchèque" Value="CZ" />
            <Item Text="Danemark" Value="DK" />
            <Item Text="Allemagne" Value="DE" />
            <Item Text="Finlande" Value="FI" />
            <Item Text="France" Value="FR" />
            <Item Text="International" Value="XY" />
            <Item Text="Italie" Value="IT" />
            <Item Text="Mexique" Value="MX" />
            <Item Text="Pays-Bas" Value="NL" />
            <Item Text="Norvège" Value="NO" />
            <Item Text="Pologne" Value="PL" />
            <Item Text="Portugal" Value="PT" />
            <Item Text="Russie" Value="RU" />
            <Item Text="Espagne" Value="ES" />
            <Item Text="Suède" Value="SE" />
            <Item Text="Suisse" Value="CH" />
            <Item Text="Turquie" Value="TR" />
            <Item Text="Émirats arabes unis" Value="AE" />
            <Item Text="Royaume-Uni" Value="GB" />
            <Item Text="États-Unis" Value="US" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_agreeTerms" TargetCollection="Restriction">
            <Item Text="Par la présente, j’accepte les {conditions d’utilisation} et la {politique de confidentialité} de Dornbracht." Value="true" />
          </LocalizedCollection>
        </LocalizedCollections>
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Créez votre propre compte maintenant.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="initial_intro">L'adresse e-mail a été vérifiée. Vous pouvez maintenant poursuivre la procédure.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_personal_data" StringId="DisplayName">Données personnelles</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_contact" StringId="DisplayName">Contact</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="readonlyEmail" StringId="DisplayName">E-Mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="DisplayName">Civilité</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="UserHelpText">Choisir la civilité</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="DisplayName">Nom</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="UserHelpText">Saisir le nom</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="DisplayName">Prénom</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="UserHelpText">Saisir le prénom</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="DisplayName">Société</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="UserHelpText">Saisir l'entreprise</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="DisplayName">Profession</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="UserHelpText">Sélectionner la profession</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="DisplayName">Pays</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="UserHelpText">Sélectionner le pays</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="DisplayName">Téléphone</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="UserHelpText">Saisir le numéro de téléphone</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_agreeTerms" StringId="DisplayName">#FR Terms &amp; Conditions</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">Nouveau mot de passe</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Saisir un nouveau mot de passe</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Suivant</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="ServiceThrottled">Il y a trop de demandes pour l’instant. Veuillez patienter quelques instants, puis réessayez.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimNotVerified">Revendication non vérifiée : {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalAlreadyExists">Un utilisateur avec l’ID spécifié existe déjà. Choisissez-en un autre.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfIncorrectPattern">Modèle incorrect pour : {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidInput">{0} a une entrée non valide.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMissingRequiredElement">Élément requis manquant : {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfValidationError">Erreur lors de la validation par : {0}</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!--Local account sign-up page Itlian-->
      <LocalizedResources Id="api.localaccountsignup.it">
        <LocalizedCollections>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" TargetCollection="Restriction">
            <Item Text="maschile" Value="1" />
            <Item Text="femminile" Value="2" />
            <Item Text="diverso" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" TargetCollection="Restriction">
            <Item Text="Architettura / Design" Value="*********" />
            <Item Text="Impianto idraulico" Value="*********" />
            <Item Text="Uso privato" Value="*********" />
            <Item Text="Investitore / Sviluppatore" Value="*********" />
            <Item Text="Commercio" Value="*********" />
            <Item Text="Hotel" Value="*********" />
            <Item Text="Altro" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="country" TargetCollection="Restriction">
            <Item Text="Austria" Value="AT" />
            <Item Text="Belgio" Value="BE" />
            <Item Text="Canada" Value="CA" />
            <Item Text="China" Value="CN" />
            <Item Text="Repubblica Ceca" Value="CZ" />
            <Item Text="Danimarca" Value="DK" />
            <Item Text="Germania" Value="DE" />
            <Item Text="Finlandia" Value="FI" />
            <Item Text="Francia" Value="FR" />
            <Item Text="Internazionale" Value="XY" />
            <Item Text="Italia" Value="IT" />
            <Item Text="Messico" Value="MX" />
            <Item Text="Paesi Bassi" Value="NL" />
            <Item Text="Norvegia" Value="NO" />
            <Item Text="Polonia" Value="PL" />
            <Item Text="Portogallo" Value="PT" />
            <Item Text="Russia" Value="RU" />
            <Item Text="Spagna" Value="ES" />
            <Item Text="Svezia" Value="SE" />
            <Item Text="Svizzera" Value="CH" />
            <Item Text="Turkey" Value="TR" />
            <Item Text="Emirati Arabi Uniti" Value="AE" />
            <Item Text="Regno Unito" Value="GB" />
            <Item Text="Stati Uniti" Value="US" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_agreeTerms" TargetCollection="Restriction">
            <Item Text="Acconsento alle {condizioni di utilizzo} e alle {disposizioni sulla privacy} di Dornbracht." Value="true" />
          </LocalizedCollection>
        </LocalizedCollections>
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Create subito il vostro account.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="initial_intro">L'indirizzo e-mail è stato verificato. A questo punto è possibile continuare il processo.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_personal_data" StringId="DisplayName">Dati personali</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_contact" StringId="DisplayName">Contatto</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="readonlyEmail" StringId="DisplayName">E-Mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="DisplayName">Intestazione</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="UserHelpText">Selezionare il saluto</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="DisplayName">Nome</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="UserHelpText">Saisir le nom</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="DisplayName">Cognome</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="UserHelpText">Inserire il cognome</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="DisplayName">Azienda</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="UserHelpText">Inserire l'azienda</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="DisplayName">Professione</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="UserHelpText">Selezionare la professione</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="DisplayName">Paese</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="UserHelpText">Selezionare il Paese</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="DisplayName">Telefono</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="UserHelpText">Inserire il numero di telefono</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_agreeTerms" StringId="DisplayName">#FR Terms &amp; Conditions</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="email" StringId="DisplayName">E-Mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">Nuova password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Inserire la nuova password</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Prossimo</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="ServiceThrottled"></LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimNotVerified">Attestazione non verificata: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalAlreadyExists">Un utente con il nome specificato esiste già. Sceglierne un altro.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfIncorrectPattern">Modello non corretto per: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfInvalidInput">{0} contiene un input non valido.</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfMissingRequiredElement">Manca un elemento obbligatorio: {0}</LocalizedString>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfValidationError">Errore durante la convalida da: {0}</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Self-asserted page English-->
      <LocalizedResources Id="api.selfasserted.en">
        <LocalizedStrings>
          <LocalizedString ElementType="ErrorMessage" StringId="UserMessageIfClaimsPrincipalAlreadyExists">You are already registered, please press the back button and sign in instead.</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Password reset page English-->
      <LocalizedResources Id="api.localaccountpasswordreset.en">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Change password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">New Password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Enter new password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="PatternHelpText">8-16 characters, containing 3 out of 4 of the following: Lowercase characters, uppercase characters, digits (0-9), and one or more of the following symbols: @ # $ % ^ &amp; * - _ + = [ ] { } | \ : ' , ? / ` ~ " ( ) ; .</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="DisplayName">Confirm New Password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="UserHelpText">Confirm new password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="PatternHelpText">8-16 characters, containing 3 out of 4 of the following: Lowercase characters, uppercase characters, digits (0-9), and one or more of the following symbols: @ # $ % ^ &amp; * - _ + = [ ] { } | \ : ' , ? / ` ~ " ( ) ; .</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_passwordEntryMismatch">The password entry fields do not match. Please enter the same password in both fields and try again.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Confirm</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Password reset page German -->
      <LocalizedResources Id="api.localaccountpasswordreset.de">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Passwort ändern</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">Neues Passwort</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Neues Passwort eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="PatternHelpText">8–16 Zeichen, es müssen Zeichen aus mindestens 3 der folgenden 4 Gruppen enthalten sein: Kleinbuchstaben, Großbuchstaben, Ziffern (0–9) und mindestens eines der folgenden Symbole: @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="DisplayName">Passwortbestätigung</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="UserHelpText">Neues Passwort bestätigen</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="PatternHelpText">8–16 Zeichen, es müssen Zeichen aus mindestens 3 der folgenden 4 Gruppen enthalten sein: Kleinbuchstaben, Großbuchstaben, Ziffern (0–9) und mindestens eines der folgenden Symbole: @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_passwordEntryMismatch">Die Kennwörter stimmen nicht überein. Geben Sie in beiden Feldern das gleiche Kennwort ein, und versuchen Sie es erneut.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Bestätigen</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Password reset page Spanish -->
      <LocalizedResources Id="api.localaccountpasswordreset.es">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Cambiar contraseña</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">Nueva contraseña</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Introduzca la nueva contraseña</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="PatternHelpText">De 8 a 16 caracteres, que contengan 3 de los 4 tipos siguientes: caracteres en minúsculas, caracteres en mayúsculas, dígitos (0-9) y uno o más de los siguientes símbolos: @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="DisplayName">Confirmación de la contraseña</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="UserHelpText">Confirmar la nueva contraseña</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="PatternHelpText">8 a 16 caracteres, que contengan 3 de los 4 tipos siguientes: caracteres en minúsculas, caracteres en mayúsculas, dígitos (0-9) y uno o más de los siguientes símbolos: @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_passwordEntryMismatch">Los campos de entrada de contraseña no coinciden. Escriba la misma contraseña en ambos campos y vuelva a intentarlo.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Confirmar</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Password reset page French -->
      <LocalizedResources Id="api.localaccountpasswordreset.fr">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Modifier le mot de passe</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">Nouveau mot de passe</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Saisir un nouveau mot de passe</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="PatternHelpText">8-16 caractères, il doit contenir des caractères d'au moins 3 des 4 groupes suivants : lettres minuscules, lettres majuscules, chiffres (0-9) et au moins un des symboles suivants : @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="DisplayName">Confirmation de votre mot de passe</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="UserHelpText">Confirmer le nouveau mot de passe</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="PatternHelpText">8-16 caractères, il doit contenir des caractères d'au moins 3 des 4 groupes suivants : lettres minuscules, lettres majuscules, chiffres (0-9) et au moins un des symboles suivants : @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_passwordEntryMismatch">Les champs d’entrée de mot de passe ne correspondent pas. Entrez le même mot de passe dans les deux champs et réessayez.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Confirmer</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Password reset page Italian -->
      <LocalizedResources Id="api.localaccountpasswordreset.it">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Modifica della password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="DisplayName">Nuova password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="UserHelpText">Inserire la nuova password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="newPassword" StringId="PatternHelpText">8-16 caratteri, deve contenere caratteri di almeno 3 dei seguenti 4 gruppi: lettere minuscole, lettere maiuscole, cifre (0-9) e almeno uno dei seguenti simboli: @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="DisplayName">Conferma della password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="UserHelpText">Confermare la nuova password</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="reenterPassword" StringId="PatternHelpText">8-16 caratteri, deve contenere caratteri di almeno 3 dei seguenti 4 gruppi: lettere minuscole, lettere maiuscole, cifre (0-9) e almeno uno dei seguenti simboli: @ # $ % ^ &amp; * - _ + = [ ] { } | \\ : ' , ? / ` ~ \" ( ) ; .</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="error_passwordEntryMismatch">I campi di immissione della password non corrispondono. Immettere la stessa password in entrambi i campi e riprovare.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Confermare</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>

      <!-- Edit profile sign-in page English-->
      <LocalizedResources Id="api.idpselections.en">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="intro">Sign in</LocalizedString>
          <LocalizedString ElementType="ClaimsProvider" StringId="LocalAccountSigninEmailExchange">Local Account Signin</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>

      <!-- Edit profile sign-in with local account English-->
      <LocalizedResources Id="api.localaccountsignin.en">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Welcome to Dornbracht!</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="initial_intro"></LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="signInName" StringId="DisplayName">Email Address</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="password" StringId="DisplayName">Password</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Log in</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Cancel</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Edit profile sign-in with local account German-->
      <LocalizedResources Id="api.localaccountsignin.de">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Willkommen bei Dornbracht!</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="initial_intro"></LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="signInName" StringId="DisplayName">E-Mail-Adresse</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="password" StringId="DisplayName">Passwort</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Einloggen</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Abbrechen</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Edit profile sign-in with local account French-->
      <LocalizedResources Id="api.localaccountsignin.fr">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Bienvenue chez Dornbracht!</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="initial_intro"></LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="signInName" StringId="DisplayName">E-Mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="password" StringId="DisplayName">Mot de passe</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Se connecter</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Annuler</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Edit profile sign-in with local account Italian -->
      <LocalizedResources Id="api.localaccountsignin.it">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Benvenuti a Dornbracht!</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="initial_intro"></LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="signInName" StringId="DisplayName">E-Mail</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="password" StringId="DisplayName">Password</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Accedi</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Annulla</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Edit profile sign-in with local account Spanish -->
      <LocalizedResources Id="api.localaccountsignin.es">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">¡Bienvenido a Dornbracht!</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="initial_intro"></LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="signInName" StringId="DisplayName">Correo electrónico</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="password" StringId="DisplayName">Contraseña</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Entrar en el sistema</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Cancelar</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>

      <!-- Edit profile page English-->
      <LocalizedResources Id="api.selfasserted.profileupdate.en">
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Edit profile</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_personal_data" StringId="DisplayName">Personal Data</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_contact" StringId="DisplayName">Contact</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="displayName" StringId="DisplayName">Display Name</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="displayName" StringId="UserHelpText">Your display name.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="DisplayName">Salutation</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="UserHelpText">Select salutation</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="DisplayName">Nachname</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="UserHelpText">Nachname eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="DisplayName">First Name</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="UserHelpText">Enter first name</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="DisplayName">Company</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="UserHelpText">Enter company</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="DisplayName">Profession</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="UserHelpText">Select profession</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="postalCode" StringId="DisplayName">ZIP Code</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="postalCode" StringId="UserHelpText">Enter postal code</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="city" StringId="DisplayName">City</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="city" StringId="UserHelpText">Enter city</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="state" StringId="DisplayName">State</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="state" StringId="UserHelpText">Enter state</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="DisplayName">Telephone</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="UserHelpText">Enter phone number</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="streetAddress" StringId="DisplayName">Street and no.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="streetAddress" StringId="UserHelpText">Enter street and no.</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Save</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Cancel</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Edit profile page German-->
      <LocalizedResources Id="api.selfasserted.profileupdate.de">
        <LocalizedCollections>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" TargetCollection="Restriction">
            <Item Text="männlich" Value="1" />
            <Item Text="weiblich" Value="2" />
            <Item Text="divers" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" TargetCollection="Restriction">
            <Item Text="Architektur/ Innenarchitektur/ Design" Value="*********" />
            <Item Text="Installationsbetriebe / Fachpartner" Value="*********" />
            <Item Text="Privater Bedarf" Value="*********" />
            <Item Text="Investition / Baugewerbe" Value="*********" />
            <Item Text="Handel" Value="*********" />
            <Item Text="Hotel" Value="*********" />
            <Item Text="Sonstiges" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="country" TargetCollection="Restriction">
            <Item Text="Österreich" Value="AT" />
            <Item Text="Belgien" Value="BE" />
            <Item Text="Kanada" Value="CA" />
            <Item Text="China" Value="CN" />
            <Item Text="Tschechische Republik" Value="CZ" />
            <Item Text="Dänemark" Value="DK" />
            <Item Text="Finnland" Value="FI" />
            <Item Text="Frankreich" Value="FR" />
            <Item Text="Deutschland" Value="DE" />
            <Item Text="International" Value="XY" />
            <Item Text="Italien" Value="IT" />
            <Item Text="Mexiko" Value="MX" />
            <Item Text="Niederlande" Value="NL" />
            <Item Text="Norwegen" Value="NO" />
            <Item Text="Polen" Value="PL" />
            <Item Text="Portugal" Value="PT" />
            <Item Text="Russland" Value="RU" />
            <Item Text="Spanien" Value="ES" />
            <Item Text="Schweden" Value="SE" />
            <Item Text="Schweiz" Value="CH" />
            <Item Text="Türkei" Value="TR" />
            <Item Text="Vereinigte Arabische Emirate" Value="AE" />
            <Item Text="Großbritannien" Value="GB" />
            <Item Text="Vereinigte Staaten" Value="US" />
          </LocalizedCollection>
        </LocalizedCollections>
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Profil bearbeiten</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_personal_data" StringId="DisplayName">Persönliche Daten</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_contact" StringId="DisplayName">Kontakt</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="DisplayName">Anrede</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="UserHelpText">Anrede auswählen</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="DisplayName">Nachname</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="UserHelpText">Nachname eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="DisplayName">Vorname</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="UserHelpText">Vorname eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="DisplayName">Firma</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="UserHelpText">Firma eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="DisplayName">Tätigkeit</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="UserHelpText">Tätigkeit auswählen</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="postalCode" StringId="DisplayName">Postleitzahl</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="postalCode" StringId="UserHelpText">Postleitzahl eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="city" StringId="DisplayName">Stadt</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="city" StringId="UserHelpText">Stadt eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="state" StringId="DisplayName">Bundesland</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="state" StringId="UserHelpText">Bundesland eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="DisplayName">Land</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="DisplayName">Telefon</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="UserHelpText">Telefonnummer eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="streetAddress" StringId="DisplayName">Straße und Nr.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="streetAddress" StringId="UserHelpText">Straße und Nr. eingeben</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Speichern</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Abbrechen</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Edit profile page Spanish-->
      <LocalizedResources Id="api.selfasserted.profileupdate.es">
        <LocalizedCollections>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" TargetCollection="Restriction">
            <Item Text="masculino" Value="1" />
            <Item Text="femenino" Value="2" />
            <Item Text="diversos" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" TargetCollection="Restriction">
            <Item Text="Arquitectura / Diseño" Value="*********" />
            <Item Text="Fontanería" Value="*********" />
            <Item Text="Uso privado" Value="*********" />
            <Item Text="Inversor / Promotor" Value="*********" />
            <Item Text="Comercio" Value="*********" />
            <Item Text="Hotel" Value="*********" />
            <Item Text="Otro" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="country" TargetCollection="Restriction">
            <Item Text="Alemania" Value="DE" />
            <Item Text="Austria" Value="AT" />
            <Item Text="Bélgica" Value="BE" />
            <Item Text="Canadá" Value="CA" />
            <Item Text="China" Value="CN" />
            <Item Text="República Checa" Value="CZ" />
            <Item Text="Dinamarca" Value="DK" />
            <Item Text="Finlandia" Value="FI" />
            <Item Text="Francia" Value="FR" />
            <Item Text="Internacional" Value="XY" />
            <Item Text="Italia" Value="IT" />
            <Item Text="México" Value="MX" />
            <Item Text="los Países Bajos" Value="NL" />
            <Item Text="Noruega" Value="NO" />
            <Item Text="Polonia" Value="PL" />
            <Item Text="Portugal" Value="PT" />
            <Item Text="Rusia" Value="RU" />
            <Item Text="España" Value="ES" />
            <Item Text="Suecia" Value="SE" />
            <Item Text="Suiza" Value="CH" />
            <Item Text="Turquía" Value="TR" />
            <Item Text="Emiratos Árabes Unidos" Value="AE" />
            <Item Text="Gran Bretaña" Value="GB" />
            <Item Text="Estados Unidos" Value="US" />
          </LocalizedCollection>
        </LocalizedCollections>
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Editar perfil</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_personal_data" StringId="DisplayName">Datos personales</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_contact" StringId="DisplayName">Contacto</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="DisplayName">Tratamiento</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="UserHelpText">Seleccione el tratamiento</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="DisplayName">Apellidos</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="UserHelpText">Introduzca el apellido</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="DisplayName">Nombre</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="UserHelpText">Introduzca el nombre</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="DisplayName">Empresa</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="UserHelpText">Introduzca la empresa</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="DisplayName">Profesión</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="UserHelpText">Seleccione la profesión</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="postalCode" StringId="DisplayName">Código postal</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="postalCode" StringId="UserHelpText">Introduzca el código postal</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="city" StringId="DisplayName">Localidad</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="city" StringId="UserHelpText">Introduzca la localidad</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="state" StringId="DisplayName">Estado</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="state" StringId="UserHelpText">Introduzca el estado</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="DisplayName">País</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="UserHelpText">Seleccione el país</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="DisplayName">Teléfono</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="UserHelpText">Introduzca el número de teléfono</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="streetAddress" StringId="DisplayName">Calle y número</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="streetAddress" StringId="UserHelpText">Introduzca la calle y el número</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Guardar</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Cancelar</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Edit profile page French-->
      <LocalizedResources Id="api.selfasserted.profileupdate.fr">
        <LocalizedCollections>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" TargetCollection="Restriction">
            <Item Text="masculin" Value="1" />
            <Item Text="féminin" Value="2" />
            <Item Text="divers" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" TargetCollection="Restriction">
            <Item Text="Architecture / Design" Value="*********" />
            <Item Text="Plomberie" Value="*********" />
            <Item Text="Usage privé" Value="*********" />
            <Item Text="Investisseur / Développeur" Value="*********" />
            <Item Text="Commerce" Value="*********" />
            <Item Text="Hôtel" Value="*********" />
            <Item Text="Autre" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="country" TargetCollection="Restriction">
            <Item Text="Allemagne" Value="DE" />
            <Item Text="Autriche" Value="AT" />
            <Item Text="Belgique" Value="BE" />
            <Item Text="Canada" Value="CA" />
            <Item Text="Chine" Value="CN" />
            <Item Text="République tchèque" Value="CZ" />
            <Item Text="Danemark" Value="DK" />
            <Item Text="Espagne" Value="ES" />
            <Item Text="Finlande" Value="FI" />
            <Item Text="France" Value="FR" />
            <Item Text="International" Value="XY" />
            <Item Text="Italie" Value="IT" />
            <Item Text="Mexique" Value="MX" />
            <Item Text="Pays-Bas" Value="NL" />
            <Item Text="Norvège" Value="NO" />
            <Item Text="Pologne" Value="PL" />
            <Item Text="Portugal" Value="PT" />
            <Item Text="Russie" Value="RU" />
            <Item Text="Suède" Value="SE" />
            <Item Text="Suisse" Value="CH" />
            <Item Text="Turquie" Value="TR" />
            <Item Text="Émirats arabes unis" Value="AE" />
            <Item Text="Royaume-Uni" Value="GB" />
            <Item Text="États-Unis" Value="US" />
          </LocalizedCollection>
        </LocalizedCollections>
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Modifier mon profil</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_personal_data" StringId="DisplayName">Données personnelles</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_contact" StringId="DisplayName">Contact</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="DisplayName">Civilité</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="UserHelpText">Choisir la civilité</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="DisplayName">Nom</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="UserHelpText">Saisir le nom</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="DisplayName">Prénom</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="UserHelpText">Saisir le prénom</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="DisplayName">Société</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="UserHelpText">Saisir l'entreprise</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="DisplayName">Profession</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="UserHelpText">Sélectionner la profession</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="postalCode" StringId="DisplayName">Code postal</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="postalCode" StringId="UserHelpText">Saisir le code postal</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="city" StringId="DisplayName">Ville</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="city" StringId="UserHelpText">Saisir la ville</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="state" StringId="DisplayName">#Bundesland</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="state" StringId="UserHelpText">#Bundesland eingeben</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="DisplayName">Pays</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="UserHelpText">Saisir le pays</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="DisplayName">Téléphone</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="UserHelpText">Saisir le numéro de téléphone</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="streetAddress" StringId="DisplayName">Rue et numéro</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="streetAddress" StringId="UserHelpText">Saisir la rue et le numéro</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Enregistrer</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Annuler</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
      <!-- Edit profile page Italian-->
      <LocalizedResources Id="api.selfasserted.profileupdate.it">
        <LocalizedCollections>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" TargetCollection="Restriction">
            <Item Text="maschile" Value="1" />
            <Item Text="femminile" Value="2" />
            <Item Text="diverso" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" TargetCollection="Restriction">
            <Item Text="Architettura / Design" Value="*********" />
            <Item Text="Impianto idraulico" Value="*********" />
            <Item Text="Uso privato" Value="*********" />
            <Item Text="Investitore / Sviluppatore" Value="*********" />
            <Item Text="Commercio" Value="*********" />
            <Item Text="Hotel" Value="*********" />
            <Item Text="Altro" Value="*********" />
          </LocalizedCollection>
          <LocalizedCollection ElementType="ClaimType" ElementId="country" TargetCollection="Restriction">
            <Item Text="Austria" Value="AT" />
            <Item Text="Belgio" Value="BE" />
            <Item Text="Canada" Value="CA" />
            <Item Text="China" Value="CN" />
            <Item Text="Repubblica Ceca" Value="CZ" />
            <Item Text="Danimarca" Value="DK" />
            <Item Text="Germania" Value="DE" />
            <Item Text="Finlandia" Value="FI" />
            <Item Text="Francia" Value="FR" />
            <Item Text="Internazionale" Value="XY" />
            <Item Text="Italia" Value="IT" />
            <Item Text="Messico" Value="MX" />
            <Item Text="Paesi Bassi" Value="NL" />
            <Item Text="Norvegia" Value="NO" />
            <Item Text="Polonia" Value="PL" />
            <Item Text="Portogallo" Value="PT" />
            <Item Text="Russia" Value="RU" />
            <Item Text="Spagna" Value="ES" />
            <Item Text="Svezia" Value="SE" />
            <Item Text="Svizzera" Value="CH" />
            <Item Text="Turkey" Value="TR" />
            <Item Text="Emirati Arabi Uniti" Value="AE" />
            <Item Text="Regno Unito" Value="GB" />
            <Item Text="Stati Uniti" Value="US" />
          </LocalizedCollection>
        </LocalizedCollections>
        <LocalizedStrings>
          <LocalizedString ElementType="UxElement" StringId="heading">Modifica profilo</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_personal_data" StringId="DisplayName">Dati personali</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="form_legend_contact" StringId="DisplayName">Contatto</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="displayName" StringId="DisplayName">#Display Name</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="displayName" StringId="UserHelpText">#Your display name.</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="DisplayName">Intestazione</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadGenderCodeId" StringId="UserHelpText">Selezionare il saluto</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="DisplayName">Nome</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="surname" StringId="UserHelpText">Inserire il nome</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="DisplayName">Cognome</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="givenName" StringId="UserHelpText">Inserire il cognome</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="DisplayName">Azienda</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_company" StringId="UserHelpText">Inserire l'azienda</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="DisplayName">Professione</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_crmLeadTargetGroupId" StringId="UserHelpText">Selezionare la professione</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="postalCode" StringId="DisplayName">CAP</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="postalCode" StringId="UserHelpText">Inserire il codice postale</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="city" StringId="DisplayName">Città</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="city" StringId="UserHelpText">Inserire la città</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="state" StringId="DisplayName">Stato</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="state" StringId="UserHelpText">Inserire lo stato</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="DisplayName">Paese</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="country" StringId="UserHelpText">Selezionare il Paese</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="DisplayName">Telefono</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="extension_phone" StringId="UserHelpText">Inserire il numero di telefono</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="streetAddress" StringId="DisplayName">Via e n°</LocalizedString>
          <LocalizedString ElementType="ClaimType" ElementId="streetAddress" StringId="UserHelpText">Inserire la via e il numero</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_continue">Risparmiare</LocalizedString>
          <LocalizedString ElementType="UxElement" StringId="button_cancel">Annulla</LocalizedString>
        </LocalizedStrings>
      </LocalizedResources>
    </Localization>
  </BuildingBlocks>
</TrustFrameworkPolicy>
