using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Dornbracht.Storyblok.Api.Model;
using Dornbracht.Storyblok.Api.Config;
using Dornbracht.Storyblok.Api.Service;
using Newtonsoft.Json.Linq;
using Picturepark.SDK.V1.Contract;
using Dornbracht.Picturepark.Api.Config;
using Dornbracht.Picturepark.Api.Service;
using Picturepark.WebJob.UnlockShares.Config;

namespace Picturepark.WebJob.UnlockShares
{
  public class UnlockSharesService : BackgroundService
  {
    private readonly ILogger<UnlockSharesService> _logger;
    private readonly IHost _host;
    private readonly IStoryblokApiService _storyblokService;
    private readonly IPictureparkApiService _pictureparkService;
    private readonly IOptions<StoryblokConfiguration> _configStoryblok;
    private readonly IOptions<PictureparkConfiguration> _configPicturepark;
    private readonly IOptions<WebJobConfiguration> _config;
    private readonly Func<IPictureparkService> _clientFactory;

    public UnlockSharesService(
      ILogger<UnlockSharesService> logger,
      IHost host,
      IStoryblokApiService storyblokService,
      IPictureparkApiService pictureparkService,
      IOptions<StoryblokConfiguration> configStoryblok,
      IOptions<PictureparkConfiguration> configPicturepark,
      IOptions<WebJobConfiguration> config,
      Func<IPictureparkService> clientFactory)
    {
      _logger = logger;
      _host = host;
      _storyblokService = storyblokService;
      _pictureparkService = pictureparkService;
      _configStoryblok = configStoryblok;
      _configPicturepark = configPicturepark;
      _config = config;
      _clientFactory = clientFactory;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
      var countSharesUnlocked = 0;
      var countSharesNotFound = 0;
      var countSharesSkipped = 0;
      var countPublishedStories = 0;

      // iterate over all pages
      var perPage = 50;
      var totalPages = 1;
      for (int page = 1; page <= totalPages; page++)
      {
        StoryList publishedStories = await _storyblokService.GetStories(perPage, page, "is_published=true");
        totalPages = (publishedStories.Total + perPage - 1) / perPage;
        countPublishedStories += publishedStories.Stories.Count();
        _logger.LogInformation($"Check {countPublishedStories}/{publishedStories.Total} stories if they contain any picturepark asset...");

        //1. iterate over each published story
        foreach (var story in publishedStories.Stories)
        {
          _logger.LogDebug($"Story: {story.Name} ({story.Id})");

          //2. request the story including asset fields
          var assetFields = GetAssetFieldsOfStory(story.Id);
          foreach (PictureparkAsset pictureparkAsset in assetFields)
          {
            //3. iterate over all assigned assets
            foreach (var asset in pictureparkAsset.Assets)
            {
              //4. UPDATE
              _logger.LogDebug($"Unlock: {asset.Source.Filename} ({asset.ShareToken})");
              UpdateContentsOfShare(asset, ref countSharesUnlocked, ref countSharesNotFound, ref countSharesSkipped);
            }
          }
        }
      }

      // log result
      _logger.LogInformation($"{countSharesUnlocked} share(s) unlocked");
      _logger.LogInformation($"{countSharesSkipped} share(s) skipped");
      _logger.LogInformation($"{countSharesNotFound} share(s) not found");

      await _host.StopAsync();
    }

    internal IEnumerable<PictureparkAsset> GetAssetFieldsOfStory(string storyId)
    {
      var storyJson = _storyblokService.GetStory(storyId).Result;
      if (storyJson != null)
      {
        // extract picturepark asset fields
        IEnumerable<JToken> assetFieldJson = _storyblokService.ExtractAssetFields(storyJson);
        return assetFieldJson.Select(assetField => assetField.ToObject<PictureparkAsset>());
      }
      return new List<PictureparkAsset>();
    }

    internal void UpdateContentsOfShare(Asset asset, ref int countSharesUnlocked, ref int countSharesNotFound, ref int countShareSkipped)
    {
      try
      {
        var client = _clientFactory();

        var shareDetail = client.Share.GetAsync(asset.ShareId).Result;
        var storyOfShareStory = _pictureparkService.ExtractStoryOfShare(asset.ShareId, shareDetail.Description);

        if (shareDetail.Name.Equals(_configPicturepark.Value.EmbedName)
          && storyOfShareStory != null && storyOfShareStory.Space.Equals(_configStoryblok.Value.Space) //safety first ;)
          && ContainsContentsToUnlock(shareDetail))
        {
          //Es gibt leider kein Pendant zu shareDetail.AsEmbedUpdateRequest()
          //var updateRequest = shareDetail.AsEmbedUpdateRequest();
          //Daher habe musste ich diese Funktion hier nachbauen
          ShareEmbedUpdateRequest updateRequest = new ShareEmbedUpdateRequest();
          updateRequest.Description = shareDetail.Description;
          updateRequest.ExpirationDate = shareDetail.ExpirationDate;
          updateRequest.LayerSchemaIds = shareDetail.LayerSchemaIds;
          updateRequest.Name = shareDetail.Name;
          updateRequest.OutputAccess = shareDetail.OutputAccess;
          updateRequest.Contents = shareDetail.Contents;

          client.Share.UpdateAsync(asset.ShareId, updateRequest);
          _logger.LogInformation(asset.ShareToken);
          countSharesUnlocked++;
        }
        else
        {
          countShareSkipped++;
        }
      }
      catch (Exception ex)
      {
        if (ex.InnerException is ShareNotFoundException)
        {
          _logger.LogWarning($"Share with token '{asset.ShareToken}' not found");
          countSharesNotFound++;
        }
        else
        {
          throw;
        }
      }
    }

    private bool ContainsContentsToUnlock(ShareDetail shareDetail)
    {
      bool result = false;
      foreach (var embed in from content in shareDetail.Contents
                            let embed = content as EmbedContent
                            select embed)
      {
        foreach (var conversionPreset in embed.ConversionPresets)
        {
          if (conversionPreset.OutputFormatId.Equals(_config.Value.OutputFormatId) && conversionPreset.Locked)
          {
            conversionPreset.Locked = false;

            result = true;
          }
        }
      }
      return result;
    }
  }
}
