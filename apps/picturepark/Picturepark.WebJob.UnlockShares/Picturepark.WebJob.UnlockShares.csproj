<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <StartupObject>Picturepark.WebJob.UnlockShares.Program</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="6.0.0" />
    <PackageReference Include="Picturepark.SDK.V1" Version="11.12.45" />
    <PackageReference Include="Picturepark.SDK.V1.Contract" Version="11.12.45" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Dornbracht.Picturepark.Api\Dornbracht.Picturepark.Api.csproj" />
    <ProjectReference Include="..\Dornbracht.Storyblok.Api\Dornbracht.Storyblok.Api.csproj" />
  </ItemGroup>

</Project>
