﻿# Short summary
Picturepark writes updated assets to a RabbitMQ queue, which is then processed by this service provider by updating the corresponding stories in Storyblok.

# Environment Variables
## Sync Metadata
* pictureparkApiUrl=PWDTool
* pictureparkApiCustomerAlias=PWDTool
* pictureparkApiAccessToken=PWDTool
* -
* serviceProviderId=storyblok
* serviceProviderHost=PWDTool
* serviceProviderPort=5671
* serviceProviderSecret=PWDTool
* serviceProviderTriggeringBusinessRuleId=updateMetadataStoryblok
* serviceProviderNode=DevOrTestOrProdORNamenskürzel
* -
* storyblokMapiUrlPWDTool
* storyblokMapiTokenPWDTool
* storyblokSpace=156805

## Update ExpirationDate
* pictureparkApiUrl=PWDTool
* pictureparkApiCustomerAlias=PWDTool
* pictureparkApiAccessToken=PWDTool

* storyblokMapiUrlPWDTool
* storyblokMapiTokenPWDTool
* storyblokSpace=156805

# Business process service provider sample
The basis was the following repository from Picturepark: https://github.com/Picturepark/Picturepark.SDK.Samples/tree/master/Picturepark.SDK.V1.Samples/Picturepark.ServiceProvider.Example.BusinessProcess
