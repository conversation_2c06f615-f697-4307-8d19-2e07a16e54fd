using Dornbracht.Storyblok.Api.Model;
using Newtonsoft.Json.Linq;
using RestSharp;

namespace Dornbracht.Storyblok.Api.Service
{
  public interface IStoryblokApiService
  {
    Task<JObject> GetStory(string story);

    Task UpdateStory(string story, JObject storyJson);

    Task PublishStory(string story);

    Task<StoryList> GetStories(int perPage, int page, string parmeter);

    IEnumerable<JToken> ExtractAssetFields(JObject storyDetailJson);
  }
}
