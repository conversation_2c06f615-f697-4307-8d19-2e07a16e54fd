using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using RestSharp;
using Dornbracht.Storyblok.Api.Config;
using Dornbracht.Storyblok.Api.Model;
using System.Net;

namespace Dornbracht.Storyblok.Api.Service
{
  public class StoryblokApiService:IStoryblokApiService
  {
    private readonly ILogger<StoryblokApiService> _logger;
    private readonly IOptions<StoryblokConfiguration> _config;

    public StoryblokApiService(ILogger<StoryblokApiService> logger,
      IOptions<StoryblokConfiguration> config)
    {
      _logger = logger;
      _config = config;
    }

    internal RestClient Client => new RestClient($"{_config.Value.StoryblokMapiUrl}/{_config.Value.Space}");

    public async Task<JObject> GetStory(string story)
    {
      var request = new RestRequest($"/stories/{story}", Method.Get);
      request.AddHeader("Authorization", _config.Value.MapiToken);
      RestResponse response = await Client.ExecuteAsync(request);
      return JObject.Parse(response.Content);
    }

    public async Task UpdateStory(string story, JObject storyJson)
    {
      var request = new RestRequest($"/stories/{story}", Method.Put);
      request.AddHeader("Authorization", _config.Value.MapiToken);
      request.AddHeader("Content-Type", "application/json");
      request.AddParameter("application/json", storyJson.ToString(), ParameterType.RequestBody);
      await Client.ExecuteAsync(request);
    }

    public async Task PublishStory(string story)
    {
      var request = new RestRequest($"/stories/{story}/publish", Method.Get);
      request.AddHeader("Authorization", _config.Value.MapiToken);
      await Client.ExecuteAsync(request);
    }

    public async Task<StoryList> GetStories(int perPage, int page, string parameter)
    {
      var request = new RestRequest($"/stories/?per_page={perPage}&page={page}&{parameter}", Method.Get);
      request.AddHeader("Authorization", _config.Value.MapiToken);
      RestResponse response = await ExecuteRequest(request);
      
      var result = new StoryList
      {
        Total = int.Parse(response.Headers.ToList().Find(x => x.Name == "Total").Value.ToString()),
        Stories = JObject.Parse(response.Content).SelectToken("$.stories").Select(storyJson => storyJson.ToObject<Story>())
      };
      return result;
    }

    internal async Task<RestResponse> ExecuteRequest(RestRequest request)
    {
      RestResponse response = await Client.ExecuteAsync(request);
      if (response.StatusCode == HttpStatusCode.Unauthorized)
      {
        _logger.LogError($"Error accessing Content Delivery API of storyblok (HttpStatusCode={response.StatusCode}, Resource={request.Resource})");
        throw response.ErrorException;
      }
      return response;
    }

    public IEnumerable<JToken> ExtractAssetFields(JObject storyDetailJson)
    {      
      return storyDetailJson.SelectTokens($"$.story.content..[?(@.plugin=='{StoryblokConfiguration.PluginPictureparkAsset}')]"); //see: https://goessner.net/articles/JsonPath/
    }
  }
}
