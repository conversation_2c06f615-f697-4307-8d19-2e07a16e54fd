using Newtonsoft.Json;

namespace Dornbracht.Storyblok.Api.Model
{
  public class PictureparkAsset
  {
      [JsonProperty("assets")]
      public List<Asset> Assets { get; set; }
  }

  public class Asset
  {
    [JsonProperty("alt")]
    public IDictionary<string, string> Alt { get; set; }

    [JsonProperty("url")]
    public string Url { get; set; }

    [JsonProperty("source")]
    public Source Source { get; set; }

    [JsonProperty("shareId")]
    public string ShareId { get; set; }

    [JsonProperty("filename")]
    public IDictionary<string, string> Filename { get; set; }

    [JsonProperty("contentId")]
    public string ContentId { get; set; }

    [JsonProperty("sortorder")]
    public int Sortorder { get; set; }

    [JsonProperty("shareToken")]
    public string ShareToken { get; set; }

    [JsonProperty("modificationDate")]
    public string ModificationDate { get; set; }

    [<PERSON>son<PERSON>roperty("conversions")]
    public IDictionary<string, string> Conversions { get; set; }
  }

  public class Source
  {
    [JsonProperty("width")]
    public int Width { get; set; }

    [JsonProperty("height")]
    public int Height { get; set; }

    [JsonProperty("filename")]
    public string Filename { get; set; }

    [JsonProperty("extension")]
    public string Extension { get; set; }

    [JsonProperty("thumbnail")]
    public string Thumbnail { get; set; }
  }
}
