namespace Dornbracht.Storyblok.Api.Config
{
  public class StoryblokConfiguration
  {
    public const string PluginPictureparkAsset = "picturepark-asset";

    public string Space => Environment.GetEnvironmentVariable("APPSETTING_storyblokSpace");

    public string MapiToken => Environment.GetEnvironmentVariable("APPSETTING_storyblokMapiToken");

    public string StoryblokMapiUrl { get; set; }
  }
}
