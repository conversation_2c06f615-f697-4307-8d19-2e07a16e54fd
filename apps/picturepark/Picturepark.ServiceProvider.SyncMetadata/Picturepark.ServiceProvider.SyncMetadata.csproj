<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace>Picturepark.ServiceProvider.SyncMetadata</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Properties\launchSettings.json.sample" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.AzureAppServices" Version="6.0.8" />
    <PackageReference Include="Picturepark.SDK.V1" Version="11.12.45" />
    <PackageReference Include="Picturepark.SDK.V1.ServiceProvider" Version="11.12.45" />
    <PackageReference Include="RestSharp" Version="108.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Dornbracht.Picturepark.Api\Dornbracht.Picturepark.Api.csproj" />
    <ProjectReference Include="..\Dornbracht.Storyblok.Api\Dornbracht.Storyblok.Api.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ProjectExtensions>
    <VisualStudio>
      <UserProperties properties_4launchsettings_1json__JsonSchema="" />
    </VisualStudio>
  </ProjectExtensions>

</Project>
