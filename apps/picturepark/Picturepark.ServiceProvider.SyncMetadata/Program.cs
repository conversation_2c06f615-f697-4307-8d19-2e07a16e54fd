using System.IO;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Picturepark.ServiceProvider.SyncMetadata.Config;
using Dornbracht.Picturepark.Api.Config;
using Dornbracht.Storyblok.Api.Config;
using Picturepark.ServiceProvider.SyncMetadata;
using Picturepark.ServiceProvider.SyncMetadata.MessageHandler;
using Picturepark.ServiceProvider.SyncMetadata.Util;
using System;
using Picturepark.SDK.V1.Contract;
using Picturepark.SDK.V1.Authentication;
using Microsoft.Extensions.Options;
using Picturepark.SDK.V1;
using Dornbracht.Storyblok.Api.Service;
using Dornbracht.Picturepark.Api.Service;

var builder = WebApplication.CreateBuilder(args);

builder.Logging.AddConsole();
builder.Logging.AddDebug();

// necessary for app service logging
builder.Logging.AddAzureWebAppDiagnostics();

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory());
builder.Configuration.AddJsonFile("appsettings.json", true, true);

builder.Services.AddOptions();
builder.Services.Configure<ServiceProviderConfiguration>(builder.Configuration.GetSection("config"));
builder.Services.Configure<StoryblokConfiguration>(builder.Configuration.GetSection("config"));
builder.Services.Configure<PictureparkConfiguration>(builder.Configuration.GetSection("config"));

builder.Services.AddLogging();

builder.Services.AddHostedService<ContentBatchSyncMetadataService>();
builder.Services.AddHostedService<LiveStreamSubscriber>();

builder.Services.AddSingleton<IApplicationEventHandlerFactory, ApplicationEventHandlerFactory>();
builder.Services.AddTransient<IApplicationEventHandler, BusinessRuleFiredEventHandler>();

builder.Services.AddSingleton<ContentIdQueue>();

builder.Services.AddSingleton<Func<IPictureparkService>>(
          s =>
          {
            return () =>
                  {
                    var config = s.GetRequiredService<IOptions<PictureparkConfiguration>>();

                    var authClient = new AccessTokenAuthClient(config.Value.PictureparkApiUrl, config.Value.PictureparkApiAccessToken, config.Value.PictureparkApiCustomerAlias);
                    var client = new PictureparkService(new PictureparkServiceSettings(authClient));

                    return client;
                  };
          });

builder.Services.AddSingleton<IStoryblokApiService, StoryblokApiService>();
builder.Services.AddSingleton<IPictureparkApiService, PictureparkApiService>();

var app = builder.Build();
app.MapGet("/", () => string.Empty);
app.Run();
