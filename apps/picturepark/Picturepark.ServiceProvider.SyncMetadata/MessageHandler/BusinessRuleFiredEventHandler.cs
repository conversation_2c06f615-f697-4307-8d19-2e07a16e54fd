using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Picturepark.SDK.V1.Contract;
using Picturepark.ServiceProvider.SyncMetadata.Config;
using Picturepark.ServiceProvider.SyncMetadata.Util;

namespace Picturepark.ServiceProvider.SyncMetadata.MessageHandler
{
  internal class BusinessRuleFiredEventHandler : ApplicationEventHandlerBase<BusinessRuleFiredEvent>
  {
    private readonly IOptions<ServiceProviderConfiguration> _config;
    private readonly ILogger<BusinessRuleFiredEventHandler> _logger;
    private readonly ContentIdQueue _queue;

    public BusinessRuleFiredEventHandler(IOptions<ServiceProviderConfiguration> config, ILogger<BusinessRuleFiredEventHandler> logger, ContentIdQueue queue)
    {
      _config = config;
      _logger = logger;
      _queue = queue;
    }

    protected override void Handle(BusinessRuleFiredEvent applicationEvent)
    {
      var triggeredFor = applicationEvent.Details.Where(d => d.DocumentType == "Content" && d.RuleIds.Contains(_config.Value.ServiceProviderTriggeringBusinessRuleId))
          .Select(d => d.DocumentId).ToArray();

      if (triggeredFor.Any())
      {
        _logger.LogInformation($"{triggeredFor.Length} content updates, will enqueue for batch operation");

        foreach (var contentId in triggeredFor)
          _queue.Add(contentId);
      }
    }
  }
}
