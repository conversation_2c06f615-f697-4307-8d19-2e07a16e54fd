using System;
using System.Collections.Generic;

namespace Picturepark.ServiceProvider.SyncMetadata.Config
{
  public class ServiceProviderConfiguration
  {
    public string ServiceProviderId => Environment.GetEnvironmentVariable("APPSETTING_serviceProviderId");

    public string ServiceProviderHost => Environment.GetEnvironmentVariable("APPSETTING_serviceProviderHost");

    public string ServiceProviderPort => Environment.GetEnvironmentVariable("APPSETTING_serviceProviderPort");

    public bool ServiceProviderUseSsl => true;

    public string ServiceProviderNode => Environment.GetEnvironmentVariable("APPSETTING_serviceProviderNode");

    public string ServiceProviderSecret => Environment.GetEnvironmentVariable("APPSETTING_serviceProviderSecret");

    public string ServiceProviderTriggeringBusinessRuleId => Environment.GetEnvironmentVariable("APPSETTING_serviceProviderTriggeringBusinessRuleId");

    public int BatchSize { get; set; }

    public TimeSpan InactivityTimeout { get; set; }

    public int MinAgeOfUpdatedContentItems { get; set; }
  }
}
