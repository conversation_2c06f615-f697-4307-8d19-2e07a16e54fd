using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using Dornbracht.Picturepark.Api.Config;
using Picturepark.SDK.V1.Contract;
using Picturepark.ServiceProvider.SyncMetadata.Config;
using Picturepark.ServiceProvider.SyncMetadata.Model.Picturepark;
using Picturepark.ServiceProvider.SyncMetadata.Util;
using Dornbracht.Storyblok.Api.Config;
using Dornbracht.Storyblok.Api.Model;
using Dornbracht.Storyblok.Api.Service;
using Dornbracht.Picturepark.Api.Service;
using System.Globalization;

namespace Picturepark.ServiceProvider.SyncMetadata
{
  internal class ContentBatchSyncMetadataService : IHostedService, IDisposable
  {
    private readonly ILogger<ContentBatchSyncMetadataService> _logger;
    private readonly ContentIdQueue _queue;
    private readonly IOptions<ServiceProviderConfiguration> _config;
    private readonly IOptions<StoryblokConfiguration> _configStoryblok;
    private readonly IOptions<PictureparkConfiguration> _configPicturepark;
    private readonly Func<IPictureparkService> _clientFactory;
    private readonly CancellationTokenSource _taskCancellationTokenSource;
    private readonly BlockingCollection<string[]> _batches = new BlockingCollection<string[]>();
    private readonly IStoryblokApiService _storyblokService;
    private readonly IPictureparkApiService _pictureparkService;

    private Task _batchingTask;
    private Task _syncTask;

    public ContentBatchSyncMetadataService(
        ILogger<ContentBatchSyncMetadataService> logger,
        ContentIdQueue queue,
        IOptions<ServiceProviderConfiguration> config,
        IOptions<StoryblokConfiguration> configStoryblok,
        IOptions<PictureparkConfiguration> configPicturepark,
        Func<IPictureparkService> clientFactory,
        IStoryblokApiService storyblokService,
        IPictureparkApiService pictureparkService)
    {
      _logger = logger;
      _queue = queue;
      _config = config;
      _configStoryblok = configStoryblok;
      _configPicturepark = configPicturepark;
      _clientFactory = clientFactory;
      _taskCancellationTokenSource = new CancellationTokenSource();
      _storyblokService = storyblokService;
      _pictureparkService = pictureparkService;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
      _batchingTask = Task.Run(GatherBatches, CancellationToken.None).ContinueWith(
          t =>
          {
            if (t.IsFaulted)
              throw t.Exception;
          },
          CancellationToken.None);

      _syncTask = Task.Run(SyncBatches, CancellationToken.None).ContinueWith(
          t =>
          {
            if (t.IsFaulted)
              throw t.Exception;
          },
          CancellationToken.None);

      return Task.CompletedTask;
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
      _taskCancellationTokenSource.Cancel();

      var batchingTask = _batchingTask ?? Task.CompletedTask;
      var downloadTask = _syncTask ?? Task.CompletedTask;

      await Task.WhenAll(batchingTask, downloadTask).ConfigureAwait(false);
    }

    public void Dispose()
    {
      _queue?.Dispose();

      _batchingTask?.Dispose();
      _taskCancellationTokenSource?.Dispose();
    }

    private void GatherBatches()
    {
      while (!_taskCancellationTokenSource.IsCancellationRequested)
      {
        _logger.LogInformation("GatherBatches: Waiting for work...");

        using (var ctsTimeout = CancellationTokenSource.CreateLinkedTokenSource(_taskCancellationTokenSource.Token))
        {
          ctsTimeout.CancelAfter(_config.Value.InactivityTimeout);

          var batch = new List<string>();
          while (batch.Count < _config.Value.BatchSize && !ctsTimeout.IsCancellationRequested)
          {
            if (_queue.TryTake(out string contentId, _config.Value.InactivityTimeout))
              batch.Add(contentId);
          }

          if (ctsTimeout.IsCancellationRequested)
          {
            _logger.LogInformation("GatherBatches: Inactivity period elapsed, continue...");
          }

          if (batch.Count > 0)
          {
            _logger.LogInformation($"Got {batch.Count} content ids to sync metatada");
            _batches.Add(batch.ToArray(), CancellationToken.None);
          }
        }
      }
    }

    private async Task SyncBatches()
    {
      while (_batches.TryTake(out var batch, -1, _taskCancellationTokenSource.Token))
      {
        _logger.LogInformation($"SyncBatches: Batch with {batch.Length} content item(s)");

        var client = _clientFactory();

        var countSkipped = 0;
        var countQueued = 0;
        var countSuccessfullyUpdated = 0;

        foreach (var contentId in batch)
        {
          //get content
          var contentDetail = await client.Content.GetAsync(contentId, new List<ContentResolveBehavior> { ContentResolveBehavior.Metadata, ContentResolveBehavior.Content });

          int ageInMinutes = (contentDetail.Audit.ModificationDate.Minute - DateTime.Now.Minute) * -1;
          if (ageInMinutes < _config.Value.MinAgeOfUpdatedContentItems)
          {
            _queue.TryAdd(contentId);
            countQueued++;
          }
          else
          {
            //get shares
            var contentReferences = await client.Content.GetReferencesAsync(contentId, new ContentReferencesRequest() { Shares = new PagingRequest() { Limit = 500 } });

            //filter relevant shares by 'embed' and 'storyblok
            foreach (var shareReference in contentReferences.ShareReferences.Results)
            {
              if (shareReference.ShareType == ShareType.Embed && shareReference.Name.Equals(_configPicturepark.Value.EmbedName))
              {
                var shareDetail = await client.Share.GetAsync(shareReference.Id);
                var storyOfShareStory = _pictureparkService.ExtractStoryOfShare(shareReference.Id, shareDetail.Description);

                //check for space because of possibly shared picturepark instance
                if (storyOfShareStory != null && storyOfShareStory.Space.Equals(_configStoryblok.Value.Space))
                {
                  await UpdateStory(storyOfShareStory.Story, contentDetail, shareDetail);
                  countSuccessfullyUpdated++;
                }
                else
                {
                  countSkipped++;
                }
              }
            }
          }
        }

        _logger.LogInformation($"SyncBatches: {countSkipped} stories skipped");
        _logger.LogInformation($"SyncBatches: {countQueued} stories queued");
        _logger.LogInformation($"SyncBatches: {countSuccessfullyUpdated} stories updated");
      }
    }

    /**
     * NOTE Hier wird bewusst nur mit dem Story-Json-Objekt gearbeitet, da genau dieses letztlich kurze darauf unbeschaded wieder zurückgeschrieben werden muss.
     */
    internal async Task UpdateStory(string storyId, ContentDetail contentDetail, ShareDetail shareDetail)
    {
      var storyJson = _storyblokService.GetStory(storyId).Result;

      //get all picturepark-asset fields of story
      var assetFields = _storyblokService.ExtractAssetFields(storyJson);
      foreach (var assetField in assetFields)
      {
        var pictureparkAsset = assetField.ToObject<PictureparkAsset>();

        foreach (var asset in pictureparkAsset.Assets.Where(asset => asset.ShareId == shareDetail.Id && asset.ContentId == contentDetail.Id))
        {
          DateTime? modificationDate = asset.ModificationDate == null ? null : DateTime.ParseExact(asset.ModificationDate, "MM/dd/yyyy HH:mm:ss", CultureInfo.InvariantCulture);
          if (modificationDate == null || modificationDate < contentDetail.Audit.ModificationDate)
          {
            UpdateMetadata(asset, contentDetail);
            UpdatConversionStrings(asset, contentDetail);
          }
        }
        assetField["assets"] = JToken.FromObject(pictureparkAsset.Assets);
      }

      await _storyblokService.UpdateStory(storyId, storyJson);

      var published = (bool)storyJson.SelectToken("story")["published"];
      if (published)
      {
#pragma warning disable CS4014 // Da auf diesen Aufruf nicht gewartet wird, wird die Ausführung der aktuellen Methode vor Abschluss des Aufrufs fortgesetzt.
        _storyblokService.PublishStory(storyId);
#pragma warning restore CS4014 // Da auf diesen Aufruf nicht gewartet wird, wird die Ausführung der aktuellen Methode vor Abschluss des Aufrufs fortgesetzt.
      }
    }

    #region SEO Metadata
    internal LayerSeo GetLayerSeo(ContentDetail contentDetail)
    {
      LayerSeo result = null;
      if (contentDetail.Metadata.ContainsKey(_configPicturepark.Value.LayerSeo))
      {
        var namingsAndSeo = (JObject)contentDetail.Metadata[_configPicturepark.Value.LayerSeo];
        var json = new JObject { { "Metadata", namingsAndSeo } }; //forgeneric access via IDictionary
        result = json.ToObject<LayerSeo>();
      }
      return result;
    }

    internal void UpdateMetadata(Asset asset, ContentDetail contentDetail)
    {
      var layerSeo = GetLayerSeo(contentDetail);
      if (layerSeo != null)
      {
        UpdateMetadataAttribute(asset.Alt, layerSeo, _configPicturepark.Value.FieldnameAlt);
        UpdateMetadataAttribute(asset.Filename, layerSeo, _configPicturepark.Value.FieldnameFilename);
      }
    }


    internal void UpdateMetadataAttribute(IDictionary<string, string> metadataStoryblok, LayerSeo layerSeo, string pictureparkAttribut)
    {
      var metadataAttributPicturepark = layerSeo.Metadata.ContainsKey(pictureparkAttribut) ? layerSeo.Metadata[pictureparkAttribut] : null;

      if (metadataAttributPicturepark != null)
      {
        //set defaults if not already set
        if (metadataStoryblok == null)
        {
          metadataStoryblok = new Dictionary<string, string>();
          foreach (string value in _configPicturepark.Value.LanguageMappings.Values)
          {
            metadataStoryblok[value] = metadataAttributPicturepark[PictureparkConfiguration.PictureparkDefault];
          }
        }

        foreach (var language in _configPicturepark.Value.PictureparkLanguages)
        {
          if (metadataAttributPicturepark.ContainsKey(language))
          {
            metadataStoryblok[_configPicturepark.Value.LanguageMappings[language]] = metadataAttributPicturepark[language];
          }
        }
      }
    }


    #endregion

    #region UpdatConversionStrings

    internal LayerImageAnalytics GetLayerImageAnalytics(ContentDetail contentDetail)
    {
      LayerImageAnalytics result = null;
      if (contentDetail.Metadata.ContainsKey(_configPicturepark.Value.LayerImageAnalytics))
      {
        var json = (JObject)contentDetail.Metadata[_configPicturepark.Value.LayerImageAnalytics];
        result = json.ToObject<LayerImageAnalytics>();
      }
      return result;
    }


    internal void UpdatConversionStrings(Asset asset, ContentDetail contentDetail)
    {
      var layerImageAnalytics = GetLayerImageAnalytics(contentDetail);
      if (layerImageAnalytics != null && layerImageAnalytics.ConversionPresets != null)
      {
        foreach (var conversionPreset in layerImageAnalytics.ConversionPresets)
        {
          var key = conversionPreset.names[PictureparkConfiguration.PictureparkDefault];
          asset.Conversions[key] = CleanupConversionString(conversionPreset.ConversionString);
        }
      }
    }

    /**
     * !!! Attention !!! Changes you make here must also be made in the custom field type plugin
     * '/crop:681x681,fp:1051,320/UPDATED-animal.jpg#aspectRatio:1x1'  -->  '/crop:681x681,fp:1051,320'
     */
    internal string CleanupConversionString(string conversionString)
    {
      //remove #aspectRatio
      var indexOfHash = conversionString.IndexOf('#');
      if (indexOfHash > 0)
      {
        conversionString = conversionString.Substring(0, indexOfHash);
      }

      //remove filename
      var lastIndexOf = conversionString.LastIndexOf('.');
      if (lastIndexOf > 0)
      {
        lastIndexOf = conversionString.LastIndexOf('/');
        conversionString = conversionString.Substring(0, lastIndexOf);
      }
      return conversionString;
    }

    #endregion
  }
}
