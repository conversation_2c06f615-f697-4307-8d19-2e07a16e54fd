using System;
using System.Reactive;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Picturepark.SDK.V1.ServiceProvider;
using Picturepark.SDK.V1.ServiceProvider.Buffer;
using Picturepark.ServiceProvider.SyncMetadata.Config;
using Picturepark.ServiceProvider.SyncMetadata.MessageHandler;

namespace Picturepark.ServiceProvider.SyncMetadata
{
  public class LiveStreamSubscriber : IHostedService, IDisposable
  {
    private readonly ILogger<LiveStreamSubscriber> _logger;
    private readonly IApplicationEventHandlerFactory _eventHandlerFactory;
    private readonly Configuration _serviceProviderConfiguration;

    private ServiceProviderClient _client;
    private IDisposable _subscription;

    public LiveStreamSubscriber(ILogger<LiveStreamSubscriber> logger, IOptions<ServiceProviderConfiguration> config, IApplicationEventHandlerFactory eventHandlerFactory)
    {
      _logger = logger;
      _eventHandlerFactory = eventHandlerFactory;

      _serviceProviderConfiguration = new Configuration
      {
        Host = config.Value.ServiceProviderHost,
        Port = config.Value.ServiceProviderPort,
        NodeId = config.Value.ServiceProviderNode, //must be identic per environment
        ServiceProviderId = config.Value.ServiceProviderId,
        User = config.Value.ServiceProviderId,
        Password = config.Value.ServiceProviderSecret,
        UseSsl = config.Value.ServiceProviderUseSsl
      };
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
      _logger.LogInformation("Subscribing to live stream");
      _client = new ServiceProviderClient(_serviceProviderConfiguration);
      _subscription = _client.GetLiveStreamObserver().Subscribe(OnLiveStreamEvent);

      return Task.CompletedTask;
    }

    private void OnLiveStreamEvent(EventPattern<EventArgsLiveStreamMessage> e)
    {
      var applicationEvent = e.EventArgs.Message.ApplicationEvent;

      if (applicationEvent != null)
      {
        var handler = _eventHandlerFactory.Get(applicationEvent);
        handler?.Handle(applicationEvent);

        e.EventArgs.Ack();
      }
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
      return Task.CompletedTask;
    }

    public void Dispose()
    {
      _client?.Dispose();
      _subscription?.Dispose();
    }
  }
}
