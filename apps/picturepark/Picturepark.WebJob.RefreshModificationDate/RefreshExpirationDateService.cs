using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Dornbracht.Storyblok.Api.Model;
using Dornbracht.Storyblok.Api.Config;
using Dornbracht.Storyblok.Api.Service;
using Newtonsoft.Json.Linq;
using Picturepark.SDK.V1.Contract;
using Dornbracht.Picturepark.Api.Config;
using Dornbracht.Picturepark.Api.Service;
using Picturepark.WebJob.RefreshModificationDate.Config;

namespace Picturepark.WebJob.RefreshModificationDate
{
  public class RefreshExpirationDateService : BackgroundService
  {
    private readonly ILogger<RefreshExpirationDateService> _logger;
    private readonly IHost _host;
    private readonly IStoryblokApiService _storyblokService;
    private readonly IPictureparkApiService _pictureparkService;
    private readonly IOptions<StoryblokConfiguration> _configStoryblok;
    private readonly IOptions<PictureparkConfiguration> _configPicturepark;
    private readonly IOptions<WebJobConfiguration> _config;
    private readonly Func<IPictureparkService> _clientFactory;

    public RefreshExpirationDateService(
      ILogger<RefreshExpirationDateService> logger,
      IHost host,
      IStoryblokApiService storyblokService,
      IPictureparkApiService pictureparkService,
      IOptions<StoryblokConfiguration> configStoryblok,
      IOptions<PictureparkConfiguration> configPicturepark,
      IOptions<WebJobConfiguration> config,
      Func<IPictureparkService> clientFactory)
    {
      _logger = logger;
      _host = host;
      _storyblokService = storyblokService;
      _pictureparkService = pictureparkService;
      _configStoryblok = configStoryblok;
      _configPicturepark = configPicturepark;
      _config = config;
      _clientFactory = clientFactory;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
      var countSharesUpdated = 0;
      var countSharesNotFound = 0;
      var countSharesSkipped = 0;
      var countPublishedStories = 0;
      var countWarnings = 0;
      var countErrors = 0;

      DateTime newExpirationDate = DateTime.Now.AddMonths(_config.Value.MonthToAdd);

      // iterate over all pages
      var perPage = 25;
      var totalPages = 1;
      for (int page = 1; page <= totalPages; page++)
      {
        StoryList publishedStories = await _storyblokService.GetStories(perPage, page, "is_published=true");
        totalPages = (publishedStories.Total + perPage - 1) / perPage;
        countPublishedStories += publishedStories.Stories.Count();
        _logger.LogInformation($"Check {countPublishedStories}/{publishedStories.Total} stories if they contain any picturepark asset...");

        //1. iterate over each published story
        foreach (var story in publishedStories.Stories)
        {
          _logger.LogDebug($"Story: {story.Name} ({story.Id})");

          //if (story.Id == "211573773" || story.Id == "211573776")
          //{
            //2. request the story including asset fields
            var assetFields = GetAssetFieldsOfStory(story.Id);
            var count_debug = assetFields.Count();
            foreach (PictureparkAsset pictureparkAsset in assetFields)
            {
              //3. iterate over all assigned assets
              foreach (var asset in pictureparkAsset.Assets)
              {
                //4. UPDATE
                _logger.LogDebug($"Update: {asset.Source.Filename} ({asset.ShareId})");
                UpdateExpirationDateOfShare(asset, newExpirationDate, ref countSharesUpdated, ref countSharesNotFound, ref countSharesSkipped, ref countWarnings, ref countErrors);
              }
            }
          //}
        }
      }

      // log result
      _logger.LogInformation($"{countSharesUpdated} share(s) updated");
      _logger.LogInformation($"{countSharesSkipped} share(s) skipped");
      _logger.LogInformation($"{countSharesNotFound} share(s) not found");
      _logger.LogInformation($"{countWarnings} warnings");
      _logger.LogInformation($"{countErrors} errors");

      await _host.StopAsync();
    }

    internal IEnumerable<PictureparkAsset> GetAssetFieldsOfStory(string storyId)
    {
      var storyJson = _storyblokService.GetStory(storyId).Result;
      if (storyJson != null)
      {
        // extract picturepark asset fields
        IEnumerable<JToken> assetFieldJson = _storyblokService.ExtractAssetFields(storyJson);
        return assetFieldJson.Select(assetField => assetField.ToObject<PictureparkAsset>());
      }
      return new List<PictureparkAsset>();
    }

    internal void UpdateExpirationDateOfShare(Asset asset, DateTime newExpirationDate, ref int countSharesUpdated, ref int countSharesNotFound, ref int countSharesSkipped, ref int countWarning, ref int countErrors)
    {
      try
      {
        var client = _clientFactory();

        var shareDetail = client.Share.GetAsync(asset.ShareId).Result;
        var storyOfShareStory = _pictureparkService.ExtractStoryOfShare(asset.ShareId, shareDetail.Description);

        var daysUntilExpirationDate = shareDetail.ExpirationDate?.Subtract(DateTime.Now).Days;
        if (daysUntilExpirationDate <= _config.Value.MinDaysBeforeExpirationExtension)
        {
          if (shareDetail.Name.Equals(_configPicturepark.Value.EmbedName)
            && storyOfShareStory != null && storyOfShareStory.Space.Equals(_configStoryblok.Value.Space)) //safety first ;)
          {
            //var updateRequest = shareDetail.AsEmbedUpdateRequest();
            ShareEmbedUpdateRequest updateRequest = new ShareEmbedUpdateRequest();
            updateRequest.Description = shareDetail.Description;
            updateRequest.LayerSchemaIds = shareDetail.LayerSchemaIds;
            updateRequest.Name = shareDetail.Name;
            updateRequest.OutputAccess = shareDetail.OutputAccess;
            updateRequest.Contents = shareDetail.Contents;
            updateRequest.ExpirationDate = newExpirationDate;

            var result = client.Share.UpdateAsync(asset.ShareId, updateRequest).Result;
            countSharesUpdated++;
          }
        }
        else
        {
          countSharesSkipped++;
        }
      }
      catch (Exception ex)
      {
        if (ex.InnerException is ShareNotFoundException)
        {
          //in case of content sync from prod to test (or dev)
          _logger.LogWarning($"Share with token '{asset.ShareToken}' not found");
          countSharesNotFound++;
        }
        else if (ex.InnerException is ContentPermissionException)
        {
          _logger.LogError(ex.InnerException, $"No permissions to Update share with token '{asset.ShareToken}'");
          countErrors++;
        }
        else if (ex.InnerException is TaskCanceledException)
        {
          //picturepark timeouts (client.Share.GetAsync(asset.ShareId))
          _logger.LogWarning($"TaskCanceled for share with token '{asset.ShareToken}'");
          countWarning++;
        }
        else
        {
          _logger.LogError(ex, $"Expiration date of share with token '{asset.ShareToken}' not extended!");
          countErrors++;
        }
      }
    }
  }
}
