using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Dornbracht.Picturepark.Api.Config;
using Picturepark.SDK.V1;
using Picturepark.SDK.V1.Authentication;
using Picturepark.SDK.V1.Contract;
using Dornbracht.Storyblok.Api.Config;
using Dornbracht.Storyblok.Api.Service;
using Dornbracht.Picturepark.Api.Service;
using Picturepark.WebJob.RefreshModificationDate.Config;

namespace Picturepark.WebJob.RefreshModificationDate
{
  internal class Program
  {
    static void Main(string[] args)
    {
      CreateHostBuilder(args).Build().Run();
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
         Host.CreateDefaultBuilder(args)
             .ConfigureServices((hostContext, services) =>
             {
               services.AddOptions();
               services.Configure<StoryblokConfiguration>(hostContext.Configuration.GetSection("config"));
               services.Configure<PictureparkConfiguration>(hostContext.Configuration.GetSection("config"));
               services.Configure<WebJobConfiguration>(hostContext.Configuration.GetSection("config"));

               services.AddLogging();

               services.AddSingleton<Func<IPictureparkService>>(
                          s =>
                          {
                            return () =>
                            {
                              var config = s.GetRequiredService<IOptions<PictureparkConfiguration>>();

                              var authClient = new AccessTokenAuthClient(config.Value.PictureparkApiUrl, config.Value.PictureparkApiAccessToken, config.Value.PictureparkApiCustomerAlias);
                              var client = new PictureparkService(new PictureparkServiceSettings(authClient));

                              return client;
                            };
                          });

               services.AddSingleton<IStoryblokApiService, StoryblokApiService>();
               services.AddSingleton<IPictureparkApiService, PictureparkApiService>();

               services.AddHostedService<RefreshExpirationDateService>();
             });
  }
}
