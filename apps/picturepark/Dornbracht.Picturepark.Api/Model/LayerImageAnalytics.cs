using System.Collections.Generic;
using Newtonsoft.Json;

namespace Dornbracht.Picturepark.Api.Model
{
  public class LayerImageAnalytics
  {
    public class ConversionPreset
    {
      [JsonProperty("conversionString")]
      public string ConversionString { get; set; }

      [JsonProperty("outputFormatId")]
      public string OutputFormatId { get; set; }

      [JsonProperty("Names")]
      public IDictionary<string, string> names { get; set; }

      [JsonProperty("conversionPresetTemplateId")]
      public string ConversionPresetTemplateId { get; set; }
    }

    [JsonProperty("conversionPresets")]
    public List<ConversionPreset> ConversionPresets { get; set; }

  }
}
