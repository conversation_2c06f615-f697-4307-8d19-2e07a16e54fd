namespace Dornbracht.Picturepark.Api.Config
{
  public class PictureparkConfiguration
  {
    public const string PictureparkDefault = "x-default";

    public string PictureparkApiUrl => Environment.GetEnvironmentVariable("APPSETTING_pictureparkApiUrl");

    public string PictureparkApiCustomerAlias => Environment.GetEnvironmentVariable("APPSETTING_pictureparkApiCustomerAlias");

    public string PictureparkApiAccessToken => Environment.GetEnvironmentVariable("APPSETTING_pictureparkApiAccessToken");

    public string EmbedName { get; set; }

    public string LayerSeo { get; set; }

    public string LayerImageAnalytics { get; set; }

    public string FieldnameFilename { get; set; }

    public string FieldnameAlt { get; set; }

    public IList<string> PictureparkLanguages { get; set; }

    public Dictionary<string, string> LanguageMappings { get; set; }
  }
}
