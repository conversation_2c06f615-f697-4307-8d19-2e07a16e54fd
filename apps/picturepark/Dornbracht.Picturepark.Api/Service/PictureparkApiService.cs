using Dornbracht.Picturepark.Api.Config;
using Dornbracht.Picturepark.Api.Model;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Dornbracht.Picturepark.Api.Service
{

  public class PictureparkApiService : IPictureparkApiService
  {
    private readonly ILogger<PictureparkApiService> _logger;
    private readonly IOptions<PictureparkConfiguration> _config;

    public PictureparkApiService(ILogger<PictureparkApiService> logger,
      IOptions<PictureparkConfiguration> config)
    {
      _logger = logger;
      _config = config;
    }

    public StoryOfShare ExtractStoryOfShare(string shareId, string descriptionOfShare)
    {
      StoryOfShare result = null;
      try
      {
        if (descriptionOfShare != null)
        {
          result = JObject.Parse(descriptionOfShare).ToObject<StoryOfShare>();
          result = result.Story == null ? null : result; //in case of debug
        }
      }
      catch (JsonReaderException e)
      {
        _logger.LogWarning($"Invalid story at share {shareId}: {e.Message}");
      }
      return result;
    }
  }
}
