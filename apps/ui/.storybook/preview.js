/** @type { import('@storybook/vue3').Preview } */

import '../src/styles/storybook.scss';

// minimal config for vue-router to work
import { createRouter, createWebHistory } from 'vue-router';
import { setup } from '@storybook/vue3';
const StorybookFallback = {
  template: '<div></div>',
};

// define wildcard routes as minimum standard for vue-router to work
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/:pathMatch(.*)*',
      name: '*',
      component: StorybookFallback,
    },
  ],
});

setup((app) => {
  app.use(router);
});

const preview = {
  parameters: {
    actions: { argTypesRegex: '^on[A-Z].*' },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
