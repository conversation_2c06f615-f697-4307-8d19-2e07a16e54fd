<template>
  <div class="footer">
    <UiButton :disabled="disabled" type="primary" :label="buttonLabel" :title="buttonLabel" @click="$emit('clickButton')" />
  </div>
</template>

<script>
import UiButton from '../atoms/Button.vue';
/* AC-009 Bookmark-Modal Footer  */
export default {
  components: { UiButton },
  props: {
    buttonLabel: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  emits: ['clickButton']
};
</script>

<style lang="scss" scoped>
@use '../../styles/variables.scss' as variables;
@use '../../styles/mixins' as mixins;
.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: variables.$distance-static-44;
}
</style>
