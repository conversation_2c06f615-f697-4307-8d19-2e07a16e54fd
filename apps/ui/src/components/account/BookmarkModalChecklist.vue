<template>
  <div>
    <UiButton
      :disabled="false"
      icon="scale-plus"
      type="benefits"
      :label="buttonLabel"
      show-icon
      :title="buttonLabel"
      @click="$emit('clickButton')"
    />
    <div class="checklist">
      <UiCheckbox
        v-for="productList in productLists"
        :id="'Checkbox-' + productList.id"
        :key="productList.id"
        :disabled="false"
        :show-error="false"
        :checked="false"
        :label="productList.name"
        :name="'Checkbox-' + productList.id"
        @change="(val) => selectProductList(productList.id, val)"
      />
    </div>
  </div>
</template>

<script>
import UiCheckbox from '../atoms/Checkbox.vue';
import UiButton from '../atoms/Button.vue';
/* AC-007 Bookmark-Modal Sections - Button with Benefits + Checkbox (03) */
export default {
  components: { UiCheckbox, UiButton },
  props: {
    productLists: {
      type: Array,
      default() {
        return [];
      }
    },
    buttonLabel: {
      type: String,
      default: ''
    }
  },
  emits: ['selectCheckbox', 'selectCheckbox'],
  methods: {
    selectProductList(id, val) {
      this.$emit('selectCheckbox', id, val);
    }
  }
};
</script>

<style lang="scss" scoped>
@use '../../styles/variables.scss' as variables;

:deep(.button) {
  margin-bottom: variables.$distance-static-28;
}
.checklist {
  display: flex;
  flex-direction: column;
  gap: variables.$distance-static-16;
  align-items: flex-start;
}
</style>
