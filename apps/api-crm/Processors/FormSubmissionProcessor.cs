using System;
using System.Linq;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Api.Crm.Abstraction.Services;
using Dornbracht.Api.Crm.Models;
using Dornbracht.Api.Crm.Models.Api.Form;
using Dornbracht.Api.Crm.Models.Api.Form.Jotform;
using Dornbracht.Api.Crm.Models.Payload;
using Microsoft.Xrm.Sdk;
using Newtonsoft.Json.Linq;

namespace Dornbracht.Api.Crm.Processors
{
  public class FormSubmissionEntityProcessor : QueueMessageEntityProcessor<FormSubmissionPayload>
  {

    private const string UploadFieldNamePrefix = "afd_documenturl";

    private HttpClient HttpClient { get; }

    private IFormServiceApiClient FormServiceApiClient { get; }

    private BlobContainerClient ContainerClient { get; }

    public FormSubmissionEntityProcessor(IFormServiceApiClient formServiceApiClient, IHttpClientFactory clientFactory, BlobContainerClient containerClient)
    {
      FormServiceApiClient = formServiceApiClient;
      ContainerClient = containerClient;
      HttpClient = clientFactory.CreateClient();
    }

    public async override Task<(Entity, CrmAction)> ProcessAsync(FormSubmissionPayload messagePayload)
    {
      var uploadCount = 1;
      var entity = new Entity(messagePayload.FormEntityType);
      var submission = await FormServiceApiClient.GetSubmissionAsync(messagePayload.SubmissionId);
      var questions = await FormServiceApiClient.GetFormQuestionsAsync(messagePayload.FormId.ToString());

      foreach (var field in submission.Fields)
      {
        if (field.Type == SubmissionField.UploadFieldType)
        {
          // this should only be necessary on local azurite service
          await ContainerClient.CreateIfNotExistsAsync(PublicAccessType.None);

          if(!(field.Value is JArray urls)) {
            continue;
          }

          foreach (var url in urls)
          {
            var downloadUrl = url.ToString();
            using (var response = await HttpClient.GetAsync(downloadUrl))
            using (var stream = await response.Content.ReadAsStreamAsync())
            {
              var urlParts = downloadUrl.Split('/');
              var fileName = urlParts[urlParts.Length - 1];
              var blobName = $"{Guid.NewGuid().ToString("N")}-{fileName}";
              await ContainerClient.UploadBlobAsync(blobName, stream);
              var blob = ContainerClient.GetBlobClient(blobName);
              var properties = await ContainerClient.GetBlobClient(blobName).GetPropertiesAsync();

              BlobHttpHeaders headers = new BlobHttpHeaders
              {
                ContentType = response.Content.Headers.ContentType.MediaType,
                ContentLanguage = properties.Value.ContentLanguage,
                CacheControl = properties.Value.CacheControl,
                ContentDisposition = properties.Value.ContentDisposition,
                ContentEncoding = properties.Value.ContentEncoding,
                ContentHash = properties.Value.ContentHash
              };

              await blob.SetHttpHeadersAsync(headers);

              var upload = new CrmUpload
              {
                Url = $"{Environment.GetEnvironmentVariable("API_BASE_URL", EnvironmentVariableTarget.Process)?.TrimEnd('/')}/v1/crm/form/upload/{blobName}?code={Environment.GetEnvironmentVariable("FORM_UPLOAD_FUNCTION_KEY", EnvironmentVariableTarget.Process)}",
                ContentLength = properties.Value.ContentLength,
                ContentType = headers.ContentType,
                OriginalFileName = fileName,
                StoredFileName = blobName,
                StoredFilePath = "/"
              };
              var uploadData = JsonSerializer.Serialize(upload);

              entity[$"{UploadFieldNamePrefix}{uploadCount}"] = uploadData;

              uploadCount++;
            }
          }

          continue;
        }

        if (field.Value is string value && !string.IsNullOrEmpty(value))
        {
          if (field.Type == SubmissionField.DropdownFieldType || field.Type == SubmissionField.RadioFieldType)
          {
            var question = questions.FirstOrDefault(question => question.Name == field.Name);
            if (question != default && question.SelectValues.ContainsKey(value))
            {
              value = question.SelectValues[value];
            }
          }

          switch (field.CrmType.ToLower())
          {
            case "optionset":
              if (int.TryParse(value, out var optionValue))
              {
                entity[field.Name] = new OptionSetValue(optionValue);
              }
              else
              {
                throw new FormatException($"Unable to parse option set value for field '{field.Name}'.");
              }
              break;
            case "boolean":
              if (bool.TryParse(value, out var boolValue))
              {
                entity[field.Name] = boolValue;
              }
              else
              {
                throw new FormatException($"Unable to parse boolean value for field '{field.Name}'.");
              }
              break;
            case "integer":
              if (int.TryParse(value, out var intValue))
              {
                entity[field.Name] = intValue;
              }
              else
              {
                throw new FormatException($"Unable to parse integer value for field '{field.Name}'.");
              }
              break;
            case "money":
              if (decimal.TryParse(value, out var decimalValue))
              {
                entity[field.Name] = new Money(decimalValue);
              }
              else
              {
                throw new FormatException($"Unable to parse money value for field '{field.Name}'.");
              }
              break;
            case "datetime":
              if (!string.IsNullOrEmpty(value))
              {
                var dateTimeModel = JsonSerializer.Deserialize<JotformDateTime>(value);
                var dateTime = dateTimeModel.ToDateTime();
                if (dateTime.HasValue)
                {
                  entity[field.Name] = dateTime;
                }
                else
                {
                  throw new FormatException($"Unable to parse datetime value for field '{field.Name}'.");
                }
              }
              break;
            case "string":
            default:
              entity[field.Name] = value;
              break;
          }
        }
      };

      return (entity, CrmAction.Create);
    }
  }
}
