using System;
using System.Threading.Tasks;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Api.Crm.Models.Payload;
using Microsoft.Xrm.Sdk;

namespace Dornbracht.Api.Crm.Processors
{
  public class LeadUpdateEntityProcessor : QueueMessageEntityProcessor<LeadUpdatePayload>
  {
    public async override Task<(Entity, CrmAction)> ProcessAsync(LeadUpdatePayload messagePayload)
    {
      var entity = new Entity("lead", messagePayload.Id);
      entity["leadid"] = messagePayload.Id;

      if (int.TryParse(messagePayload.Data.GenderCode, out var genderId))
        entity["afd_gender"] = new OptionSetValue(genderId);

      entity["firstname"] = messagePayload.Data.FirstName;
      entity["lastname"] = messagePayload.Data.LastName;
      entity["telephone1"] = messagePayload.Data.Phone;

      if (!string.Equals(messagePayload.Data.Country, "XY", StringComparison.InvariantCultureIgnoreCase))
      {
        entity["address1_country"] = messagePayload.Data.Country;
      }
      else
      {
        entity["address1_country"] = string.Empty;
      }

      entity["companyname"] = messagePayload.Data.Company;

      if (int.TryParse(messagePayload.Data.TargetGroup, out var roleId))
        entity["afd_targetgroup"] = new OptionSetValue(roleId);

      entity["address1_postalcode"] = messagePayload.Data.PostalCode;
      entity["address1_line1"] = messagePayload.Data.Street;
      entity["address1_city"] = messagePayload.Data.City;
      entity["address1_stateorprovince"] = messagePayload.Data.State;

      // fixed values      
      entity["leadsourcecode"] = new OptionSetValue(*********);  

      // Different CampaignId for anonymous Users with Watchlist => DORSUP-175
      if (messagePayload.Data.UserHasSpecList.Equals("true"))
      {
        entity["campaignid"] = new EntityReference("campaign", new Guid("bdc0b9d1-882f-ee11-bdf4-000d3a2acbd3"));
      }
      else
      {
        entity["campaignid"] = new EntityReference("campaign", new Guid("b920f781-1c44-ea11-a812-000d3a4a1f5d"));
      }      
      
      return await Task.FromResult((entity, CrmAction.Update));
    }
  }
}
