using System;
using System.Threading.Tasks;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Api.Crm.Abstraction.Services;
using Dornbracht.Api.Crm.Models.Payload;
using Microsoft.Xrm.Sdk;

namespace Dornbracht.Api.Crm.Processors.Callbacks
{
  public class FormSubmissionCallback : QueueMessageCallback<FormSubmissionPayload>
  {
    public FormSubmissionCallback(IFormServiceApiClient serviceClient)
    {
      ServiceClient = serviceClient;
    }

    private IFormServiceApiClient ServiceClient { get; }

    public async override Task Execute(ParameterCollection results, FormSubmissionPayload payload)
    {
      if (Environment.GetEnvironmentVariable("FORMS_SKIP_DELETION", EnvironmentVariableTarget.Process) == "true")
        return;

      await ServiceClient.DeleteSubmissionAsync(payload.SubmissionId.ToString());
    }
  }
}
