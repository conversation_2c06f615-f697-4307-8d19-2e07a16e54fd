using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Api.Crm.Models.Payload;
using Dornbracht.Api.Crm.Models.Pdh;
using Dornbracht.Api.Crm.Services;
using Microsoft.Xrm.Sdk;

namespace Dornbracht.Api.Crm.Processors
{
  public class LeadCreationEntityProcessor : QueueMessageEntityProcessor<LeadCreationPayload>
  {
    /**
     * The maximal field length for the afd_speclist field.
     */
    private const int SpecListMaxLength = 4000;

    /**
     * The separator for the afd_speclist field.
     */
    private const String SpecListSeparator = ";";

    /**
     * ProductLists service.
     */
    private TemporaryProductListServiceClient ProductListService { get; }

    public LeadCreationEntityProcessor(TemporaryProductListServiceClient serviceClient)
    {
      ProductListService = serviceClient;
    }

    public override async Task<(Entity, CrmAction)> ProcessAsync(LeadCreationPayload messagePayload)
    {
      var entity = new Entity("lead", messagePayload.Id);
      entity["leadid"] = messagePayload.Id;
      entity["emailaddress1"] = messagePayload.Data.Email;

      if (int.TryParse(messagePayload.Data.GenderCode, out var genderId))
        entity["afd_gender"] = new OptionSetValue(genderId);

      entity["firstname"] = messagePayload.Data.FirstName;
      entity["lastname"] = messagePayload.Data.LastName;
      entity["telephone1"] = messagePayload.Data.Phone;

      if (!string.Equals(messagePayload.Data.Country, "XY", StringComparison.InvariantCultureIgnoreCase))
      {
        entity["address1_country"] = messagePayload.Data.Country;
      }

      entity["companyname"] = messagePayload.Data.Company;

      if (int.TryParse(messagePayload.Data.TargetGroup, out var roleId))
        entity["afd_targetgroup"] = new OptionSetValue(roleId);

      // fixed values
      entity["subject"] = "Dornbracht.com - Registration";
      entity["leadsourcecode"] = new OptionSetValue(*********);

      // Different CampaignId for anonymous Users with Watchlist => DORSUP-175
      if (messagePayload.Data.UserHasSpecList.Equals("true"))
      {
        entity["campaignid"] = new EntityReference("campaign", new Guid("bdc0b9d1-882f-ee11-bdf4-000d3a2acbd3"));
      }
      else
      {
        entity["campaignid"] = new EntityReference("campaign", new Guid("b920f781-1c44-ea11-a812-000d3a4a1f5d"));
      }

      entity["donotbulkemail"] = true;
      entity["afd_visitdate"] = DateTime.UtcNow;
      entity["afd_newdgpaccount"] = true;
      entity["afd_dgpspecification"] = false;

      if (messagePayload.Data.UserHasSpecList.Equals("true")
          && !String.IsNullOrWhiteSpace(messagePayload.Data.AnonymousUserGuid)
          && !String.IsNullOrWhiteSpace(messagePayload.Data.UserLanguage)
          && messagePayload.Data.UserLanguage.Contains('-'))
      {
        AddProductListField(entity, messagePayload);
      }

      return await Task.FromResult((entity, CrmAction.Create));
    }

    /**
     * Adds the afd_speclist for the passed user guid.
     */
    private void AddProductListField(Entity entity, LeadCreationPayload messagePayload)
    {
      String[] split = messagePayload.Data.UserLanguage.Split("-");
      string market = split[0];
      string language = split[1];
      String guid = messagePayload.Data.AnonymousUserGuid;

      var apiTask = ProductListService.GetTemporaryProductListDetails(market, language, guid);
      apiTask.Wait();

      List<ProductListEntry> entries;
      switch (apiTask.Result)
      {
        case ProductListResult.ProductList list:
          entries = list.Entries;
          break;
        case ProductListResult.Forbidden _:
          entries = new List<ProductListEntry>();
          break;
        case ProductListResult.NotFound _:
          entries = new List<ProductListEntry>();
          break;
        default:
          entries = new List<ProductListEntry>();
          break;
      }

      if (entries.Count == 0)
      {
        return;
      }

      var builder = new StringBuilder();
      builder.AppendLine("productNumber" + SpecListSeparator + "productName");

      foreach (ProductListEntry product in entries)
      {
        String productNumber = product.ProductNumber.Replace("\"", "");
        ;
        String name = product.ProductName.Replace("\"", "");
        String line = $"{productNumber}{SpecListSeparator}\"{name}\"";

        if (builder.Length + line.Length < SpecListMaxLength)
        {
          builder.AppendLine(line);
        }
      }

      entity["afd_speclist"] = builder.ToString();
    }
  }
}
