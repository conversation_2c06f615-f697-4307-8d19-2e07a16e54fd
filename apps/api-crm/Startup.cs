using System;
using System.Reflection;
using Azure.Storage.Blobs;
using AzureFunctions.Extensions.Swashbuckle.Settings;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Api.Crm.Abstraction.Services;
using Dornbracht.Api.Crm.Models.Payload;
using Dornbracht.Api.Crm.Processors;
using Dornbracht.Api.Crm.Processors.Callbacks;
using Dornbracht.Api.Crm.Services;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Messaging;
using Dornbracht.Functions.Extensions.Search;
using Dornbracht.Functions.Extensions.Swagger;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

[assembly: FunctionsStartup(typeof(Dornbracht.Api.Crm.Startup))]
namespace Dornbracht.Api.Crm
{
  public class Startup : FunctionsStartup
  {
    public override void Configure(IFunctionsHostBuilder builder)
    {
      var baseUrl = Environment.GetEnvironmentVariable("API_BASE_URL", EnvironmentVariableTarget.Process);
      var hostKey = Environment.GetEnvironmentVariable("SWAGGER_JSON_FUNCTION_KEY", EnvironmentVariableTarget.Process);

      builder.AddFunctionsSwagger(Assembly.GetExecutingAssembly(), baseUrl, "v1/crm/swagger/json", hostKey, false, (options) =>
      {
        options.Title = "Dornbracht PDH API";
        options.XmlPath = "Dornbracht.Api.Crm.xml";
        options.Documents = new[]
                      {
                    new SwaggerDocument
                    {
                        Name = "v1",
                        Title = "Dornbracht CRM API",
                        Description = "Swagger documentation for the Dornbracht CRM API function layer.",
                        Version = "v1"
                    }
          };
      });

      builder.Services.AddLogging(configure => configure.AddConsole());
      builder.Services.AddHttpClient();
      builder.Services.AddSingleton<IQueueSerializer, QueueSerializer>();
      builder.Services.AddSingleton<IFormServiceApiClient, JotformServiceApiClient>();
      builder.Services.AddSingleton<ICrmRepository, CrmRepository>();

      builder.Services.AddSingleton<QueueMessageEntityProcessor<FormSubmissionPayload>, FormSubmissionEntityProcessor>();
      builder.Services.AddSingleton<QueueMessageEntityProcessor<LeadCreationPayload>, LeadCreationEntityProcessor>();
      builder.Services.AddSingleton<QueueMessageEntityProcessor<LeadUpdatePayload>, LeadUpdateEntityProcessor>();

      builder.Services.AddSingleton<QueueMessageCallback<FormSubmissionPayload>, FormSubmissionCallback>();

      builder.Services.AddTransient(_ => new BlobContainerClient(Environment.GetEnvironmentVariable("FORM_UPLOADS_CONNECTION_STRING", EnvironmentVariableTarget.Process), Environment.GetEnvironmentVariable("FORM_UPLOADS_CONTAINER_NAME", EnvironmentVariableTarget.Process)));

      builder.AddSearchServiceRepository();
      builder.AddMessagingServices();

      builder.Services.AddSingleton<IShowroomService, ShowroomService>();
      builder.Services.AddSingleton<TemporaryProductListServiceClient, TemporaryProductListServiceClient>();
    }
  }
}
