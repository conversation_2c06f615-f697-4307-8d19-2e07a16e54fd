using System;
using System.Threading.Tasks;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;

namespace Dornbracht.Api.Crm.Abstraction.Services
{
  public interface ICrmRepository
  {

    Task<EntityCollection> GetEntitiesByFetchXml(string fetch);

    Task<Entity> GetEntity(Guid id, string type);

    bool TryBulkCreateEntities(Entity[] entities, out ExecuteMultipleResponse response);

    bool TryBulkUpdateEntities(Entity[] entities, out ExecuteMultipleResponse response);
  }
}
