using System.Threading.Tasks;
using Microsoft.Xrm.Sdk;

namespace Dornbracht.Api.Crm.Abstraction
{
  public abstract class QueueMessageCallback<T> : IQueueMessageCallback where T : IMessagePayload
  {
    public abstract Task Execute(ParameterCollection results, T payload);

    public async Task Execute(ParameterCollection results, IMessagePayload messagePayload)
    {
      await Execute(results, (T)messagePayload);
    }
  }
}
