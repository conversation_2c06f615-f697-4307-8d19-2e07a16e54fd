using System.Threading.Tasks;
using Microsoft.Xrm.Sdk;

namespace Dornbracht.Api.Crm.Abstraction
{
  public abstract class QueueMessageEntityProcessor<T> : IQueueMessageEntityProcessor where T : IMessagePayload
  {
    public abstract Task<(Entity, CrmAction)> ProcessAsync(T messagePayload);

    public async Task<(Entity, CrmAction)> ProcessAsync(IMessagePayload messagePayload)
    {
      return await ProcessAsync((T)messagePayload);
    }
  }
}
