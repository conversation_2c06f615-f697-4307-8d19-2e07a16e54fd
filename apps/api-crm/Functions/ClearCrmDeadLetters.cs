using System.Threading;
using System;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Functions.Extensions.Attributes;
using Azure.Messaging.ServiceBus;
using Dornbracht.Functions.Extensions;

namespace Dornbracht.Api.Crm.Functions
{
  public class ClearCrmDeadLetters
  {
    public ClearCrmDeadLetters()
    {
      Client = new ServiceBusClient(Environment.GetEnvironmentVariable("ASB_CRM_QUEUE_PRIMARY_CONNECTION_STRING", EnvironmentVariableTarget.Process));
    }

    private ServiceBusClient Client { get; }

    /// <summary>
    /// Clears the dead-letter queue of the crm queue. Before executing: Always check the dead-letter reasons in Azure if there is a general error present.
    /// </summary>
    /// <remarks>This function requires a admin host key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <response code="200">Dead-letters successfully deleted.</response>
    /// <returns></returns>
    [FunctionName(nameof(ClearCrmDeadLetters))]
    [SwaggerTag("Administration")]
    public async Task Run([HttpTrigger(AuthorizationLevel.Admin, "get", Route = "crm/queue/clear")] HttpRequest req, ILogger log)
    {
      var queue = Environment.GetEnvironmentVariable("ASB_CRM_QUEUE_NAME", EnvironmentVariableTarget.Process);
      var deadLetterReceiver = Client.CreateReceiver(queue, new ServiceBusReceiverOptions() { SubQueue = SubQueue.DeadLetter });
      var messages = await deadLetterReceiver.ReceiveMessagesAsync(100, TimeSpan.FromSeconds(5));

      foreach (var message in messages)
      {
        log.LogInformation($"Removing dead-letter message {message.MessageId} (dead-letter reason: {message.DeadLetterReason}) from queue '{queue}'.");
        await deadLetterReceiver.CompleteMessageAsync(message);
      }
      req.AddNoCacheHeader();
    }
  }
}
