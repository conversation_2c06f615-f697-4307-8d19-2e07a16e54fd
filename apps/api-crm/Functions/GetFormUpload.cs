using System;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Crm.Functions
{
  public class GetFormUpload
  {
    public GetFormUpload(BlobContainerClient containerClient)
    {
      ContainerClient = containerClient;
    }

    private BlobContainerClient ContainerClient { get; }

    /// <summary>
    /// Returns a uploaded file from blob storage.
    /// </summary>
    /// <remarks>This function requires a function key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="id">Id/name of the blob in Azure Blob Storage.</param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">The requested file.</response>
    /// <response code="404">File not found.</response>
    [FunctionName(nameof(GetFormUpload))]
    [SwaggerTag("Form Endpoints")]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Function, "get", Route = "crm/form/upload/{id}")] HttpRequest req, string id, ILogger log)
    {
      var blobClient = ContainerClient.GetBlobClient(id);

      if (!(await blobClient.ExistsAsync()).Value)
      {
        return new NotFoundResult();
      }

      var blob = await blobClient.DownloadStreamingAsync();

      req.AddMaxAgeHttpCacheHeader(TimeSpan.FromDays(1));
      req.HttpContext.Response.Headers.ContentDisposition = $"inline; filename={id}";

      return new FileStreamResult(blob.Value.Content, "application/octet-stream");
    }
  }
}
