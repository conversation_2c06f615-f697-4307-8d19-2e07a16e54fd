using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Crm.Abstraction;

namespace Dornbracht.Api.Crm.Functions
{
  public class SyncShowrooms
  {
    public SyncShowrooms(IShowroomService showroomService)
    {
      ShowroomService = showroomService;
    }

    public IShowroomService ShowroomService { get; }

    /// <summary>
    /// Timed sync (daily at 01:30) for showrooms.
    /// </summary>
    /// <param name="timer"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    [FunctionName(nameof(SyncShowrooms))]
    public async Task Run([TimerTrigger("0 30 1 * * *")] TimerInfo timer, ILogger log)
    {
      if (timer.IsPastDue)
      {
        log.LogInformation("Showroom sync is running late!");
      }

      await ShowroomService.RebuildShowroomIndexAsync();
    }
  }
}
