using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Functions.Extensions.Attributes;

namespace Dornbracht.Api.Crm.Functions
{
  public class RebuildShowrooms
  {
    public RebuildShowrooms(IShowroomService showroomService)
    {
      ShowroomService = showroomService;
    }

    public IShowroomService ShowroomService { get; }

    /// <summary>
    /// Executes a manual rebuild of the showrooms index.
    /// </summary>
    /// <remarks>This function requires a function host (admin) key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Index rebuild was successful.</response>
    [FunctionName(nameof(RebuildShowrooms))]
    [SwaggerTag("Showroom Endpoints")]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Admin, "get", Route = "crm/showrooms/rebuild")] HttpRequest req, ILogger log)
    {
      await ShowroomService.RebuildShowroomIndexAsync();

      return new ContentResult();
    }
  }
}
