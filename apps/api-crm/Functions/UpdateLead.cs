using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Crm.Models;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Api.Crm.Models.Payload;
using Microsoft.AspNetCore.Mvc;
using Dornbracht.Api.Crm.Models.Lead;
using System;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Functions.Extensions.Attributes;

namespace Dornbracht.Api.Crm.Functions
{
  public class UpdateLead
  {
    /// <summary>
    /// Update a lead entity in CRM with new data.
    /// </summary>
    /// <remarks>This function requires a function key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="serviceBus"></param>
    /// <param name="log"></param>
    /// <response code="200">Lead update request successfully added to queue.</response>
    /// <response code="400">Error on request execution.</response>
    /// <returns></returns>
    [FunctionName(nameof(UpdateLead))]
    [SwaggerTag("CRM Lead Endpoints")]
    [Consumes(typeof(UpdateLeadRequestModel), "application/json")]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(AzureAdErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Function, "post", Route = "crm/lead/update")][RequestBodyType(typeof(UpdateLeadRequestModel), "Input claims from AAD B2C")] HttpRequest req, [ServiceBus("%ASB_CRM_QUEUE_NAME%", Connection = "ASB_CRM_QUEUE_PRIMARY_CONNECTION_STRING")] IAsyncCollector<CrmQueueMessage> serviceBus, ILogger log)
    {
      try
      {
        var data = await req.ReadFromJsonAsync<UpdateLeadRequestModel>();

        if (Guid.TryParse(data.LeadId, out var id))
        {
          await serviceBus.AddAsync(new CrmQueueMessage(MessageType.LeadUpdate, id.ToString("D"), new LeadUpdatePayload(id, data)));
          return new OkResult();
        }
        else
        {
          return new ObjectResult(new AzureAdErrorResponse("1.0.0", 0, "Unable to parse user id.")) { StatusCode = 400 };
        }
      }
      catch (Exception e)
      {
        log.LogError(e, $"Cannot enqueue lead update.");

        return new ObjectResult(new AzureAdErrorResponse("1.0.0", 0, "Unable to process user update.")) { StatusCode = 400 };
      }
    }
  }
}
