using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Api.Crm.Abstraction.Services;
using Dornbracht.Api.Crm.Models;
using Dornbracht.Api.Crm.Models.Payload;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.ServiceBus;
using Microsoft.Extensions.Logging;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;

namespace Dornbracht.Api.Crm.Functions
{
  public class CrmQueueListener
  {
    public CrmQueueListener(IServiceProvider serviceProvider, ICrmRepository crmRepository)
    {
      ServiceProvider = serviceProvider;
      CrmRepository = crmRepository;
    }

    private IServiceProvider ServiceProvider { get; }
    private ICrmRepository CrmRepository { get; }

    [FunctionName(nameof(CrmQueueListener))]
    public async Task Run(
      [ServiceBusTrigger("%ASB_CRM_QUEUE_NAME%", Connection = "ASB_CRM_QUEUE_PRIMARY_CONNECTION_STRING",
        AutoCompleteMessages = false)]
      ServiceBusReceivedMessage[] messages, ServiceBusMessageActions messageActions, ILogger log)
    {
      var createRequests =
        new List<(Entity Entity, ServiceBusReceivedMessage Message, string Id, IMessagePayload Payload)>();
      var updateRequests =
        new List<(Entity Entity, ServiceBusReceivedMessage Message, string Id, IMessagePayload Payload)>();

      foreach (var message in messages)
      {
        try
        {
          log.LogInformation("Received message: {Message}", message.Body.ToString());
          var crmMessage = message.Body.ToObjectFromJson<CrmQueueMessage>();
          log.LogInformation("Deserialized CRM message: {@CrmMessage}", crmMessage);

          var payload = GetMessagePayload(crmMessage);
          var (entity, action) = await ExecuteQueueMessageProcessor(payload);

          switch (action)
          {
            case CrmAction.Create:
              createRequests.Add((entity, message, crmMessage.ExternalIdentifier, payload));
              break;
            case CrmAction.Update:
              updateRequests.Add((entity, message, crmMessage.ExternalIdentifier, payload));
              break;
          }
        }
        catch (Exception e)
        {
          log.LogError(e, "Exception during execution of queue message entity processor! {} {}", e.Message, e.StackTrace);
          await messageActions.DeadLetterMessageAsync(message, "Transformation failed",
            $"Transformation to CRM Entity object failed with exception: {e.Message}");
        }
      }

      if (createRequests.Count > 0)
      {
        if (CrmRepository.TryBulkCreateEntities(createRequests.Select(c => c.Entity).ToArray(), out var createResponse))
        {
          await ProcessCrmBulkResponse(createResponse, createRequests, messageActions, log);
        }
      }

      if (updateRequests.Count > 0)
      {
        if (CrmRepository.TryBulkUpdateEntities(updateRequests.Select(c => c.Entity).ToArray(), out var updateResponse))
        {
          await ProcessCrmBulkResponse(updateResponse, updateRequests, messageActions, log);
        }
      }
    }

    private async Task<(Entity, CrmAction)> ExecuteQueueMessageProcessor<T>(T payload) where T : IMessagePayload
    {
      var processor =
        ServiceProvider.GetService(typeof(QueueMessageEntityProcessor<>).MakeGenericType(payload.GetType())) as
          IQueueMessageEntityProcessor;

      if (processor == null)
      {
        throw new Exception($"No message processor found for payload type {nameof(T)}");
      }

      return await processor.ProcessAsync(payload);
    }

    private async Task ProcessCrmBulkResponse(ExecuteMultipleResponse response,
      List<(Entity Entity, ServiceBusReceivedMessage Message, string Id, IMessagePayload Payload)> entityList,
      ServiceBusMessageActions messageActions, ILogger log)
    {
      foreach (var responseItem in response.Responses)
      {
        if (responseItem.Fault != null)
        {
          // faulty request
          var (entity, message, id, _) = entityList[responseItem.RequestIndex];

          if (Environment.GetEnvironmentVariable("CRM_LOG_ENTITY", EnvironmentVariableTarget.Process) == "true")
          {
            foreach (var attribute in entity.Attributes)
            {
              log.LogInformation($"{attribute.Key}: {attribute.Value}");
            }
          }

          log.LogError(
            $"Could not sync entity (external id: {id}) to CRM. Error: {responseItem.Fault.Message} {responseItem.Fault.InnerFault?.InnerFault?.Message}");
          await messageActions.DeadLetterMessageAsync(message, $"Error code {responseItem.Fault.ErrorCode}",
            $"{responseItem.Fault.Message} {responseItem.Fault.InnerFault?.InnerFault?.Message}");
        }
        else
        {
          // successful request
          var (_, message, id, payload) = entityList[responseItem.RequestIndex];

          try
          {
            await ProcessMessageCallback(payload, responseItem.Response.Results);
            await messageActions.CompleteMessageAsync(message);
          }
          catch (Exception e)
          {
            log.LogError(e, "Exception during callback call.");
            await messageActions.DeadLetterMessageAsync(message, "Exception on callback", e.Message);
          }
        }
      }
    }

    private async Task ProcessMessageCallback<T>(T payload, ParameterCollection results) where T : IMessagePayload
    {
      var callback =
        ServiceProvider.GetService(typeof(QueueMessageCallback<>).MakeGenericType(payload.GetType())) as
          IQueueMessageCallback;

      if (callback != null)
      {
        await callback.Execute(results, payload);
      }
    }

    private IMessagePayload GetMessagePayload(CrmQueueMessage message)
    {
      if (message.Payload is JsonElement payload)
      {
        switch (message.MessageType)
        {
          case MessageType.FormSubmission:
            return payload.Deserialize<FormSubmissionPayload>();
          case MessageType.LeadCreation:
            return payload.Deserialize<LeadCreationPayload>();
          case MessageType.LeadUpdate:
            return payload.Deserialize<LeadUpdatePayload>();
        }
      }

      return null;
    }
  }
}
