using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;
using Dornbracht.Api.Crm.Abstraction;
using System.Globalization;
using Dornbracht.Api.Crm.Models;
using Dornbracht.Functions.Extensions;
using System;
using Dornbracht.Functions.Extensions.Attributes;

namespace Dornbracht.Api.Crm.Functions
{
  public class SearchShowrooms
  {
    public SearchShowrooms(IShowroomService showroomService)
    {
      ShowroomService = showroomService;
    }

    public IShowroomService ShowroomService { get; }

    /// <summary>
    /// Returns a list of showrooms around the given geographic point. The list is sorted by distance (asc).
    /// </summary>
    /// <remarks>Responses contain a Cache-Control header for browser cache utilization to cache the request for 24 hours.</remarks>
    /// <param name="req"></param>
    /// <param name="latitude"></param>
    /// <param name="longitude"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">List of showrooms in range.</response>
    [FunctionName(nameof(SearchShowrooms))]
    [SwaggerTag("Showroom Endpoints")]
    [ProducesResponseType(typeof(ShowroomSearchResult[]), StatusCodes.Status200OK)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "crm/showrooms/search/{latitude}/{longitude}")] HttpRequest req, string latitude, string longitude, ILogger log)
    {
      if (!double.TryParse(latitude, NumberStyles.Float, CultureInfo.InvariantCulture, out var lat) || !double.TryParse(longitude, NumberStyles.Float, CultureInfo.InvariantCulture, out var lng))
      {
        return new BadRequestResult();
      }

      var result = new List<ShowroomSearchResult>();
      await foreach (var room in ShowroomService.SearchShowrooms(lat, lng))
      {
        result.Add(room);
      }

      req.AddMaxAgeHttpCacheHeader(TimeSpan.FromHours(24));

      return new OkObjectResult(result);
    }

  }
}
