using System;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Crm.Functions
{
  public class CleanUpFormFileUploads
  {
    public CleanUpFormFileUploads(BlobContainerClient containerClient)
    {
      ContainerClient = containerClient;
    }

    private BlobContainerClient ContainerClient { get; }

    private static TimeSpan MaxAge = TimeSpan.FromDays(7 * 4);

    /// <summary>
    /// Timed clean up (daily at 01:30) of form file uploads that are older than 4 weeks.
    /// </summary>
    /// <param name="timer"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    [FunctionName(nameof(CleanUpFormFileUploads))]
    public async Task Run([TimerTrigger("0 30 1 * * *")] TimerInfo timer, ILogger log)
    {
      if (timer.IsPastDue)
      {
        log.LogInformation("File upload clean up is running late!");
      }

      await foreach (var blob in ContainerClient.GetBlobsAsync())
      {
        if (!blob.Properties.CreatedOn.HasValue)
        {
          continue;
        }

        if (DateTime.UtcNow > blob.Properties.CreatedOn.Value.UtcDateTime + MaxAge)
        {
          log.LogInformation($"Deleting old file upload blob: {blob.Name} (created at {blob.Properties.CreatedOn.Value.UtcDateTime})");
          await ContainerClient.DeleteBlobIfExistsAsync(blob.Name);
        }
      }
    }
  }
}
