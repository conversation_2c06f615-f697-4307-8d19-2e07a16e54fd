using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Crm.Models;
using Dornbracht.Api.Crm.Abstraction.Services;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Api.Crm.Models.Payload;
using Microsoft.AspNetCore.Mvc;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.Abstraction;

namespace Dornbracht.Api.Crm.Functions
{
  public class FormSubmission
  {
    public FormSubmission(IQueueSerializer serializer, IFormServiceApiClient formServiceApiClient)
    {
      Serializer = serializer;
      FormServiceApiClient = formServiceApiClient;
    }

    private IQueueSerializer Serializer { get; }

    private IFormServiceApiClient FormServiceApiClient { get; }

    /// <summary>
    /// Jotform webhook endpoint for registration of new form submissions.
    /// </summary>
    /// <remarks>This function requires a function key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="formEntityType">Name of the entity that should be created in CRM.</param>
    /// <param name="log"></param>
    /// <param name="cancellationToken"></param>
    /// <response code="200">Form submission successfully added to queue.</response>
    /// <returns></returns>
    [FunctionName(nameof(FormSubmission))]
    [SwaggerTag("Form Endpoints")]
    [Consumes(typeof(JotformWebhookRequest), "multipart/form-data")]
    [return: ServiceBus("%ASB_CRM_QUEUE_NAME%", Connection = "ASB_CRM_QUEUE_PRIMARY_CONNECTION_STRING")]
    public async Task<byte[]> Run([HttpTrigger(AuthorizationLevel.Function, "post", Route = "crm/form/submission/{formEntityType}")][RequestBodyType(typeof(JotformWebhookRequest), "Jotform webhook form data")] HttpRequest req,
                                  string formEntityType,
                                  ILogger log,
                                  CancellationToken cancellationToken)
    {

      try
      {

        // Check if the operation has been cancelled
        if (cancellationToken.IsCancellationRequested)
        {
          log.LogWarning("Operation canceled => A cancellation token was received.");
          cancellationToken.ThrowIfCancellationRequested();
        }

        // Get Form ID and Submission ID from Jotform webhook multipart request
        if (ulong.TryParse(req.Form["formID"], out var formId) && ulong.TryParse(req.Form["submissionID"], out var submissionId))
        {

          log.LogInformation("Form ID: {FormId}, Submission ID: {SubmissionId}", formId, submissionId);

          // Serialize the message to be sent to the Service Bus queue
          return await Serializer.SerializeQueueMessage(
            new CrmQueueMessage(MessageType.FormSubmission, submissionId.ToString(), new FormSubmissionPayload(formId, submissionId.ToString(), formEntityType)));
        }
      }
      catch (OperationCanceledException ex)
      {
        log.LogError(ex, "Operation canceled: {ErrorMessage}", ex.Message);
        throw;
      }
      catch (Exception ex)
      {
        log.LogError($"General Error occured: {ex.Message}", ex);
      }

      // Azure Service Bus output binding sends no message to the queue if null is returned, but only if the return type of the function is string or byte[]!
      return null;
    }
  }
}