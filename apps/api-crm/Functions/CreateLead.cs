using System;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Crm.Models;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Api.Crm.Models.Payload;
using Dornbracht.Api.Crm.Models.Lead;
using Microsoft.AspNetCore.Mvc;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Functions.Extensions.Attributes;

namespace Dornbracht.Api.Crm.Functions
{
  public class CreateLead
  {
    /// <summary>
    /// Create a new lead entity in CRM.
    /// </summary>
    /// <remarks>This function requires a function key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="serviceBus"></param>
    /// <param name="log"></param>
    /// <response code="200">Lead creation request successfully added to queue. Returns CRM lead ID as output claim.</response>
    /// <response code="400">Error on request execution.</response>
    /// <returns>Returns CRM lead ID as output claim.</returns>
    [FunctionName(nameof(CreateLead))]
    [SwaggerTag("CRM Lead Endpoints")]
    [Consumes(typeof(CreateLeadRequestModel), "application/json")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(CreateLeadResult), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(AzureAdErrorResponse), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Function, "post", Route = "crm/lead/create")][RequestBodyType(typeof(CreateLeadRequestModel), "Input claims from AAD B2C")] HttpRequest req, [ServiceBus("%ASB_CRM_QUEUE_NAME%", Connection = "ASB_CRM_QUEUE_PRIMARY_CONNECTION_STRING")] IAsyncCollector<CrmQueueMessage> serviceBus, ILogger log)
    {
      try
      {
        var data = await req.ReadFromJsonAsync<CreateLeadRequestModel>();
        var id = Guid.NewGuid();

        log.LogDebug("CreateLead for: {} {} {}, hasSpecList: {}", data.Country, data.UserLanguage, data.AnonymousUserGuid, data.UserHasSpecList);
        await serviceBus.AddAsync(new CrmQueueMessage(MessageType.LeadCreation, id.ToString("D"), new LeadCreationPayload(id, data)));

        // return lead id as output claim to AAD
        return new OkObjectResult(new CreateLeadResult { LeadId = id.ToString("D") });
      }
      catch (Exception e)
      {
        log.LogError(e, $"Cannot enqueue lead registration.");

        return new ObjectResult(new AzureAdErrorResponse("1.0.0", 0, "Unable to process user registration.")) { StatusCode = 400 };
      }
    }
  }
}
