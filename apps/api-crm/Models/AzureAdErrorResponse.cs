using System.Text.Json.Serialization;

namespace Dornbracht.Api.Crm.Models
{
  public class AzureAdErrorResponse
  {
    public AzureAdErrorResponse(string version, int status, string userMessage)
    {
      Version = version;
      Status = status;
      UserMessage = userMessage;
    }

    [JsonPropertyName("version")]
    public string Version { get; set; }

    [JsonPropertyName("status")]
    public int Status { get; set; }

    [JsonPropertyName("code")]
    public string Code { get; set; }

    [JsonPropertyName("userMessage")]
    public string UserMessage { get; set; }

    [JsonPropertyName("developerMessage")]
    public string DeveloperMessage { get; set; }

    [JsonPropertyName("requestId")]
    public string RequestId { get; set; }

    [JsonPropertyName("moreInfo")]
    public string MoreInfo { get; set; }
  }
}
