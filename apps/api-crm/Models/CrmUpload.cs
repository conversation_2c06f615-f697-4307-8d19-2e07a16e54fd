using System.Text.Json.Serialization;

namespace Dornbracht.Api.Crm.Models
{
  public record struct CrmUpload
  {
    [JsonPropertyName("Url")]
    public string Url { get; init; }

    [JsonPropertyName("OriginalFileName")]
    public string OriginalFileName { get; init; }

    [JsonPropertyName("ContentType")]
    public string ContentType { get; init; }

    [JsonPropertyName("ContentLength")]
    public long ContentLength { get; init; }

    [JsonPropertyName("StoredFileName")]
    public string StoredFileName { get; init; }

    [JsonPropertyName("StoredFilePath")]
    public string StoredFilePath { get; init; }
  }
}
