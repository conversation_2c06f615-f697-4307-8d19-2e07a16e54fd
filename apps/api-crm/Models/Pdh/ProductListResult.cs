using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Crm.Models.Pdh
{
  public record ProductListResult
  {
    public record ProductList : ProductListResult
    {
      [JsonPropertyName("entries")] public List<ProductListEntry> Entries { get; set; }
    }

    public record NotFound : ProductListResult
    {
    }

    public record Forbidden : ProductListResult
    {
    }
  }
}
