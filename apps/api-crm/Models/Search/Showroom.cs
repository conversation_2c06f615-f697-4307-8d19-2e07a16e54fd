using System.Text.Json.Serialization;
using Azure.Core.Serialization;
using Azure.Search.Documents.Indexes;
using Microsoft.Spatial;

namespace Dornbracht.Api.Crm.Models.Search
{
  public class Showroom
  {
    public const string IndexName = "showrooms";

    #region Base Data

    [SimpleField(IsKey = true, IsFilterable = true, IsSortable = true)]
    public string Id { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string Name { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string Channel { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string SubChannel { get; set; }

    #endregion

    #region Geodata

    [SimpleField(IsFilterable = true, IsSortable = true)]
    [JsonConverter(typeof(MicrosoftSpatialGeoJsonConverter))]
    public GeographyPoint Location { get; set; }

    #endregion

    #region Address

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string AddressLine1 { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string AddressLine2 { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string PostalCode { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string City { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string State { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string Country { get; set; }
    #endregion

    #region Contact

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string Web { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string Email { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string Fax { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string Phone { get; set; }

    #endregion
  }
}
