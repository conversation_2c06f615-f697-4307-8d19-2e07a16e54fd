
using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace Dornbracht.Api.Crm.Models.Lead
{
  // ASB SDK uses Newtonsoft.Json for serialization, we use System.Text.Json. This is why we need both attributes for the serialization to work in both cases. This is because of the attribute names coming from AAD B2C.
  public record UpdateLeadRequestModel
  {
    [JsonPropertyName("objectId")]
    [JsonProperty("objectId")]
    public string ObjectId { get; set; }

    [JsonPropertyName("extension_crmLeadId")]
    [Json<PERSON>roperty("extension_crmLeadId")]
    public string LeadId { get; set; }

    [JsonPropertyName("policyName")]
    [JsonProperty("policyName")]
    public string PolicyName { get; set; }

    [JsonPropertyName("extension_crmLeadGenderCodeId")]
    [J<PERSON><PERSON>roperty("extension_crmLeadGenderCodeId")]
    public string GenderCode { get; set; }

    [<PERSON><PERSON><PERSON>roper<PERSON><PERSON><PERSON>("surname")]
    [<PERSON><PERSON><PERSON><PERSON><PERSON>("surname")]
    public string LastName { get; set; }

    [JsonPropertyName("givenName")]
    [JsonProperty("givenName")]
    public string FirstName { get; set; }

    [JsonPropertyName("extension_company")]
    [JsonProperty("extension_company")]
    public string Company { get; set; }

    [JsonPropertyName("extension_crmLeadTargetGroupId")]
    [JsonProperty("extension_crmLeadTargetGroupId")]
    public string TargetGroup { get; set; }

    [JsonPropertyName("country")]
    [JsonProperty("country")]
    public string Country { get; set; }

    [JsonPropertyName("extension_phone")]
    [JsonProperty("extension_phone")]
    public string Phone { get; set; }

    [JsonPropertyName("streetAddress")]
    [JsonProperty("streetAddress")]
    public string Street { get; set; }

    [JsonPropertyName("postalCode")]
    [JsonProperty("postalCode")]
    public string PostalCode { get; set; }

    [JsonPropertyName("city")]
    [JsonProperty("city")]
    public string City { get; set; }

    [JsonPropertyName("state")]
    [JsonProperty("state")]
    public string State { get; set; }

    [JsonPropertyName("extension_userHasSpecList")]
    [JsonProperty("extension_userHasSpecList")]
    public string UserHasSpecList { get; set; }
  }
}
