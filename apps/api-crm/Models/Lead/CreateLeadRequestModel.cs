
using System.Text.Json.Serialization;
using Newtonsoft.Json;

namespace Dornbracht.Api.Crm.Models.Lead
{
  // ASB SDK uses Newtonsoft.Json for serialization, we use System.Text.Json. This is why we need both attributes for the serialization to work in both cases. This is because of the attribute names coming from AAD B2C.
  public record CreateLeadRequestModel
  {
    [JsonPropertyName("objectId")]
    [JsonProperty("objectId")]
    public string ObjectId { get; set; }

    [JsonPropertyName("userLanguage")]
    [J<PERSON><PERSON>roperty("userLanguage")]
    public string UserLanguage { get; set; }

    [JsonPropertyName("anonymousUserGuid")]
    [J<PERSON><PERSON>roperty("anonymousUserGuid")]
    public string AnonymousUserGuid { get; set; }

    [JsonPropertyName("policyName")]
    [J<PERSON><PERSON>roperty("policyName")]
    public string PolicyName { get; set; }

    [JsonPropertyName("extension_crmLeadGenderCodeId")]
    [JsonProperty("extension_crmLeadGenderCodeId")]
    public string GenderCode { get; set; }

    [<PERSON><PERSON><PERSON><PERSON>ty<PERSON><PERSON>("surname")]
    [<PERSON><PERSON><PERSON><PERSON><PERSON>("surname")]
    public string LastName { get; set; }

    [JsonPropertyName("givenName")]
    [JsonProperty("givenName")]
    public string FirstName { get; set; }

    [JsonPropertyName("email")]
    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonPropertyName("extension_company")]
    [JsonProperty("extension_company")]
    public string Company { get; set; }

    [JsonPropertyName("extension_crmLeadTargetGroupId")]
    [JsonProperty("extension_crmLeadTargetGroupId")]
    public string TargetGroup { get; set; }

    [JsonPropertyName("country")]
    [JsonProperty("country")]
    public string Country { get; set; }

    [JsonPropertyName("extension_phone")]
    [JsonProperty("extension_phone")]
    public string Phone { get; set; }

    [JsonPropertyName("extension_agreeTerms")]
    [JsonProperty("extension_agreeTerms")]
    public string Terms { get; set; }

    [JsonPropertyName("extension_userHasSpecList")]
    [JsonProperty("extension_userHasSpecList")]
    public string UserHasSpecList { get; set; }

  }
}
