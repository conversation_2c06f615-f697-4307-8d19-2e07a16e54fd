using System;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Crm.Models.Api.Form.Jotform
{
  public record struct JotformDateTime
  {
    [JsonPropertyName("day")]
    public string Day { get; init; }

    [JsonPropertyName("month")]
    public string Month { get; init; }

    [JsonPropertyName("year")]
    public string Year { get; init; }

    [JsonPropertyName("datetime")]
    public string DateTime { get; init; }


    public int? GetDay()
    {
      if (int.TryParse(Day, out var day))
        return day;

      return null;
    }

    public int? GetMonth()
    {
      if (int.TryParse(Month, out var month))
        return month;

      return null;
    }

    public int? GetYear()
    {
      if (int.TryParse(Year, out var year))
        return year;

      return null;
    }

    public DateTime? ToDateTime()
    {
      var day = GetDay();
      var month = GetMonth();
      var year = GetYear();

      if (day.HasValue && month.HasValue && year.HasValue)
      {
        return new DateTime(year.Value, month.Value, day.Value);
      }

      return null;
    }
  }
}
