using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Crm.Models.Api.Form.Jotform
{
  public record struct JotformSubmission
  {
    [JsonPropertyName("id")]
    public string Id { get; init; }

    [JsonPropertyName("form_id")]
    public string FormId { get; init; }

    [JsonPropertyName("answers")]
    public Dictionary<string, JotformSubmissionField> Answers { get; set; }
  }
}
