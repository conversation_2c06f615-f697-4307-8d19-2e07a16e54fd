using System.Text.Json.Serialization;

namespace Dornbracht.Api.Crm.Models.Api.Form.Jotform
{
  public record struct JotformSubmissionField
  {
    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("answer")]
    public object Answer { get; init; }

    [JsonPropertyName("prettyFormat")]
    public string PrettyFormat { get; init; }

    [JsonPropertyName("type")]
    public string Type { get; init; }
  }
}
