using System.Text.Json.Serialization;

namespace Dornbracht.Api.Crm.Models.Api.Form.Jotform
{
  public record struct JotformFormQuestion
  {
    [JsonPropertyName("calcValues")]
    public string CalcValues { get; init; }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("type")]
    public string Type { get; init; }

    [JsonPropertyName("qid")]
    public string Id { get; init; }

    [JsonPropertyName("options")]
    public string Options { get; init; }
  }
}
