using System.Linq;
using System;
using System.Net.Http;
using Dornbracht.Api.Crm.Abstraction.Services;
using Dornbracht.Api.Crm.Models.Api.Form;
using Dornbracht.Api.Crm.Models.Api.Form.Jotform;
using System.Threading.Tasks;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace Dornbracht.Api.Crm.Services
{
  public class JotformServiceApiClient : IFormServiceApiClient
  {
    private HttpClient Client { get; init; }

    private const string BaseUrl = "https://dornbracht.jotform.com/API";

    private const string CrmTypeSplitter = "__";

    public JotformServiceApiClient(IHttpClientFactory httpClientFactory)
    {
      Client = httpClientFactory.CreateClient();
    }

    private HttpRequestMessage CreateRequest(HttpMethod method, string url)
    {
      var message = new HttpRequestMessage(method, $"{BaseUrl}/{url}");
      message.Headers.Add("APIKEY", Environment.GetEnvironmentVariable("JOTFORM_API_KEY", EnvironmentVariableTarget.Process));

      return message;
    }

    public async Task<Submission> GetSubmissionAsync(string id)
    {
      var request = CreateRequest(HttpMethod.Get, $"submission/{id}");
      var response = await Client.SendAsync(request);
      var data = await response.Content.ReadAsAsync<JotformResponse<JotformSubmission>>();

      return new Submission(data.Content.Id, data.Content.Answers.Where(answer => answer.Value.Name.IndexOf(CrmTypeSplitter) > -1 || answer.Value.Type == SubmissionField.UploadFieldType).Select(answer =>
      {
        var nameAndType = answer.Value.Type == SubmissionField.UploadFieldType ? new[] { answer.Value.Name, string.Empty } : answer.Value.Name.Split(CrmTypeSplitter, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

        switch (answer.Value.Answer)
        {
          case JArray valueList:
            return new SubmissionField(nameAndType[0], nameAndType[1], valueList, answer.Value.Type);
          case string value:
            return new SubmissionField(nameAndType[0], nameAndType[1], value, answer.Value.Type);
          case JObject objValue:
            return new SubmissionField(nameAndType[0], nameAndType[1], objValue.ToString(Formatting.None), answer.Value.Type);
        }

        return new SubmissionField(nameAndType[0], nameAndType[1], string.Empty, string.Empty);
      }));
    }

    public async Task<IEnumerable<FormQuestion>> GetFormQuestionsAsync(string formId)
    {
      var request = CreateRequest(HttpMethod.Get, $"form/{formId}/questions");
      var response = await Client.SendAsync(request);
      var data = await response.Content.ReadAsAsync<JotformResponse<Dictionary<string, JotformFormQuestion>>>();

      return data.Content.Where(question => question.Value.Name.IndexOf(CrmTypeSplitter) > -1).Select(question =>
      {
        var nameAndType = question.Value.Name.Split(CrmTypeSplitter, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);
        var options = question.Value.Options?.Split('|') ?? Array.Empty<string>();
        var values = question.Value.CalcValues?.Split('|') ?? Array.Empty<string>();
        var selectValues = new Dictionary<string, string>();

        for (var i = 0; i < options.Length; i++)
        {
          if (values.Length > options.Length)
          {
            // Jotform select elements can have an "empty" option that can have a calc value but is not listed as option
            selectValues.Add(options[i], values[i + 1]);
          }
          else
          {

            if (i < values.Length)
            {
              selectValues.Add(options[i], values[i]);
            }
          }
        }

        return new FormQuestion(nameAndType[0], question.Value.Type, nameAndType[1], selectValues);
      });
    }

    public async Task DeleteSubmissionAsync(string id)
    {
      var request = CreateRequest(HttpMethod.Delete, $"submission/{id}");

      await Client.SendAsync(request);
    }
  }
}
