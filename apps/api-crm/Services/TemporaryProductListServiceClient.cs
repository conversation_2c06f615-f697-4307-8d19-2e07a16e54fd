using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Dornbracht.Api.Crm.Models.Pdh;
using Dornbracht.Api.Crm.Configuration;
using Dornbracht.Functions.Extensions.Builder;

namespace Dornbracht.Api.Crm.Services
{
  public class TemporaryProductListServiceClient
  {
    private string BaseUrl =>
      Environment.GetEnvironmentVariable("API_BASE_URL", EnvironmentVariableTarget.Process);

    private string WebsiteUrl =>
      Environment.GetEnvironmentVariable("DORNBRACHT_DOMAIN", EnvironmentVariableTarget.Process);

    private HttpClient Client { get; init; }

    public TemporaryProductListServiceClient(IHttpClientFactory httpClientFactory)
    {
      Client = httpClientFactory.CreateClient(HttpClientNames.PDH_CLIENT);
    }

    private void SetDefaultHeaders(HttpRequestMessage requestMessage)
    {
      requestMessage.Headers.Add("Accept", "application/json");
      requestMessage.Headers.Add("Origin", WebsiteUrl);
    }

    private HttpRequestMessage CreateRequest(HttpMethod method, string requestUrl)
    {
      var request = new HttpRequestMessage(method, $"{BaseUrl.TrimEnd('/')}/{requestUrl.TrimStart('/')}");

      SetDefaultHeaders(request);

      return request;
    }

    /**
     * Get temporary productlist for the passed owner.
     */
    public async Task<ProductListResult> GetTemporaryProductListDetails(string market, string language, String owner)
    {
      var requestUrl = $"v1/productlists/temporary/{market}/{language}/{owner}";

      var request = CreateRequest(HttpMethod.Get, requestUrl);
      var builder = new HttpRequestActionBuilder<ProductListResult>(Client, request);

      return await builder
        .MapJson<ProductListResult.ProductList>(HttpStatusCode.OK)
        .Map<ProductListResult.Forbidden>(HttpStatusCode.Forbidden)
        .Map<ProductListResult.NotFound>(HttpStatusCode.NotFound)
        .ExecuteAsync();
    }
  }
}
