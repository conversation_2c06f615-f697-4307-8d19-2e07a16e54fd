using System;
using System.Threading.Tasks;
using Dornbracht.Api.Crm.Abstraction.Services;
using Microsoft.Extensions.Logging;
using Microsoft.PowerPlatform.Dataverse.Client;
using Microsoft.Xrm.Sdk;
using Microsoft.Xrm.Sdk.Messages;
using Microsoft.Xrm.Sdk.Query;

namespace Dornbracht.Api.Crm.Services
{
  public class CrmRepository : ICrmRepository
  {
    public CrmRepository(ILogger log)
    {
      Log = log;
    }

    private ILogger Log { get; }

    public async Task<EntityCollection> GetEntitiesByFetchXml(string fetch)
    {
      using (var client = CreateServiceClient())
      {
        return await client.RetrieveMultipleAsync(new FetchExpression(fetch));
      }
    }

    public async Task<Entity> GetEntity(Guid id, string type)
    {
      using (var client = CreateServiceClient())
      {
        return await client.RetrieveAsync(type, id, new ColumnSet(true));
      }
    }

    public bool TryBulkCreateEntities(Entity[] entities, out ExecuteMultipleResponse response)
    {
      response = null;

      var requests = new OrganizationRequestCollection();
      foreach (var entity in entities)
      {
        requests.Add(new CreateRequest { Target = entity });
      }

      if (requests.Count > 0)
      {
        return ExecuteMultipleRequest(requests, out response);
      }

      return true;
    }

    public bool TryBulkUpdateEntities(Entity[] entities, out ExecuteMultipleResponse response)
    {
      response = null;

      var requests = new OrganizationRequestCollection();
      foreach (var entity in entities)
      {
        requests.Add(new UpdateRequest { Target = entity });
      }

      if (requests.Count > 0)
      {
        return ExecuteMultipleRequest(requests, out response);
      }

      return true;
    }

    private ServiceClient CreateServiceClient()
    {
      return new ServiceClient(Environment.GetEnvironmentVariable("CRM_CONNECTION_STRING", EnvironmentVariableTarget.Process), Log);
    }

    private bool ExecuteMultipleRequest(OrganizationRequestCollection requests, out ExecuteMultipleResponse response)
    {
      response = null;

      var request = new ExecuteMultipleRequest
      {
        Settings = new ExecuteMultipleSettings
        {
          ContinueOnError = true,
          ReturnResponses = true
        },
        Requests = requests
      };

      try
      {
        using (var client = CreateServiceClient())
        {
          response = (ExecuteMultipleResponse)client.ExecuteOrganizationRequest(request);
          return true;
        }
      }
      catch (Exception e)
      {
        Log.LogError(e, "Exception while sending organization request to CRM.");
        return false;
      }
    }
  }
}
