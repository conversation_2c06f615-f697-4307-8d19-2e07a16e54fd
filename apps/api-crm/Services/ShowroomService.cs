using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Azure.Search.Documents.Models;
using Dornbracht.Api.Crm.Abstraction;
using Dornbracht.Api.Crm.Abstraction.Services;
using Dornbracht.Api.Crm.Extensions;
using Dornbracht.Api.Crm.Models;
using Dornbracht.Api.Crm.Models.Search;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Geography;
using Microsoft.Extensions.Logging;
using Microsoft.Spatial;
using Microsoft.Xrm.Sdk;

namespace Dornbracht.Api.Crm.Services
{
  public class ShowroomService : IShowroomService
  {
    /// <summary>
    /// Default distance (in km) for showroom search.
    /// </summary>
    private const int DefaultDistance = 50;

    /// <summary>
    /// Number of time the search radius should be extended if no results are found. (DefaultDistance * NumberOfRetries)
    /// </summary>
    private const int MaxRadiusExtensions = 5;

    // TODO: Maybe move filter to storyblok
    private const string CrmFilter = @"
<fetch version='1.0' output-format='xml-platform' mapping='logical' distinct='false'>
  <entity name='account'>
    <attribute name='name' />
    <attribute name='afd_subchannelid' />
    <attribute name='ownerid' />
    <attribute name='afd_channelid' />
    <attribute name='address1_city' />
    <attribute name='address1_line1' />
    <attribute name='address1_line2' />
    <attribute name='accountid' />
    <attribute name='address1_postalcode' />
    <attribute name='address1_country' />
    <attribute name='address1_country' />
    <attribute name='emailaddress1' />
    <attribute name='websiteurl' />
    <attribute name='fax' />
    <attribute name='afd_dgpsynchronisation' />
    <attribute name='telephone1' />
    <attribute name='address1_longitude' />
    <attribute name='address1_latitude' />
    <attribute name='statecode' />
    <order attribute='name' descending='false' />
    <filter type='and'>
      <condition attribute='statecode' operator='eq' value='0' />
      <condition attribute='afd_dgpsynchronisation' operator='eq' value='1' />
      <filter type='or'>
        <condition attribute='afd_channelid' operator='eq' uiname='Retail' uitype='afd_channel' value='{1BC6E5E3-41D9-E411-80C2-00155DDCE32A}' />
        <filter type='and'>
          <condition attribute='afd_channelid' operator='eq' uiname='Craftsman' uitype='afd_channel' value='{C5D0AC33-42D9-E411-80C2-00155DDCE32A}' />
          <filter type='or'>
            <condition attribute='afd_subchannelid' operator='eq' uiname='Plumber with Showroom (Direct)' uitype='afd_subchannel' value='{1ACD4D78-AFBF-EB11-BACC-000D3A49A97E}' />
            <condition attribute='afd_subchannelid' operator='eq' uiname='Plumber with Showroom (Indirect)' uitype='afd_subchannel' value='{79947B6A-AFBF-EB11-BACC-000D3A49A97E}' />
          </filter>
        </filter>
        <filter type='and'>
          <condition attribute='afd_channelid' operator='eq' uiname='Wholesale' uitype='afd_channel' value='{D10790E5-A8BF-EB11-BACC-000D3A49A97E}' />
          <condition attribute='afd_subchannelid' operator='eq' uiname='Wholesaler with Showroom' uitype='afd_subchannel' value='{FED42568-B0BF-EB11-BACC-000D3A49A97E}' />
        </filter>
      </filter>
    </filter>
  </entity>
</fetch>";

    private ISearchRepository SearchRepository { get; }

    private ICrmRepository CrmRepository { get; }

    private ILogger Log { get; }

    public ShowroomService(ISearchRepository searchRepository, ICrmRepository crmRepository, ILogger<ShowroomService> log)
    {
      SearchRepository = searchRepository;
      CrmRepository = crmRepository;
      Log = log;
    }

    public async Task RebuildShowroomIndexAsync(bool purge = false)
    {
      if (purge)
      {
        Log.LogDebug($"purge: delete index '{Showroom.IndexName}'");
        await SearchRepository.DeleteIndexAsync(Showroom.IndexName);
      }

      Log.LogDebug($"ensure than the index '{Showroom.IndexName}' exists");
      await SearchRepository.CreateOrUpdateIndexAsync<Showroom>(Showroom.IndexName);

      Log.LogDebug("get all showrooms from crm");
      var list = await CrmRepository.GetEntitiesByFetchXml(CrmFilter);
      Log.LogDebug($"{list.Entities.Count} showrooms from crm");

      Log.LogDebug($"create the index '{Showroom.IndexName}' as batch");
      var batch = IndexDocumentsBatch.Create<Showroom>();

      Log.LogDebug("map entities to showroom models and add or update all showrooms");
      var rooms = list.Entities.Select(MapCrmEntityToModel);
      foreach (var room in rooms)
      {
        batch.Actions.Add(IndexDocumentsAction.MergeOrUpload(room));
      }

      Log.LogDebug($"get search client by index '{Showroom.IndexName}'");
      var client = SearchRepository.GetSearchClient(Showroom.IndexName, true);

      Log.LogDebug("remove showrooms from index that do not exist anymore");
      var indexedRoomIds = await SearchRepository.GetFieldFromAllDocumentsAsync<Showroom, string>(client, room => room.Id);
      int documentsToRemove = 0;
      foreach (var id in indexedRoomIds)
      {
        if (!rooms.Any(r => r.Id == id))
        {
          batch.Actions.Add(IndexDocumentsAction.Delete(new Showroom { Id = id }));
          documentsToRemove++;
        }
      }

      Log.LogDebug($"{documentsToRemove} documents to remove");

      if (batch.Actions.Count > 0)
      {
        try
        {
          Log.LogDebug($"send batch ({batch.Actions.Count} actions) to azure search");
          await SearchRepository.IndexDocumentsAsync(client, batch);
        }
        catch (System.Exception ex)
        {
          Log.LogError(ex, "Error while sending batch to azure search");
          throw;
        }
      }
    }

    public async IAsyncEnumerable<ShowroomSearchResult> SearchShowrooms(double latitude, double longitude)
    {
      var location = GeographyPoint.Create(latitude, longitude);
      var client = SearchRepository.GetSearchClient(Showroom.IndexName);

      SearchResults<Showroom> result;
      var i = 1;
      do
      {
        result = await SearchRepository.SearchGeoSpatial<Showroom>(client, s => s.Location, latitude, longitude, 50 * i);
        i++;
      }
      while (result.TotalCount == 0 && i <= MaxRadiusExtensions);

      await foreach (var room in result.GetResultsAsync())
      {
        yield return new ShowroomSearchResult(location.GetDistance(room.Document.Location), room.Document);
      }
    }

    private Showroom MapCrmEntityToModel(Entity entity)
    {
      return new Showroom
      {
        Id = entity.Id.ToString(),
        Name = entity.GetAttribute<string>("name"),
        Channel = entity.GetAttribute<EntityReference>("afd_channelid").Id.ToString(),
        SubChannel = entity.GetAttribute<EntityReference>("afd_subchannelid").Id.ToString(),
        Location = GeographyPoint.Create(entity.GetAttribute<double>("address1_latitude"), entity.GetAttribute<double>("address1_longitude")),
        AddressLine1 = entity.GetAttribute<string>("address1_line1"),
        AddressLine2 = entity.GetAttribute<string>("address1_line2"),
        PostalCode = entity.GetAttribute<string>("address1_postalcode"),
        City = entity.GetAttribute<string>("address1_city"),
        State = entity.GetAttribute<string>("address1_stateorprovince"),
        Country = entity.GetAttribute<string>("address1_country"),
        Web = entity.GetAttribute<string>("websiteurl"),
        Email = entity.GetAttribute<string>("emailaddress1"),
        Fax = entity.GetAttribute<string>("fax"),
        Phone = entity.GetAttribute<string>("telephone1"),
      };
    }
  }
}
