# CRM Azure Functions

## Prerequisites

- [Azure Functions Extension](https://learn.microsoft.com/en-us/azure/azure-functions/functions-develop-vs-code?tabs=node-v4%2Cpython-v2%2Cisolated-process&pivots=programming-language-csharp#prerequisites)
- [Azurite Extension](https://marketplace.visualstudio.com/items?itemName=Azurite.azurite). This is required for running a non-HTTP-Trigger Function and [Azure Blob Storage](https://docs.microsoft.com/en-us/dotnet/api/overview/azure/Storage.Blobs-readme) locally!
- Add a firewall rule to allow communication over AMQP port (5671) with the Azure Service Bus.
- Start Azurite Blob Service.

## Configuration

- Create `local.settings.json` file with the following properties:

  ```json
  {
    "IsEncrypted": false,
    "Values": {
      "AzureWebJobsStorage": "UseDevelopmentStorage=true",
      "API_BASE_URL": "http://localhost:7072",
      "FUNCTIONS_WORKER_RUNTIME": "dotnet",
      "FUNCTIONS_EXTENSION_VERSION": "~4",
      "ASB_CRM_QUEUE_PRIMARY_CONNECTION_STRING": "Endpoint=sb://{hostname}/;SharedAccessKeyName=CrmQueueAccessKey;SharedAccessKey={accessKey};EntityPath=web-crm-asbq-0-d-d50",
      "ASB_CRM_QUEUE_NAME": "web-crm-asbq-0-d-d50",
      "JOTFORM_API_KEY": "",
      "CRM_CONNECTION_STRING": "Url={url}; Username={username}; Password={password}; AuthenticationType=OAuth; Timeout=20; LoginPrompt=Never; AppId=51f81489-12ee-4a9e-aaae-a2591f45987d; RedirectUri=app://58145B91-0C36-4500-8554-080854F2AC97",
      "FORM_UPLOADS_CONNECTION_STRING": "UseDevelopmentStorage=true",
      "FORM_UPLOADS_CONTAINER_NAME": "uploads",
      "SEARCH_SERVICES_MANAGEMENT_KEY": "{search service management key}",
      "SEARCH_SERVICES_QUERY_KEY": "{search service query key}",
      "SEARCH_SERVICES_ENDPOINT": "https://{search service name}.search.windows.net",
    },
    "Host": {
      "CORS": "*"
    }
  }
  ```

- Fill the environment variables with the appropriate values (`ASB_CRM_QUEUE_PRIMARY_CONNECTION_STRING`, `CRM_CONNECTION_STRING`, `JOTFORM_API_KEY` and Search Service variables).

## Add processing for new CRM entities

1. Create a new payload model that inherits from `IMessagePayload`.
1. Add a new message type to the `MessageType` enum.
1. Create a function that adds a new `CrmQueueMessage` that contains an instance of the new message payload class and is marked with the new message type to the Azure Service Bus.
1. Extend the message payload de-serialization in the `CrmQueueListener` function (`GetMessagePayload`) with the new `MessageType` and `IMessagePayload`.
1. Implement a new `QueueMessageEntityProcessor` for the new message payload type. This processor should return the CRM `Entity` object that should be written to the CRM and the action that should be executed by the CRM (create or update).
1. Optional implement a new `QueueMessageCallback` for the new message payload type to execute any action after the Entity was successfully sent to the CRM.
