<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <RootNamespace>Dornbracht.Api.Crm</RootNamespace>
    <AssemblyName>Dornbracht.Api.Crm</AssemblyName>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>1591</NoWarn>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Azure.Search.Documents" Version="11.4.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.20.0" />
    <PackageReference Include="AzureExtensions.Swashbuckle" Version="3.3.2" />
    <PackageReference Include="Microsoft.Azure.Core.Spatial" Version="1.1.0" />
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.ServiceBus" Version="5.16.4" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="6.0.0" />
    <PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.4.1" />
    <PackageReference Include="Microsoft.PowerPlatform.Dataverse.Client" Version="1.0.4" />
    <PackageReference Include="Microsoft.Spatial" Version="7.12.2" />
  </ItemGroup>
  <ItemGroup>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\function-extensions\function-extensions.csproj" />
  </ItemGroup>
</Project>
