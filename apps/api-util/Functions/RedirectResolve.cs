using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Functions.Extensions.Attributes;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Microsoft.Azure.Cosmos;
using Dornbracht.Functions.Extensions.Cosmos;
using Dornbracht.Api.Util.Models.Redirects;
using System.Net;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Abstraction;

namespace Dornbracht.Api.Util.Functions
{
  public class RedirectResolve
  {
    public RedirectResolve()
    {
    }

    /// <summary>
    /// Resolves a redirect for a given URL path.
    /// </summary>
    /// <remarks>
    /// Returns an empty object if no redirect is found in the database. Responses are cached by Front Door for 1 day (no redirect found) or 30 days (redirect found). This function requires a function key for authentication.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="client"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Resolved redirect target.</response>
    /// <response code="404">No redirect found.</response>
    [FunctionName(nameof(RedirectResolve))]
    [Produces(typeof(RedirectResponse))]
    [ProducesResponseType(typeof(RedirectResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [SwaggerTag("Redirects")]
    [CustomQueryParameter("path", typeof(string), "The path must contain a leading / (slash). Use url encoding to include it in the request.", true, "/de/old/path")]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Function, "get", Route = "util/redirect")] HttpRequest req,
                [CosmosDB(
                databaseName: "%COSMOS_REDIRECTS_DATABASE_NAME%",
                containerName: "%COSMOS_REDIRECTS_CONTAINER_NAME%",
                Connection = "cosmos"
                )][SwaggerIgnore] CosmosClient  client, ILogger log)
    {
      if (!req.Query.TryGetValue("path", out var pathParam))
      {
        return new BadRequestResult();
      }

      var path = pathParam.ToString();

      if (!path.StartsWith('/'))
      {
        return new BadRequestResult();
      }

      var id = path.ConvertToSaveId();
      var key = new PartitionKey(path);
      var container = client.GetDatabase(Environment.GetEnvironmentVariable("COSMOS_REDIRECTS_DATABASE_NAME", EnvironmentVariableTarget.Process))
                            .GetContainer(Environment.GetEnvironmentVariable("COSMOS_REDIRECTS_CONTAINER_NAME", EnvironmentVariableTarget.Process));

      try
      {
        // read redirect from database with eventual consistency
        var item = await container.ReadItemAsync<RedirectItem>(path.ConvertToSaveId(), new PartitionKey(path), new ItemRequestOptions { ConsistencyLevel = ConsistencyLevel.Eventual });

        // cache API response for a month
        req.AddMaxAgeHttpCacheHeader(TimeSpan.FromDays(30), HttpCacheType.Public);
        return new OkObjectResult(new RedirectResponse.Redirect(item.Resource));
      }
      catch (CosmosException e)
      {
        if (e.StatusCode == HttpStatusCode.NotFound)
        {
          // cache "not found" to reduce API and database strain
          req.AddMaxAgeHttpCacheHeader(TimeSpan.FromDays(1), HttpCacheType.Public);
          return new OkObjectResult(new RedirectResponse.NoRedirect());
        }

        log.LogError("Error while fetching redirect item!", e);
        return new StatusCodeResult(StatusCodes.Status500InternalServerError);
      }
    }
  }
}
