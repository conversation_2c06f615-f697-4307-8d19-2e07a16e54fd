using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Functions.Extensions.Attributes;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Microsoft.Azure.Cosmos;
using Dornbracht.Functions.Extensions.Cosmos;

namespace Dornbracht.Api.Util.Functions
{
  public class RedirectRemove
  {
    public RedirectRemove()
    {
    }

    /// <summary>
    /// Removes a redirect.
    /// </summary>
    /// <remarks>
    /// Path must start with a / (slash) otherwise it is invalid.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="client"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Success</response>
    /// <response code="400">Request is missing data or path is invalid. This function requires a function key for authentication.</response>
    /// <response code="500">Error on deletion.</response>
    [FunctionName(nameof(RedirectRemove))]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [CustomQueryParameter("path", typeof(string), "The path must contain a leading / (slash). Use url encoding to include it in the request.", true, "/de/old/path")]
    [SwaggerTag("Redirects")]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Function, "delete", Route = "util/redirect")] HttpRequest req, [CosmosDB(
                databaseName: "%COSMOS_REDIRECTS_DATABASE_NAME%",
                containerName: "%COSMOS_REDIRECTS_CONTAINER_NAME%",
                Connection = "cosmos"
                )][SwaggerIgnore] CosmosClient  client, ILogger log)
    {
      if (!req.Query.TryGetValue("path", out var pathParam))
      {
        return new BadRequestResult();
      }

      var path = pathParam.ToString();
      if (!path.StartsWith('/'))
      {
        return new BadRequestResult();
      }

      var id = path.ConvertToSaveId();
      var key = new PartitionKey(path);
      var container = client.GetDatabase(Environment.GetEnvironmentVariable("COSMOS_REDIRECTS_DATABASE_NAME", EnvironmentVariableTarget.Process))
                            .GetContainer(Environment.GetEnvironmentVariable("COSMOS_REDIRECTS_CONTAINER_NAME", EnvironmentVariableTarget.Process));

      try
      {
        await container.DeleteItemStreamAsync(id, key);

        return new OkResult();
      }
      catch (CosmosException e)
      {
        log.LogError(e, "Failed to replace redirect!");
        return new StatusCodeResult(StatusCodes.Status500InternalServerError);
      }
    }
  }
}
