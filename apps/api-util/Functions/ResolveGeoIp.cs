using System;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Util.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Attributes;

namespace Dornbracht.Api.Util.Functions
{
  public class ResolveGeoIp
  {
    public ResolveGeoIp(GeoIpService service)
    {
      Service = service;
    }

    private GeoIpService Service { get; }

    /// <summary>
    /// Resolves the country for a given IP address. (for SSR requests)
    /// </summary>
    /// <remarks>Responses contain a Cache-Control header for browser cache utilization to cache the request for 24 hours. This function also requires a function key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="ip"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Resolved country for given IP address.</response>
    /// <response code="400">IP address can not be parsed or is not supported.</response>
    [FunctionName(nameof(ResolveGeoIp))]
    [ProducesResponseType(typeof(GeoInformation), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [SwaggerTag("Geo IP")]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Function, "get", Route = "util/geoip/{ip}")] HttpRequest req, string ip, ILogger log)
    {
      if (!IPAddress.TryParse(ip, out var iPAddress) || IPAddress.IsLoopback(iPAddress) || iPAddress.IsIPv6Multicast)
      {
        return new BadRequestResult();
      }

      var data = await Service.GetGeoInformationFromIp(iPAddress);

      req.AddMaxAgeHttpCacheHeader(TimeSpan.FromDays(1));

      return new OkObjectResult(new GeoInformation(data.Location, data.Postal, data.City, data.MostSpecificSubdivision, data.Country));
    }
  }
}
