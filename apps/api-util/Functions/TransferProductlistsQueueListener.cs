using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using Dornbracht.Api.Util.Models.UserMigration;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.ServiceBus;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Util.Functions
{
  public class TransferProductlistsQueueListener
  {
    private HttpClient Client { get; init; }

    public TransferProductlistsQueueListener(IHttpClientFactory httpClientFactory)
    {
      Client = httpClientFactory.CreateClient("PDH");
    }

    [FunctionName(nameof(TransferProductlistsQueueListener))]
    public async Task Run([ServiceBusTrigger("%ASB_LIST_MIGRATION_QUEUE_NAME%", Connection = "ASB_LIST_MIGRATION_QUEUE_CONNECTION_STRING", AutoCompleteMessages = false)] ServiceBusReceivedMessage[] messages, ServiceBusMessageActions messageActions, ILogger log)
    {
      foreach (var message in messages)
      {
        try
        {
          TransferProductlistsPayload transferObject = await ExtractTransferObject(message, messageActions);
          if (transferObject != null)
          {
            var request = new HttpRequestMessage(HttpMethod.Post, $"{Client.BaseAddress}v1/productlists/transfer?code={Environment.GetEnvironmentVariable("TRANSFER_PRODUCTLISTS_FUNCTION_KEY", EnvironmentVariableTarget.Process)}");
            request.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            request.Content = JsonContent.Create(transferObject);
            var httpResponse = await Client.SendAsync(request);

            if (httpResponse.IsSuccessStatusCode)
            {
              await messageActions.CompleteMessageAsync(message);
            }
            else
            {
              log.LogError($"Could not transfer productlist ({transferObject.from} > {transferObject.to}) ({httpResponse.StatusCode})");
              await messageActions.DeadLetterMessageAsync(message, "Transfer failed", $"Could not transfer productlist ({httpResponse.StatusCode})");
            }
          }
        }
        catch (Exception e)
        {
          log.LogError(e, $"Exception during productlist transfer: {e.Message}");
          await messageActions.DeadLetterMessageAsync(message, "Exception", $"Exception during productlist transfer: {e.Message}");
        }
      }
    }

    private async Task<TransferProductlistsPayload> ExtractTransferObject(ServiceBusReceivedMessage message, ServiceBusMessageActions messageActions)
    {
      try
      {
        return message.Body.ToObjectFromJson<TransferProductlistsPayload>();
      }
      catch (Exception e)
      {
        await messageActions.DeadLetterMessageAsync(message, "Transformation failed", $"Transformation to transfer object failed with exception: {e.Message}");
      }
      return null;
    }
  }
}
