using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Util.Services;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.Models.Storyblok;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Dornbracht.Functions.Extensions;

namespace Dornbracht.Api.Util.Functions
{
  public class BuildImageSitemapManual
  {
    public BuildImageSitemapManual(ImageSitemapService service, IStoryblokRepository storyblokRepository)
    {
      Service = service;
      StoryblokRepository = storyblokRepository;
    }

    private ImageSitemapService Service { get; }
    private IStoryblokRepository StoryblokRepository { get; init; }

    /// <summary>
    /// Builds an image XML Sitemap in all defined languages. Usage only for testing purposes.
    /// </summary>
    /// <remarks>This function requires a function host (admin) key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    [SwaggerTag("Administration")]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [FunctionName(nameof(BuildImageSitemapManual))]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Admin, "get", Route = "util/buildimagesitemap")] HttpRequest req, ILogger log)
    {
      var space = await StoryblokRepository.GetSpace();
      if (space is SpaceResult.Success spaceResult)
      {
        await Service.BuildImageSitemaps(spaceResult.Space, log);
      }
      else
      {
        log.LogWarning("IMAGE SITEMAP BUILDER: Cannot access Storyblok space (HTTP 401 Unauthorized). Unable to build image sitemaps.");
        return new UnauthorizedObjectResult("Cannot access Storyblok space");
      }

      req.AddNoCacheHeader();

      return new OkObjectResult("") { StatusCode = 202 };
    }
  }
}
