using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Util.Services;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Models.Storyblok;

namespace Dornbracht.Api.Util.Functions
{
  public class BuildImageSitemapTask
  {
    public BuildImageSitemapTask(ImageSitemapService service, IStoryblokRepository storyblokRepository)
    {
      Service = service;
      StoryblokRepository = storyblokRepository;
    }

    private ImageSitemapService Service { get; }
    private IStoryblokRepository StoryblokRepository { get; init; }

    /// <summary>
    /// Builds a XML Sitemap in all defined languages
    /// </summary>
    /// <param name="timer"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    [FunctionName(nameof(BuildImageSitemapTask))]
    public async Task Run([TimerTrigger("0 0 1 * * *")] TimerInfo timer, ILogger log)
    {
      var space = await StoryblokRepository.GetSpace();
      if (space is SpaceResult.Success spaceResult)
      {
        await Service.BuildImageSitemaps(spaceResult.Space, log);
      }
      else
      {
        log.LogWarning("IMAGE SITEMAP BUILDER: Cannot access Storyblok space (HTTP 401 Unauthorized). Unable to build image sitemaps.");
      }
    }
  }
}
