using System.Linq;
using System;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Util.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.Abstraction;

namespace Dornbracht.Api.Util.Functions
{
  public class ResolveClientGeoIp
  {
    public ResolveClientGeoIp(GeoIpService service)
    {
      Service = service;
    }

    private GeoIpService Service { get; }

    /// <summary>
    /// Resolves the country for the client IP address. (for CSR requests)
    /// </summary>
    /// <remarks>Responses contain a Cache-Control header for browser cache utilization to cache the request for 24 hours.</remarks>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Resolved country for given IP address.</response>
    /// <response code="400">IP address can not be parsed or is not supported.</response>
    [FunctionName(nameof(ResolveClientGeoIp))]
    [ProducesResponseType(typeof(GeoInformation), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [SwaggerTag("Geo IP")]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "util/geoip")] HttpRequest req, ILogger log)
    {
      if (!req.Headers.ContainsKey("x-azure-clientip") && !req.Headers.ContainsKey("x-forwarded-for"))
      {
        return new BadRequestResult();
      }
      string ip;

      if (req.Headers.ContainsKey("x-azure-clientip"))
      {
        ip = req.Headers["x-azure-clientip"][0];
      }
      else
      {
        var headers = string.Join(',', req.Headers["x-forwarded-for"]);
        ip = headers.Split(',').FirstOrDefault()?.Trim();
      }

      if (string.IsNullOrEmpty(ip) || !IPAddress.TryParse(ip, out var iPAddress) || IPAddress.IsLoopback(iPAddress) || iPAddress.IsIPv6Multicast)
      {
        return new BadRequestResult();
      }

      var data = await Service.GetGeoInformationFromIp(iPAddress);

      req.AddMaxAgeHttpCacheHeader(TimeSpan.FromDays(1), HttpCacheType.Private);

      return new OkObjectResult(new GeoInformation(data.Location, data.Postal, data.City, data.MostSpecificSubdivision, data.Country));
    }
  }
}
