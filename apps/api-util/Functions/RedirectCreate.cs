using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Functions.Extensions.Attributes;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Microsoft.Azure.Cosmos;
using Dornbracht.Api.Util.Models.Redirects;
using System.Net;
using Dornbracht.Functions.Extensions.Cosmos;

namespace Dornbracht.Api.Util.Functions
{
  public class RedirectCreate
  {
    public RedirectCreate()
    {
    }

    /// <summary>
    /// Creates a new redirect or replaces an existing one.
    /// </summary>
    /// <remarks>
    /// Both paths must start with a / (slash) otherwise they are invalid. This function requires a function key for authentication.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="client"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Success</response>
    /// <response code="400">Request is missing data or paths are invalid.</response>
    /// <response code="500">Error on creation or replace.</response>
    [FunctionName(nameof(RedirectCreate))]
    [Consumes(typeof(CreateRedirectModel), "application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [SwaggerTag("Redirects")]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Function, "post", Route = "util/redirect")][RequestBodyType(typeof(CreateRedirectModel), "Redirect data")] HttpRequest req, [CosmosDB(
                databaseName: "%COSMOS_REDIRECTS_DATABASE_NAME%",
                containerName: "%COSMOS_REDIRECTS_CONTAINER_NAME%",
                Connection = "cosmos"
                )][SwaggerIgnore] CosmosClient  client, ILogger log)
    {
      var model = await req.ReadFromJsonAsync<CreateRedirectModel>();

      if (string.IsNullOrEmpty(model.Path) || string.IsNullOrEmpty(model.Target))
      {
        return new BadRequestResult();
      }

      if (!model.Path.StartsWith('/') || !model.Target.StartsWith('/'))
      {
        return new BadRequestResult();
      }

      var id = model.Path.ConvertToSaveId();
      var key = new PartitionKey(model.Path);
      var container = client.GetDatabase(Environment.GetEnvironmentVariable("COSMOS_REDIRECTS_DATABASE_NAME", EnvironmentVariableTarget.Process))
                            .GetContainer(Environment.GetEnvironmentVariable("COSMOS_REDIRECTS_CONTAINER_NAME", EnvironmentVariableTarget.Process));

      using (var response = await container.ReadItemStreamAsync(id, key))
      {
        if (response.IsSuccessStatusCode)
        {
          try
          {
            await container.ReplaceItemAsync<RedirectItem>(new RedirectItem { Path = model.Path, Id = id, Target = model.Target, Temporary = model.Temporary }, id, key);

            return new OkResult();
          }
          catch (CosmosException e)
          {
            log.LogError(e, "Failed to replace redirect!");
            return new StatusCodeResult(StatusCodes.Status500InternalServerError);
          }
        }

        if (response.StatusCode == HttpStatusCode.NotFound)
        {
          try
          {
            await container.CreateItemAsync<RedirectItem>(new RedirectItem { Path = model.Path, Id = id, Target = model.Target, Temporary = model.Temporary }, key);
            return new OkResult();
          }
          catch (CosmosException e)
          {
            log.LogError(e, "Failed to create redirect!");
            return new StatusCodeResult(StatusCodes.Status500InternalServerError);
          }
        }

        log.LogError($"Failed to fetch redirect from cosmos db: {response.ErrorMessage}");
        return new StatusCodeResult(StatusCodes.Status500InternalServerError);
      }
    }
  }
}
