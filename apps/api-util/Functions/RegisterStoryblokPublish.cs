using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Mvc;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.Azure.WebJobs.ServiceBus;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Messaging;
using Dornbracht.Functions.Extensions.Storyblok;

namespace Dornbracht.Api.Util.Functions
{
  public class RegisterStoryblokPublish
  {
    public RegisterStoryblokPublish(IQueueSerializer queueSerializer)
    {
      QueueSerializer = queueSerializer;
    }

    private IQueueSerializer QueueSerializer { get; }

    /// <summary>
    /// Storyblock webhook endpoint for publish events.
    /// </summary>
    /// <remarks>This function requires a function key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <response code="200">Form submission successfully added to queue.</response>
    /// <returns></returns>
    [FunctionName(nameof(RegisterStoryblokPublish))]
    [SwaggerTag("Publish Endpoints")]
    [Consumes(typeof(StoryblokWebhookRequest), "application/json")]
    [return: ServiceBus("%ASB_PUBLISH_TOPIC_NAME%", ServiceBusEntityType.Topic, Connection = "ASB_PUBLISH_TOPIC_CONNECTION_STRING")]
    public async Task<byte[]> Run([HttpTrigger(AuthorizationLevel.Function, "post", Route = "util/storyblok/publish")][RequestBodyType(typeof(StoryblokWebhookRequest), "Storyblok webhook form data")] HttpRequest req, ILogger log)
    {
      var data = await req.ReadFromJsonAsync<StoryblokWebhookRequest>();

      if (data != null)
      {
        return await QueueSerializer.SerializeQueueMessage<PublishMessage>(new PublishMessage { Action = data.Action, SpaceId = data.SpaceId, StoryId = data.StoryId });
      }

      // Azure Service Bus output binding sends no message to the topic if null is returned, but only if the return type of the function is string or byte[]!
      return null;
    }
  }
}
