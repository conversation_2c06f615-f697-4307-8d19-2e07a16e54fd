using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Util.Services;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.Models.Storyblok;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Dornbracht.Functions.Extensions;

namespace Dornbracht.Api.Util.Functions
{
  public class BuildSitemapManual
  {
    public BuildSitemapManual(SitemapService service, IStoryblokRepository storyblokRepository)
    {
      Service = service;
      StoryblokRepository = storyblokRepository;
    }

    private SitemapService Service { get; }
    private IStoryblokRepository StoryblokRepository { get; init; }

    /// <summary>
    /// Builds a XML Sitemap in all defined languages. Usage only for testing purposes.
    /// </summary>
    /// <remarks>This function requires a function host (admin) key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    [SwaggerTag("Administration")]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [FunctionName(nameof(BuildSitemapManual))]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Admin, "get", Route = "util/buildsitemap")] HttpRequest req, ILogger log)
    {
      var space = await StoryblokRepository.GetSpace();
      if (space is SpaceResult.Success spaceResult)
      {
        var sitemaps = await Service.BuildSitemaps(spaceResult.Space, log);
        var sitemapIndex = SitemapService.GetSitemapIndex(sitemaps, spaceResult.Space);

        Service.UploadSitemap(sitemapIndex, log);
      }
      else
      {
        log.LogWarning("SITEMAP BUILDER: Cannot access Storyblok space (HTTP 401 Unauthorized). Unable to build sitemaps.");
      }

      req.AddNoCacheHeader();
      return new OkObjectResult("") { StatusCode = 202 };
    }
  }
}
