using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Util.Services;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Models.Storyblok;

namespace Dornbracht.Api.Util.Functions
{
  public class BuildSitemapTask
  {
    public BuildSitemapTask(SitemapService service, IStoryblokRepository storyblokRepository)
    {
      Service = service;
      StoryblokRepository = storyblokRepository;
    }

    private SitemapService Service { get; }
    private IStoryblokRepository StoryblokRepository { get; init; }

    /// <summary>
    /// Builds a XML Sitemap in all defined languages
    /// </summary>
    /// <param name="timer"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    [FunctionName(nameof(BuildSitemapTask))]
    public async Task Run([TimerTrigger("0 0 1 * * *")] TimerInfo timer, ILogger log)
    {
      var space = await StoryblokRepository.GetSpace();
      if (space is SpaceResult.Success spaceResult)
      {
        var sitemaps = await Service.BuildSitemaps(spaceResult.Space, log);
        var sitemapIndex = SitemapService.GetSitemapIndex(sitemaps, spaceResult.Space);

        Service.UploadSitemap(sitemapIndex, log);
      }
      else
      {
        log.LogWarning("SITEMAP BUILDER: Cannot access Storyblok space (HTTP 401 Unauthorized). Unable to build sitemaps.");
      }
    }
  }
}
