using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Dornbracht.Functions.Extensions.Messaging;
using Azure.Identity;
using Azure.ResourceManager;
using Azure.Core;
using Azure.ResourceManager.Cdn;
using Azure.ResourceManager.Cdn.Models;
using Azure;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Models.Storyblok;
using Dornbracht.Functions.Extensions.Languages;
using System.Collections.Generic;
using Azure.Messaging.ServiceBus;

namespace Dornbracht.Api.Util.Functions
{
  public class PurgeEdgeCacheForPublishedPages
  {
    const string WebhookActionDeleted = "deleted";

    public PurgeEdgeCacheForPublishedPages(IStoryblokRepository storyblokRepository, IStoryblokManagementRepository managementRepository)
    {
      StoryblokRepository = storyblokRepository;
      ManagementRepository = managementRepository;
      Client = new ServiceBusClient(Environment.GetEnvironmentVariable("ASB_PUBLISH_TOPIC_CONNECTION_STRING", EnvironmentVariableTarget.Process));
    }

    private IStoryblokRepository StoryblokRepository { get; }

    private IStoryblokManagementRepository ManagementRepository { get; }

    private ServiceBusClient Client { get; }

    [FunctionName(nameof(PurgeEdgeCacheForPublishedPages))]
    public async Task Run([TimerTrigger("0 */15 * * * *")] TimerInfo timer, ILogger log)
    {
      var topic = Environment.GetEnvironmentVariable("ASB_PUBLISH_TOPIC_NAME", EnvironmentVariableTarget.Process);
      var subscription = Environment.GetEnvironmentVariable("ASB_PUBLISH_TOPIC_PURGE_SUBSCRIPTION_NAME", EnvironmentVariableTarget.Process);
      var receiver = Client.CreateReceiver(topic, subscription);
      var messages = await receiver.ReceiveMessagesAsync(4, TimeSpan.FromSeconds(3));

      if(!messages.Any())  {
        log.LogInformation($"Cache purge skipped. No stories published.");
        return;
      }

      var storyPaths = new Dictionary<long, string[]>();
      foreach(var busMessage in messages) {
        var message = busMessage.Body.ToObjectFromJson<PublishMessage>();
        var data = await GetStoryData( message, log);

        if (data.HasValue)
        {
          var (defaultSlug, slugs) = data.Value;
          var paths = BuildUrlsFromSlugs(slugs, defaultSlug).ToArray();

          if (paths.Length > 0)
          {
            storyPaths[message.StoryId] = paths;
          }
        }
        else
        {
          await receiver.DeadLetterMessageAsync(busMessage, "story not found");
          log.LogWarning($"Failed to get Story {message.StoryId} from Storyblok! No cache purge will be executed for this story.");
        }

        await receiver.CompleteMessageAsync(busMessage);
      }

      if(storyPaths.Any()) {
        log.LogInformation($"Cache purge started for all paths of the following published stories {string.Join(", ", storyPaths.Select((kv) => kv.Key))}.");

        // beware: the azure API can only process 100 paths concurrently. Processing takes up to 10 min.
        await StartCachePurge(storyPaths.SelectMany((kv) => kv.Value).ToArray(), log);
      } else {
        log.LogInformation($"Cache purge skipped. No content paths to purge.");
      }
    }

    private async Task<(string defaultSlug, TranslatedSlug[] slugs)?> GetStoryData(PublishMessage message, ILogger log)
    {
      if (message.Action == WebhookActionDeleted)
      {
        // get story from management api on delete
        var storyResult = await ManagementRepository.GetStory(message.SpaceId, message.StoryId);

        if (storyResult is ManagementStoryResult.Success story)
        {
          return (story.Story.FullSlug, story.Story.LocalizedPaths.ToArray());
        }
      }
      else
      {
        var storyResult = await StoryblokRepository.GetStory(message.StoryId, "default");

        if (storyResult is StoryResult.Success story)
        {
          return (story.Story.FullSlug, story.Story.TranslatedSlugs.ToArray());
        }
      }

      return null;
    }

    private IEnumerable<string> BuildUrlsFromSlugs(TranslatedSlug[] slugs, string defaultSlug)
    {
      foreach (var language in LanguageRepository.LanguageMappingDictionary)
      {
        var slug = slugs.FirstOrDefault(s => s.Language == language.Value);

        if (slug != null)
        {
          yield return $"/{language.Key}/{slug.Path.Trim('/')}";
          // yield return $"/{language.Key}/{slug.Path.Trim('/')}/*";
        }
        else
        {
          yield return $"/{language.Key}/{defaultSlug.Trim('/')}";
          // yield return $"/{language.Key}/{defaultSlug.Trim('/')}/*";
        }
      }
    }

    private async Task StartCachePurge(string[] paths, ILogger log)
    {
      var endpointId = Environment.GetEnvironmentVariable("WEBSITE_FRONTDOOR_ENDPOINT_ID", EnvironmentVariableTarget.Process);
      var domain = Environment.GetEnvironmentVariable("WEBSITE_DOMAIN", EnvironmentVariableTarget.Process);

      ArmClient client = new ArmClient(new ChainedTokenCredential(new AzureCliCredential(), new DefaultAzureCredential()));
      var frontDoor = client.GetFrontDoorEndpointResource(new ResourceIdentifier(endpointId));

      var options = new FrontDoorPurgeContent(paths);
      options.Domains.Add(domain);

      await frontDoor.PurgeContentAsync(WaitUntil.Started, options);
    }
  }
}
