using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.AspNetCore.Http;
using Dornbracht.Api.Util.Services;
using Dornbracht.Api.Util.Models;
using Dornbracht.Functions.Extensions.Attributes;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using AzureFunctions.Extensions.Swashbuckle.Attribute;

namespace Dornbracht.Api.Util.Functions
{
  public class CheckPwdHash
  {
    public CheckPwdHash(PwdHashService service, TransferProductlistsQueueService transferMessageQueueService)
    {
      PwdHashService = service;
      TransferMessageQueueClient = transferMessageQueueService;
    }

    private PwdHashService PwdHashService { get; }

    private TransferProductlistsQueueService TransferMessageQueueClient { get; }

    /// <summary>
    /// REST API to validate the password against the legacy Identity Provider.
    /// Implemented according to Microsoft documentation for use in the AD B2 login process:
    /// https://github.com/azure-ad-b2c/user-migration/tree/master/seamless-account-migration#configuring-the-rest-api-response-to-azure-ad-b2c
    /// </summary>
    /// <remarks>This function requires a function key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200"></response>
    /// <response code="400">Invalid request data.</response>
    /// <response code="404">User not found.</response>
    [FunctionName(nameof(CheckPwdHash))]
    [ProducesResponseType(typeof(PwdHashCheckResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Consumes(typeof(PwdHashCheckRequest), "application/json")]
    [SwaggerTag("Administration")]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Function, "post", Route = "util/checkpwdhash")][RequestBodyType(typeof(PwdHashCheckRequest), "User data")] HttpRequest req, ILogger log)
    {
      PwdHashCheckRequest pwdHashCheckRequest = await req.ReadFromJsonAsync<PwdHashCheckRequest>();
      if (pwdHashCheckRequest == null)
      {
        return new BadRequestObjectResult("Request body not valid, signInName and password expected");
      }

      MigratedPwdHashEntity pwdHashCheckEntity = await PwdHashService.LoadMigratedPwdHashOfUser(pwdHashCheckRequest.SignInName, log);
      if (pwdHashCheckEntity == null)
      {
        return new NotFoundObjectResult($"Migrated user '{pwdHashCheckRequest.SignInName}' not found");
      }

      var data = PwdHashService.CheckPwdHash(pwdHashCheckRequest, pwdHashCheckEntity, log);

      if (data.tokenSuccess)
      {
        await TransferMessageQueueClient.SendMessage(pwdHashCheckEntity.UserId, pwdHashCheckEntity.ObjectIdADB2C);
      }
      return new OkObjectResult(data);
    }
  }
}
