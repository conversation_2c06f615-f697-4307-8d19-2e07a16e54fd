using System.Threading;
using System;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Functions.Extensions.Attributes;
using Azure.Messaging.ServiceBus;
using Dornbracht.Functions.Extensions;

namespace Dornbracht.Api.Util.Functions
{
  public class ClearPublishDeadLetters
  {
    public ClearPublishDeadLetters()
    {
      Client = new ServiceBusClient(Environment.GetEnvironmentVariable("ASB_PUBLISH_TOPIC_CONNECTION_STRING", EnvironmentVariableTarget.Process));
    }

    private ServiceBusClient Client { get; }

    /// <summary>
    /// Clears the dead-letter queues of the publish topic. Before executing: Always check the dead-letter reasons in Azure if there is a general error present.
    /// </summary>
    /// <remarks>This function requires a admin host key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <response code="200">Dead-letters successfully deleted.</response>
    /// <returns></returns>
    [FunctionName(nameof(ClearPublishDeadLetters))]
    [SwaggerTag("Publish Endpoints")]
    public async Task Run([HttpTrigger(AuthorizationLevel.Admin, "get", Route = "util/storyblok/publish/clear")] HttpRequest req, ILogger log)
    {
      await ClearDeadLettersForSubscription("purge_edge_cache", log);
      await ClearDeadLettersForSubscription("search_index_update", log);

      req.AddNoCacheHeader();
    }

    private async Task ClearDeadLettersForSubscription(string subscription, ILogger log)
    {
      var deadLetterReceiver = Client.CreateReceiver(Environment.GetEnvironmentVariable("ASB_PUBLISH_TOPIC_NAME", EnvironmentVariableTarget.Process), subscription, new ServiceBusReceiverOptions() { SubQueue = SubQueue.DeadLetter });
      var messages = await deadLetterReceiver.ReceiveMessagesAsync(100, TimeSpan.FromSeconds(5));

      foreach (var message in messages)
      {
        log.LogInformation($"Removing dead-letter message {message.MessageId} (dead-letter reason: {message.DeadLetterReason}) from subscription '{subscription}'.");
        await deadLetterReceiver.CompleteMessageAsync(message);
      }
    }
  }
}
