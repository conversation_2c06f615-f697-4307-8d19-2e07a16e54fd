using System;
using System.Reflection;
using AzureFunctions.Extensions.Swashbuckle.Settings;
using Dornbracht.Api.Util.Services;
using Dornbracht.Functions.Extensions.Messaging;
using Dornbracht.Functions.Extensions.Storyblok;
using Dornbracht.Functions.Extensions.Swagger;
using MaxMind.GeoIP2;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Net.Http.Headers;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Auth;

[assembly: FunctionsStartup(typeof(Dornbracht.Api.Util.Startup))]
namespace Dornbracht.Api.Util
{
  public class Startup : FunctionsStartup
  {
    public override void Configure(IFunctionsHostBuilder builder)
    {
      var baseUrl = Environment.GetEnvironmentVariable("API_BASE_URL", EnvironmentVariableTarget.Process);
      var hostKey = Environment.GetEnvironmentVariable("SWAGGER_JSON_FUNCTION_KEY", EnvironmentVariableTarget.Process);

      builder.AddFunctionsSwagger(Assembly.GetExecutingAssembly(), baseUrl, "v1/util/swagger/json", hostKey, false, (options) =>
      {
        options.Title = "Dornbracht Util API";
        options.XmlPath = "Dornbracht.Api.Util.xml";
        options.Documents = new[]
                      {
                    new SwaggerDocument
                    {
                        Name = "v1",
                        Title = "Dornbracht Util API",
                        Description = "Swagger documentation for the Dornbracht Util API function layer.",
                        Version = "v1"
                    }
          };
      });

      builder.Services.AddOptions<WebServiceClientOptions>()
        .Configure<IConfiguration>((settings, configuration) =>
        {
          configuration.GetSection("Maxmind").Bind(settings);
        });
      builder.Services.AddHttpClient<WebServiceClient>();

      builder.Services.AddSingleton<GeoIpService>();
      builder.AddStoryblokRepository();
      builder.AddStoryblokManagementRepository();
      builder.AddMessagingServices();

      builder.Services.AddSingleton<SitemapService>();
      builder.Services.AddSingleton<ImageSitemapService>();

      builder.Services.AddSingleton<PwdHashService>();
      builder.Services.AddSingleton<TransferProductlistsQueueService>();
      builder.Services.AddTransient(_ =>
      {
        var accountName = Environment.GetEnvironmentVariable("AzureStorageTable:AccountName", EnvironmentVariableTarget.Process);
        var accountKey = Environment.GetEnvironmentVariable("AzureStorageTable:AccountKey", EnvironmentVariableTarget.Process);

        StorageCredentials creds = new StorageCredentials(accountName, accountKey);
        CloudStorageAccount account = new CloudStorageAccount(creds, useHttps: true);
        return account.CreateCloudTableClient();
      });

      builder.Services.AddHttpClient("PDH", httpClient =>
      {
        httpClient.BaseAddress = new Uri(Environment.GetEnvironmentVariable("API_BASE_URL", EnvironmentVariableTarget.Process));
        httpClient.DefaultRequestHeaders.Add(HeaderNames.Accept, "application/json");
      });
    }
  }
}
