# Util Azure Functions

## Prerequisites

- Azure Functions VS Code Extensions
- [Azurite](https://marketplace.visualstudio.com/items?itemName=Azurite.azurite). This is required for running a non-HTTP-Trigger Function and [Azure Blob Storage](https://docs.microsoft.com/en-us/dotnet/api/overview/azure/Storage.Blobs-readme) locally!
- Add a firewall rule to allow communication over AMQP port (5671) with the Azure Service Bus.
- Start Azurite Blob Service.

## Configuration

- Create `local.settings.json` file with the following properties:

  ```json
  {
    "IsEncrypted": false,
    "Values": {
      "AzureWebJobsStorage": "UseDevelopmentStorage=true",
      "API_BASE_URL": "http://localhost:7073",
      "SWAGGER_JSON_FUNCTION_KEY": "",
      "TRANSFER_PRODUCTLISTS_FUNCTION_KEY": "",
      "FUNCTIONS_WORKER_RUNTIME": "dotnet",
      "FUNCTIONS_EXTENSION_VERSION": "~4",
      "Maxmind:LicenseKey": "",
      "Maxmind:AccountId": "",
      "STORYBLOK_PREVIEW_API_TOKEN": "",
      "WEBSITE_BASE_URL": "https://website-dev.dornbracht.com/",
      "WEBSITE_DOMAIN": "website-dev.dornbracht.com",
      "SITEMAP_STORAGE_CONNECTION_STRING": "UseDevelopmentStorage=true",
      "SITEMAP_STORAGE_CONTAINER_NAME": "$web",
      "AzureStorageTable:AccountName": "",
      "AzureStorageTable:AccountKey": "",
      "ASB_LIST_MIGRATION_QUEUE_CONNECTION_STRING": "Endpoint=sb://{hostname}/;SharedAccessKeyName=ListMigrationQueueAccessKey;SharedAccessKey={accessKey};EntityPath=web-listmigration-asbq-0-d-d50",
      "ASB_LIST_MIGRATION_QUEUE_NAME": "web-listmigration-asbq-0-d-d50",
      "cosmos:accountEndpoint": "https://web-docdb-0-d-d50.documents.azure.com:443/",
      "cosmos:tenantId": "********-77ab-4ca3-9120-d94085b01b80",
      "COSMOS_REDIRECTS_CONTAINER_NAME": "redirects",
      "COSMOS_REDIRECTS_DATABASE_NAME": "redirects",
      "ASB_PUBLISH_TOPIC_NAME": "web-publish-asbt-0-d-d50",
      "ASB_PUBLISH_TOPIC_PURGE_SUBSCRIPTION_NAME": "purge_edge_cache",
      "ASB_PUBLISH_TOPIC_CONNECTION_STRING": "Endpoint=sb://{hostname}/;SharedAccessKeyName=PublishTopicAccessKey;SharedAccessKey={accessKey};EntityPath=web-publish-asbt-0-d-d50",
      "WEBSITE_FRONTDOOR_ENDPOINT_ID": "/subscriptions/903bf823-4b87-4b5c-b854-4052e0a9d53f/resourceGroups/web-rg-dev/providers/Microsoft.Cdn/profiles/web-fd-0-d-d50/afdEndpoints/web-fde-website-0-d-d50",
      "STORYBLOK_MANAGEMENT_API_TOKEN":""
    },
    "Host": {
      "CORS": "*"
    }
  }
  ```

- Fill the environment variables with the appropriate values
  - `Maxmind:LicenseKey`, `Maxmind:AccountId`: https://orga15.bielefeld.comspace.de/pwtool/project/769
  - `AzureStorageTable:AccountName`, `AzureStorageTable:AccountKey`: https://orga15.bielefeld.comspace.de/pwtool/project/797#system10172
  - `TRANSFER_PRODUCTLISTS_FUNCTION_KEY`: https://orga15.bielefeld.comspace.de/pwtool/project/797#system10085

### CosmosDB Authenticaton

Functions using the Cosmos DB client are using RBAC token authentication. The local function host will use Azure CLI or any other Azure Login listed [here](https://learn.microsoft.com/en-us/azure/azure-functions/functions-reference?tabs=cosmos#local-development-with-identity-based-connections) to get the required token. Make sure to login properly (with an account that has permissions for the subscriptions) with one of the listed solutions.

### SDK Authentication for cache purge function

The azure function that clears front door caches on Storyblock publish uses the Azure SDK to execute the cache purge. The SDK needs authentication for this action. The local function host will use Azure CLI to get the required token for authentication. Make sure to login properly (with an account that has permissions for the subscriptions) and select the Development subscription (az account set).
