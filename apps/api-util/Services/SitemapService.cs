using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Languages;
using Dornbracht.Functions.Extensions.Models.Sitemap;
using Dornbracht.Functions.Extensions.Models.Storyblok;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Util.Services
{
  public class SitemapService : BaseSitemapService
  {
    public SitemapService(IStoryblokRepository storyblokRepository) : base(storyblokRepository)
    {
    }

    public static SitemapIndex GetSitemapIndex(IEnumerable<string> sitemaps, Space space)
    {
      return new SitemapIndex($"sitemap.index.xml", sitemaps, space.Domain);
    }

    public async Task<IEnumerable<string>> BuildSitemaps(Space space, ILogger log)
    {
      var sitemapFiles = new List<string>();
      foreach (var language in LanguageRepository.StoryblokLanguages)
      {
        var sitemaps = await BuildSitemapsByLanguage(language, space, log);
        sitemapFiles.AddRange(sitemaps);
      }

      return sitemapFiles;
    }

    public async Task<IEnumerable<string>> BuildSitemapsByLanguage(string storyblokLanguage, Space space, ILogger log)
    {
      var stories = await FetchAllStories(storyblokLanguage, space);
      var urlLanguages = LanguageRepository.GetUrlLanguages(storyblokLanguage);
      var sitemaps = new List<string>();

      foreach (var urlLanguage in urlLanguages)
      {
        var sitemapNodes = new List<SitemapNode>();

        foreach (var story in stories)
        {
          if (story.IsValidPageType)
          {
            string url = urlLanguage + "/";

            if (story.Content.TryGetValue("markets", out var marketsElement)
                && marketsElement.GetArrayLength() > 0)
            {
              var markets = marketsElement.EnumerateArray().Select((element) => element.GetString()).ToList();
              if (markets.Any(market =>
                    market == (urlLanguage == LanguageRepository.StoryblokDefaultUrlLanguage ? "xy" : urlLanguage.Split('-')[1])))
              {
                if (!story.Slug.ToLower().Equals(DefaultHomePage))
                {
                  if (storyblokLanguage == LanguageRepository.StoryblokDefaultUrlLanguage)
                  {
                    url += story.FullSlug;
                  }
                  else
                  {
                    url = string.Concat(url, story.FullSlug.AsSpan(story.FullSlug.IndexOf('/') + 1));
                  }
                }
                sitemapNodes.Add(new SitemapNode(url, story.PublishedAt, BuildHrefLangSet(story, markets)));
              }
            }
            else
            {
              if (!story.Slug.ToLower().Equals(DefaultHomePage))
              {
                if (storyblokLanguage == LanguageRepository.StoryblokDefaultUrlLanguage)
                {
                  url += story.FullSlug;
                }
                else
                {
                  url = string.Concat(url, story.FullSlug.AsSpan(story.FullSlug.IndexOf('/') + 1));
                }
              }
              sitemapNodes.Add(new SitemapNode(url, story.PublishedAt, BuildHrefLangSet(story)));
            }
          }
        }

        var sitemap = new Sitemap($"{urlLanguage}/sitemap.{urlLanguage}.xml", sitemapNodes, Domain);
        sitemaps.Add(sitemap.FileName);

        UploadSitemap(sitemap, log);
      }

      return sitemaps;
    }

    private static Dictionary<string, string> BuildHrefLangSet(Story story, IEnumerable<string> markets = null)
    {
      var urlsByLanguage = new Dictionary<string, string>();
      if (story.Content.TryGetValue("canonical_link_uid", out var canonicalLinkUid))
      {
        var canonicalLinkUidValue = canonicalLinkUid.GetString();
        if (string.IsNullOrEmpty(canonicalLinkUidValue) || canonicalLinkUidValue == story.Uuid)
        {
          foreach (var languageMappingKeyValuePair in markets == null
                     ? LanguageRepository.LanguageMappingDictionary
                     : LanguageRepository.LanguageMappingDictionary.Where((keyValuePair) =>
                       markets.Contains(keyValuePair.Key == "en" ? "xy" : keyValuePair.Key.Split('-')[1])))
          {
            string url = languageMappingKeyValuePair.Key + "/";
            if (languageMappingKeyValuePair.Value == LanguageRepository.StoryblokDefaultUrlLanguage)
            {
              if (!story.Slug.ToLower().Equals(DefaultHomePage))
              {
                url += story.DefaultFullSlug;
              }

              if (languageMappingKeyValuePair.Key == LanguageRepository.StoryblokDefaultUrlLanguage)
              {
                urlsByLanguage.Add("x-default", url);
              }
              else
              {
                urlsByLanguage.Add(languageMappingKeyValuePair.Key, url);
              }
            }
            else
            {
              if (!story.Slug.ToLower().Equals(DefaultHomePage))
              {
                url += story.TranslatedSlugs.FirstOrDefault(slug => slug.Language == languageMappingKeyValuePair.Value)?.Path;
              }

              urlsByLanguage.Add(languageMappingKeyValuePair.Key, url);
            }
          }
        }
      }
      return urlsByLanguage;
    }
  }
}
