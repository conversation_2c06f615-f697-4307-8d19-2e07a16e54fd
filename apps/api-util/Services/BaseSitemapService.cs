using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Dornbracht.Api.Util.Models;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Languages;
using Dornbracht.Functions.Extensions.Models.Sitemap;
using Dornbracht.Functions.Extensions.Models.Storyblok;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Util.Services
{
  public class BaseSitemapService
  {
    protected const string DefaultHomePage = "home";
    private const int StoriesPerPage = 100;
    private const int MaxPages = 5;

    public BaseSitemapService(IStoryblokRepository storyblokRepository)
    {
      StoryblokRepository = storyblokRepository;
      Domain = Environment.GetEnvironmentVariable("WEBSITE_BASE_URL", EnvironmentVariableTarget.Process);
    }

    protected string Domain { get; init; }
    private IStoryblokRepository StoryblokRepository { get; init; }

    public async Task<List<Story>> FetchAllStories(string storyblokLanguage, Space space)
    {
      if (!LanguageRepository.StoryblokLanguages.Contains(storyblokLanguage))
      {
        return null;
      }

      var storyList = new List<Story>();
      for (int page = 1; page <= MaxPages; page++)
      {
        await Task.Delay(TimeSpan.FromSeconds(1));
        var filter = $"filter_query[meta_robots][not_like]=noindex*&filter_query[exclude_from_sitemap][is]=false&starts_with={(storyblokLanguage == LanguageRepository.StoryblokDefaultUrlLanguage ? $"[{LanguageRepository.StoryblokDefaultLanguage}]" : storyblokLanguage)}/*&version=published&cv={space.Version}&per_page={StoriesPerPage}&page={page}";
        var stories = await StoryblokRepository.GetStories(filter);

        if (stories is Stories.Success storiesSuccess && storiesSuccess.StoriesList.Any())
        {
          storyList.AddRange(storiesSuccess.StoriesList);
        }
        else
        {
          break;
        }
      }

      return storyList;
    }

    public void UploadSitemap(BaseSitemap sitemap, ILogger log)
    {
      UploadSitemaps(new List<BaseSitemap>{ sitemap}, log);
    }

    public void UploadSitemaps(IEnumerable<BaseSitemap> sitemaps, ILogger log)
    {
      var options = new BlobUploadOptions
      {
        HttpHeaders = new BlobHttpHeaders { ContentType = "application/xml" }
      };

      var containerClient = new BlobContainerClient(Environment.GetEnvironmentVariable("SITEMAP_STORAGE_CONNECTION_STRING", EnvironmentVariableTarget.Process), Environment.GetEnvironmentVariable("SITEMAP_STORAGE_CONTAINER_NAME", EnvironmentVariableTarget.Process));

      foreach (var sitemap in sitemaps)
      {
        try
        {
          var client = containerClient.GetBlobClient(sitemap.FileName);

          using (var ms = new MemoryStream())
          {
            sitemap.WriteXmlStream(ms);
            ms.Position = 0;
            client.Upload(ms, options);
          }
        }
        catch (Azure.RequestFailedException failedException)
        {
          log.LogWarning(failedException, "SITEMAP BUILDER: Failed to upload Sitemap " + sitemap.FileName);
        }
      }
    }
  }
}
