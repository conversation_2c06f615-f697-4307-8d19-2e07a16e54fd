using System;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Dornbracht.Api.Util.Models.UserMigration;
using Microsoft.Azure.ServiceBus;

namespace Dornbracht.Api.Util.Services
{
  public class TransferProductlistsQueueService
  {
    private QueueClient Client { get; init; }

    public TransferProductlistsQueueService()
    {
      var queueConnectionString = Environment.GetEnvironmentVariable("ASB_LIST_MIGRATION_QUEUE_CONNECTION_STRING", EnvironmentVariableTarget.Process);
      Client = new QueueClient(new ServiceBusConnectionStringBuilder(queueConnectionString));
    }

    public async Task SendMessage(string from, string to)
    {
      var messageJson = JsonSerializer.Serialize(new TransferProductlistsPayload(from, to));
      var message = new Message(Encoding.UTF8.GetBytes(messageJson))
      {
        MessageId = Guid.NewGuid().ToString(),
        ContentType = "application/json"
      };
      await Client.SendAsync(message);
    }
  }
}
