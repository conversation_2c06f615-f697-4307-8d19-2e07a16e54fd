using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;
using Dornbracht.Api.Util.Models;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Languages;
using Dornbracht.Functions.Extensions.Models.Sitemap;
using Dornbracht.Functions.Extensions.Models.Storyblok;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Util.Services
{
  public class ImageSitemapService : BaseSitemapService
  {

    public ImageSitemapService(IStoryblokRepository storyblokRepository) : base(storyblokRepository)
    {
    }

    public async Task BuildImageSitemaps(Space space, ILogger log)
    {
      foreach (var language in LanguageRepository.StoryblokLanguages)
      {
        await BuildImageSitemapsByLanguage(language, space, log);
      }
    }

    public async Task BuildImageSitemapsByLanguage(string storyblokLanguage, Space space, ILogger log)
    {
      var stories = await FetchAllStories(storyblokLanguage, space);
      var urlLanguages = LanguageRepository.GetUrlLanguages(storyblokLanguage);

      foreach (var urlLanguage in urlLanguages)
      {
        var sitemapNodes = new List<ImageSitemapNode>();

        foreach (var story in stories)
        {
          if (story.IsValidPageType)
          {
            string url = urlLanguage;
            if (!story.Slug.ToLower().Equals(DefaultHomePage))
            {
              if (storyblokLanguage == LanguageRepository.StoryblokDefaultUrlLanguage)
              {
                url = string.Concat(url, "/", story.FullSlug);
              }
              else
              {
                url = string.Concat(url, story.FullSlug.AsSpan(story.FullSlug.IndexOf('/')));
              }
            }
            sitemapNodes.Add(new ImageSitemapNode(url, FetchImageUrls(story, urlLanguage)));
          }
        }

        UploadSitemap(new ImageSitemap($"{urlLanguage}/ImageSitemap.{urlLanguage}.xml", sitemapNodes, Domain), log);
      }

    }

    private static IEnumerable<string> FetchImageUrls(Story story, string urlLanguage)
    {
      var assetStart = "\"assets\":[";
      var urls = new List<string>();
      foreach (var contentElement in story.Content.Values)
      {
        var contentValue = contentElement.ToString();

        var indexOfAsset = contentValue.IndexOf(assetStart);
        while (indexOfAsset >= 0)
        {
          var closingBrace = contentValue.IndexOf("],", indexOfAsset) + 1;

          var assetString = string.Concat("{", contentValue[indexOfAsset..closingBrace], "}");

          var assets = JsonSerializer.Deserialize<PictureparkAssets>(assetString);
          urls.AddRange(BuildImageSitemapAssetUrls(assets, urlLanguage));

          indexOfAsset = contentValue.IndexOf(assetStart, closingBrace);
        }
      }

      return urls;
    }

    private static IEnumerable<string> BuildImageSitemapAssetUrls(PictureparkAssets assets, string urlLanguage)
    {
      var assetUrls = new List<string>();
      foreach (var asset in assets.Assets)
      {
        if (!asset.FileName.TryGetValue(urlLanguage, out var fileName) && !asset.FileName.TryGetValue(LanguageRepository.StoryblokDefaultUrlLanguage, out fileName))
        {
          fileName = asset.Source["filename"].GetString();
        }

        var url = string.Concat(asset.Url, "/quality:85/", fileName, ".webp");
        assetUrls.Add(url);
      }
      return assetUrls;
    }
  }
}
