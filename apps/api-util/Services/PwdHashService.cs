using System;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using Dornbracht.Api.Util.Models;
using Microsoft.Extensions.Logging;
using Microsoft.WindowsAzure.Storage.Table;
namespace Dornbracht.Api.Util.Services
{
  public class PwdHashService
  {
    private CloudTableClient _cloudTableClient;

    public PwdHashService(CloudTableClient cloudTableClient)
    {
      this._cloudTableClient = cloudTableClient;
    }

    public PwdHashCheckResponse CheckPwdHash(PwdHashCheckRequest request, MigratedPwdHashEntity migratedPwdHash, ILogger log)
    {
      string encodedPwd = EncodePassword(request.Password, migratedPwdHash.PasswordSalt);
      if (migratedPwdHash.Password.Equals(encodedPwd))
      {
        return new PwdHashCheckResponse(true, false);
      }
      return new PwdHashCheckResponse(false, true);
    }

    public async Task<MigratedPwdHashEntity> LoadMigratedPwdHashOfUser(string signInName, ILogger log)
    {
      CloudTable table = _cloudTableClient.GetTableReference("migration");

      TableOperation retrieveOperation = TableOperation.Retrieve<MigratedPwdHashEntity>("pk", signInName);
      TableResult result = await table.ExecuteAsync(retrieveOperation);
      return result?.Result == null ? null : result?.Result as MigratedPwdHashEntity;
    }

    private string EncodePassword(string pass, string salt)
    {
      byte[] bytes = Encoding.Unicode.GetBytes(pass);
      byte[] src = Convert.FromBase64String(salt);

      byte[] numArray = new byte[src.Length + bytes.Length];
      Buffer.BlockCopy((Array)src, 0, (Array)numArray, 0, src.Length);
      Buffer.BlockCopy((Array)bytes, 0, (Array)numArray, src.Length, bytes.Length);

      byte[] inArray = HashAlgorithm.Create("SHA1").ComputeHash(numArray);
      return Convert.ToBase64String(inArray);
    }
  }
}
