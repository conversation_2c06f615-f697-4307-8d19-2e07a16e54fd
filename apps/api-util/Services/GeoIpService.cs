using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using MaxMind.GeoIP2;
using MaxMind.GeoIP2.Model;
using MaxMind.GeoIP2.Responses;

namespace Dornbracht.Api.Util.Services
{
  public class GeoIpService
  {

    public GeoIpService(WebServiceClient client)
    {
      Client = client;
    }

    private WebServiceClient Client { get; }

    public async Task<CityResponse> GetGeoInformationFromIp(IPAddress ip)
    {
      return await Client.CityAsync(ip);
    }
  }
}
