using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Util.Models
{
  public record PictureparkAssets
  {
     [JsonPropertyName("assets")]
     public List<PictureparkAsset> Assets { get; init; }
  }

  public record PictureparkAsset
  {
    [JsonPropertyName("url")]
    public string Url { get; init; }

    [JsonPropertyName("source")]
    public IDictionary<string, JsonElement> Source { get; init; }

    [JsonPropertyName("filename")]
    public IDictionary<string, string> FileName { get; init; }
  }
}


