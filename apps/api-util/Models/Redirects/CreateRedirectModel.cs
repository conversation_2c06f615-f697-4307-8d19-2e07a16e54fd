using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Util.Models.Redirects
{
  public record struct CreateRedirectModel
  {
    public CreateRedirectModel(string path, string target)
    {
      Path = path;
      Target = target;
    }

    /// <summary>
    /// The old path that should be redirected. The value must start with a / (slash).
    /// </summary>
    [JsonPropertyName("path")]
    [Required]
    [DefaultValue("/de/old/path")]
    public string Path { get; init; }

    /// <summary>
    /// The url where the old path should be redirected to. The value must start with a / (slash).
    /// </summary>
    [JsonPropertyName("target")]
    [Required]
    [DefaultValue("/de-de/new/path")]
    public string Target { get; init; }

    /// <summary>
    /// Indicates whether the redirect is temporary (302) or not (301).
    /// </summary>
    [JsonPropertyName("temporary")]
    [DefaultValue(false)]
    public bool Temporary { get; init; } = false;
  }
}
