using System.Text.Json.Serialization;

namespace Dornbracht.Api.Util.Models.Redirects
{
  public record RedirectResponse
  {
    private RedirectResponse() { }

    public record Redirect() : RedirectResponse
    {
      public Redirect(RedirectItem item) : this()
      {
        Target = item.Target;
        Temporary = item.Temporary;
      }

      [JsonPropertyName("target")]
      public string Target { get; init; }

      [JsonPropertyName("temporary")]
      public bool Temporary { get; init; }
    }

    public record NoRedirect() : RedirectResponse
    {

    }
  }
}
