using System.Net.Http;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle;

namespace Dornbracht.Functions.Extensions.Swagger
{
  public static class SwashBuckleClientExtensions
  {
    public static Task<HttpResponseMessage> CreateSwaggerJsonDocumentResponseAsync(this ISwashBuckleClient client, HttpRequestMessage req)
    {
      return Task.FromResult(client.CreateSwaggerJsonDocumentResponse(req));
    }

    public static Task<HttpResponseMessage> CreateSwaggerUIResponseAsync(this ISwashBuckleClient client, HttpRequestMessage req, string documentRoute)
    {
      return Task.FromResult(client.CreateSwaggerUIResponse(req, documentRoute));
    }
  }
}
