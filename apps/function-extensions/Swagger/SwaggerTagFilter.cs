using System.Collections.Generic;
using System.Linq;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Dornbracht.Functions.Extensions.Swagger
{
  public class SwaggerTagFilter : IOperationFilter
  {
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
      var attributes = context.ApiDescription.CustomAttributes().Where(attribute => attribute is SwaggerTagAttribute).Select(attribute => attribute as SwaggerTagAttribute);
      if (attributes.Count() == 0)
      {
        return;
      }

      var list = new List<OpenApiTag>();
      foreach (var attribute in attributes)
      {
        if (attribute == null)
        {
          continue;
        }

        list.Add(new OpenApiTag() { Name = attribute.Tag, Description = attribute.Description });
      }

      operation.Tags = list;
    }
  }
}
