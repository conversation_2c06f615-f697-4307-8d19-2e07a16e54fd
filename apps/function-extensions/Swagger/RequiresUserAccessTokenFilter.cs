using System.Collections.Generic;
using System.Linq;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Dornbracht.Functions.Extensions.Swagger
{
  public class RequiresUserAccessTokenFilter : IOperationFilter
  {
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
      var attributes = context.ApiDescription.CustomAttributes().Where(attribute => attribute is RequiresUserAccessTokenAttribute).Select(attribute => attribute as RequiresUserAccessTokenAttribute);
      if (attributes.Count() == 0)
      {
        return;
      }

      var oAuthScheme = new OpenApiSecurityScheme
      {
        Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "Bearer" }
      };

      operation.Security = new List<OpenApiSecurityRequirement>
                {
                    new OpenApiSecurityRequirement
                    {
                        [ oAuthScheme ] = new List<string>()
                    }
                };
    }
  }
}
