using System.Dynamic;
using System;
using System.Reflection;
using AzureFunctions.Extensions.Swashbuckle;
using AzureFunctions.Extensions.Swashbuckle.Settings;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Dornbracht.Functions.Extensions.Swagger
{
  public static class SwaggerHostBuilderExtensions
  {
    public static void AddFunctionsSwagger(this IFunctionsHostBuilder builder, Assembly assembly, string baseUrl, string jsonRoute, string jsonFunctionKey, bool enableAccessToken, Action<SwaggerDocOptions> swaggerDocOptions, Action<SwaggerGenOptions> swaggerGenOptions = null)
    {
      builder.AddSwashBuckle(assembly, (options) =>
      {
        options.OverridenPathToSwaggerJson = new Uri($"{baseUrl.TrimEnd('/')}/{jsonRoute.TrimStart('/')}{(!string.IsNullOrEmpty(jsonFunctionKey) ? $"?code={jsonFunctionKey}" : string.Empty)}");
        options.AddCodeParameter = true;
        options.PrependOperationWithRoutePrefix = true;

        swaggerDocOptions(options);

        options.ConfigureSwaggerGen = (gen =>
              {
                gen.AddServer(new OpenApiServer { Url = $"{baseUrl.TrimEnd('/')}" });

                gen.CustomSchemaIds(x =>
                {
                  if (x.IsGenericType)
                  {
                    return x.Name;
                  }
                  return x.FullName?.Replace($"{x.Namespace}.", string.Empty).Replace("+", ".");
                });

                if (enableAccessToken)
                {
                  gen.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                  {
                    Description = "User Access Token (JWT) from Azure AD B2C",
                    In = ParameterLocation.Header,
                    Name = "Authorization",
                    Type = SecuritySchemeType.Http,
                    Scheme = "Bearer"
                  });

                  gen.OperationFilter<RequiresUserAccessTokenFilter>();
                }

                gen.CustomOperationIds(e => $"{e.ActionDescriptor.RouteValues["controller"]}_{e.HttpMethod}");

                gen.OperationFilter<CustomQueryParameterFilter>();
                gen.OperationFilter<SwaggerTagFilter>();

                gen.UseOneOfForPolymorphism();

                if (swaggerGenOptions != null)
                {
                  swaggerGenOptions(gen);
                }
              });
      });
    }
  }
}
