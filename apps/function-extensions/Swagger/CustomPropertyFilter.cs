using System.Collections.Generic;
using System.Linq;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Dornbracht.Functions.Extensions.Swagger
{
  public class CustomQueryParameterFilter : IOperationFilter
  {

    public CustomQueryParameterFilter(ISchemaGenerator schemaGenerator)
    {
      SchemaGenerator = schemaGenerator;
    }

    private ISchemaGenerator SchemaGenerator { get; }

    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
      var attributes = context.ApiDescription.CustomAttributes().Where(attribute => attribute is CustomQueryParameterAttribute).Select(attribute => attribute as CustomQueryParameterAttribute);
      if (attributes.Count() == 0)
      {
        return;
      }

      var list = new List<OpenApiParameter>();
      list.AddRange(operation.Parameters.Where(p => p.In != ParameterLocation.Query));
      foreach (var attribute in attributes)
      {
        if (attribute == null)
        {
          continue;
        }

        var schema = SchemaGenerator.GenerateSchema(attribute.ParameterType, new SchemaRepository());

        if (attribute.ParameterDefault != null)
        {
          schema.Default = OpenApiAnyFactory.CreateFor(schema, attribute.ParameterDefault);
        }

        list.Add(new OpenApiParameter
        {
          Name = attribute.ParameterName,
          @In = ParameterLocation.Query,
          Required = attribute.Required,
          Description = attribute.Description,
          Schema = schema
        });
      }

      list.AddRange(operation.Parameters.Where(p => p.In == ParameterLocation.Query));
      operation.Parameters = list;
    }
  }
}
