using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Builder;
using Dornbracht.Functions.Extensions.Models.Storyblok;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Functions.Extensions.Storyblok
{
  public class StoryblokManagementRepository : IStoryblokManagementRepository
  {
    private HttpClient Client { get; init; }

    private ILogger Log { get; }

    private string BaseUrl { get; init; }

    private string Token { get; init; }

    public StoryblokManagementRepository(IHttpClientFactory httpClientFactory, ILogger log)
    {
      Client = httpClientFactory.CreateClient();
      Log = log;
      BaseUrl = "https://mapi.storyblok.com/v1";
      Token = Environment.GetEnvironmentVariable("STORYBLOK_MANAGEMENT_API_TOKEN", EnvironmentVariableTarget.Process);
    }

    public async Task<ManagementStoryResult> GetStory(int space, string fullSlug)
    {
      var requestUrl = $"stories/{fullSlug.TrimStart('/')}";

      return await BuildRequest<ManagementStoryResult>(HttpMethod.Get, space, requestUrl)
      .MapJson<ManagementStoryResult.Success>(HttpStatusCode.OK)
      .Map<ManagementStoryResult.NotAuthorized>(HttpStatusCode.Unauthorized)
      .Map<ManagementStoryResult.NotFound>(HttpStatusCode.NotFound)
      .ExecuteAsync();
    }

    public async Task<ManagementStoryResult> GetStory(int space, long id)
    {
      return await GetStory(space, id.ToString());
    }

    private HttpRequestMessage CreateRequest(HttpMethod method, int space, string requestUrl)
    {
      var message = new HttpRequestMessage(method, $"{BaseUrl.TrimEnd('/')}/spaces/{space}/{requestUrl.TrimStart('/')}");
      message.Headers.Add("Authorization", Token);

      return message;
    }

    private HttpRequestActionBuilder<TResult> BuildRequest<TResult>(HttpMethod method, int space, string requestUrl)
    {
      var request = CreateRequest(method, space, requestUrl);
      var builder = new HttpRequestActionBuilder<TResult>(Client, request);

      return builder;
    }
  }
}
