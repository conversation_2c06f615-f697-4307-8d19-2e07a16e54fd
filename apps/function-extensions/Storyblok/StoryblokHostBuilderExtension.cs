using Dornbracht.Functions.Extensions.Abstraction;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;

namespace Dornbracht.Functions.Extensions.Storyblok
{
  public static class StoryblokHostBuilderExtension
  {
    /// <summary>
    /// Adds the Storyblok repository to the DI container. The repository needs the following environment variables to work properly: STORYBLOK_PREVIEW_API_TOKEN
    /// </summary>
    /// <param name="builder"></param>
    public static void AddStoryblokRepository(this IFunctionsHostBuilder builder)
    {
      builder.Services.AddSingleton<IStoryblokRepository, StoryblokRepository>();
    }

    /// <summary>
    /// Adds the Storyblok Management repository to the DI container. The repository needs the following environment variables to work properly: STORYBLOK_MANAGEMENT_API_TOKEN
    /// </summary>
    /// <param name="builder"></param>
    public static void AddStoryblokManagementRepository(this IFunctionsHostBuilder builder)
    {
      builder.Services.AddSingleton<IStoryblokManagementRepository, StoryblokManagementRepository>();
    }
  }
}
