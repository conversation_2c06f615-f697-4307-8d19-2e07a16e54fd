using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Builder;
using Dornbracht.Functions.Extensions.Models.Storyblok;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Functions.Extensions.Storyblok
{
  public class StoryblokRepository : IStoryblokRepository
  {
    private HttpClient Client { get; init; }
    private ILogger Log { get; }
    private string BaseUrl { get; init; }
    protected string Token { get; init; }

    private static List<string> PageTypes => new List<string> {"content-page","category-page", "collection-page",
                                                              "subcategory-page", "series-detail-page", "area-overview-page"};

    public StoryblokRepository(IHttpClientFactory httpClientFactory, ILogger log)
    {
      Client = httpClientFactory.CreateClient();
      Log = log;
      BaseUrl = "https://api.storyblok.com/v2/cdn";
      Token = Environment.GetEnvironmentVariable("STORYBLOK_PREVIEW_API_TOKEN", EnvironmentVariableTarget.Process);
    }

    public async Task<Stories> GetStories()
    {

      var builder = BuildRequest<Stories>(HttpMethod.Get, "/stories/", Token);
      return await builder.MapJson<Stories.Success>(HttpStatusCode.OK)
      .Map<Stories.NotAuthorized>(HttpStatusCode.Unauthorized)
      .ExecuteAsync();
    }

    public async Task<Stories> GetStories(string filter)
    {
      var builder = BuildRequest<Stories>(HttpMethod.Get, "/stories/", Token, filter);
      return await builder.MapJson<Stories.Success>(HttpStatusCode.OK)
      .Map<Stories.NotAuthorized>(HttpStatusCode.Unauthorized)
      .ExecuteAsync();
    }

    public async Task<StoryResult> GetStory(string fullSlug, string language)
    {

      var requestUrl = $"/stories/{fullSlug.TrimStart('/')}";

      return await BuildRequest<StoryResult>(HttpMethod.Get, requestUrl, Token, $"language={language}")
      .MapJson<StoryResult.Success>(HttpStatusCode.OK)
      .Map<StoryResult.NotAuthorized>(HttpStatusCode.Unauthorized)
      .Map<StoryResult.NotFound>(HttpStatusCode.NotFound)
      .ExecuteAsync();
    }

    public async Task<StoryResult> GetStory(long id, string language)
    {
      return await GetStory(id.ToString(), language);
    }

    public async Task<SpaceResult> GetSpace()
    {
      var builder = BuildRequest<SpaceResult>(HttpMethod.Get, "/spaces/me", Token);
      return await builder.MapJson<SpaceResult.Success>(HttpStatusCode.OK)
      .Map<SpaceResult.NotAuthorized>(HttpStatusCode.Unauthorized)
      .ExecuteAsync();
    }

    public static bool IsValidPageType(string pageType)
    {
      return PageTypes.Any(ptype => pageType.Equals(ptype, StringComparison.InvariantCultureIgnoreCase));
    }

    private HttpRequestMessage CreateRequest(HttpMethod method, string requestUrl, string token)
    {
      return new HttpRequestMessage(method, $"{BaseUrl.TrimEnd('/')}/{requestUrl.TrimStart('/')}?token={token}");
    }

    private HttpRequestMessage CreateRequest(HttpMethod method, string requestUrl, string token, string filter)
    {
      return new HttpRequestMessage(method, $"{BaseUrl.TrimEnd('/')}/{requestUrl.TrimStart('/')}?token={token}&{filter}");
    }

    private HttpRequestActionBuilder<TResult> BuildRequest<TResult>(HttpMethod method, string requestUrl, string token)
    {
      var request = CreateRequest(method, requestUrl, token);
      var builder = new HttpRequestActionBuilder<TResult>(Client, request);

      return builder;
    }

    private HttpRequestActionBuilder<TResult> BuildRequest<TResult>(HttpMethod method, string requestUrl, string token, string filter)
    {
      var request = CreateRequest(method, requestUrl, token, filter);
      var builder = new HttpRequestActionBuilder<TResult>(Client, request);

      return builder;
    }
  }
}
