using System;
using Dornbracht.Functions.Extensions.Abstraction;
using Microsoft.AspNetCore.Http;

namespace Dornbracht.Functions.Extensions
{
  public static class HttpRequestExtensions
  {
    private const string CacheTypePrivate = "private";
    private const string CacheTypePublic = "public";
    private const string AuthorizationHeaderName = "Authorization";

    public static void AddMaxAgeHttpCacheHeader(this HttpRequest req, TimeSpan maxAge, HttpCacheType type = HttpCacheType.None)
    {
      var cacheType = string.Empty;
      switch (type)
      {
        case HttpCacheType.Public:
          cacheType = CacheTypePublic;
          break;
        case HttpCacheType.Private:
          cacheType = CacheTypePrivate;
          break;
      }

      req.HttpContext.Response.Headers.Add("Cache-Control", $"{(!string.IsNullOrEmpty(cacheType) ? $"{cacheType}, " : "")}max-age={maxAge.TotalSeconds}");
    }

    public static void AddNoCacheHeader(this HttpRequest req)
    {
      req.HttpContext.Response.Headers.Add("Cache-Control", "no-cache");
    }

    public static void AddVaryHeader(this HttpRequest req, string[] headerNames)
    {
      req.HttpContext.Response.Headers.Add("Vary", string.Join(", ", headerNames));
    }

    public static void AddVaryByAuthorization(this HttpRequest req)
    {
      req.AddVaryHeader(new[] { AuthorizationHeaderName });
    }
  }
}
