using System;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes.Models;
using Azure.Search.Documents.Models;
using Microsoft.Spatial;

namespace Dornbracht.Functions.Extensions.Abstraction
{
  /// <summary>
  /// The search repository uses the following environment variables to connect to Azure Search: SEARCH_SERVICES_ENDPOINT, SEARCH_SERVICES_QUERY_KEY, SEARCH_SERVICES_MANAGEMENT_KEY
  /// </summary>
  public interface ISearchRepository
  {
    /// <summary>
    /// Gets a <see cref="Azure.Search.Documents.SearchClient" /> instance for an index that can perform actions on the index. Can be either a client with query or management privileges.
    /// </summary>
    /// <param name="indexName">Name of the index.</param>
    /// <param name="managementClient">Set to true if the client should use the management key to be able to perform write operations.</param>
    /// <returns>Returns the <see cref="Azure.Search.Documents.SearchClient" /> instance.</returns>
    SearchClient GetSearchClient(string indexName, bool managementClient = false);

    /// <summary>
    /// Checks if a index exists with the given name.
    /// </summary>
    /// <param name="indexName">The index name that should be checked.</param>
    /// <returns>True if an index exists for the index name. False if no index is found for the index name.</returns>
    Task<bool> IndexExistsAsync(string indexName);

    /// <summary>
    /// Creates a new search index. The index name must be unique.
    /// </summary>
    /// <param name="index">The index definition as <see cref="Azure.Search.Documents.Indexes.Models.SearchIndex" /></param>
    /// <returns></returns>
    Task CreateIndexAsync(SearchIndex index);

    /// <summary>
    /// Updates the index definition of an existing index or creates the index if it does not exist. The <see cref="Azure.Search.Documents.Indexes.FieldBuilder" /> is used here to get the index definition from the document model type (via Azure Search SDK attributes).
    /// </summary>
    /// <typeparam name="TModel">Model of the entities in the index.</typeparam>
    /// <param name="indexName">Name of the index. The name must be unique.</param>
    /// <param name="suggester">Optional: A <see cref="Azure.Search.Documents.Indexes.Models.SearchSuggester" /> for search suggestions.</param>
    /// <returns></returns>
    Task CreateOrUpdateIndexAsync<TModel>(string indexName, SearchSuggester suggester = null);

    /// <summary>
    /// Deletes the entire index from the Search Service. (There is no backup, deleted means: Gone for ever!)
    /// </summary>
    /// <param name="indexName">Name of the index.</param>
    /// <returns></returns>
    Task DeleteIndexAsync(string indexName);

    /// <summary>
    /// Gets information about a search index.
    /// </summary>
    /// <param name="indexName">Name of the index.</param>
    /// <returns>Index information as <see cref="Azure.Search.Documents.Indexes.Models.SearchIndex" /></returns>
    Task<SearchIndex> GetIndexAsync(string indexName);

    /// <summary>
    /// Gets a documents from the index by it's id.
    /// </summary>
    /// <typeparam name="TModel">Model of the entities in the index.</typeparam>
    /// <param name="client"><see cref="Azure.Search.Documents.SearchClient" /> for the index that should be accessed.</param>
    /// <param name="id"></param>
    /// <returns>Returns the model if a document was found for the specified id or null if no document was found.</returns>
    Task<TModel> GetDocumentAsync<TModel>(SearchClient client, string id);

    /// <summary>
    /// Runs a batch of <see cref="Azure.Search.Documents.Models.IndexDocumentsAction" /> against the index. This operations requires a <see cref="Azure.Search.Documents.SearchClient" /> with management key permissions!
    /// </summary>
    /// <typeparam name="TModel">Model of the entities in the index.</typeparam>
    /// <param name="client"><see cref="Azure.Search.Documents.SearchClient" /> for the index that should be accessed.</param>
    /// <param name="batch"><cref="Azure.Search.Documents.Models.IndexDocumentsBatch" /> of <see cref="Azure.Search.Documents.Models.IndexDocumentsAction" />. Static methods of <see cref="Azure.Search.Documents.Models.IndexDocumentsAction" /> can be used to create new actions.</param>
    /// <returns></returns>
    Task IndexDocumentsAsync<TModel>(SearchClient client, IndexDocumentsBatch<TModel> batch);

    /// <summary>
    /// Starts a search for the given search text.
    /// </summary>
    /// <typeparam name="TModel">Model of the entities in the index.</typeparam>
    /// <param name="client"><see cref="Azure.Search.Documents.SearchClient" /> for the index that should be accessed.</param>
    /// <param name="searchText">Use "*" to match all documents.</param>
    /// <returns>Search Results as <see cref="Azure.Search.Documents.Models.SearchResults" /></returns>
    Task<SearchResults<TModel>> SearchAsync<TModel>(SearchClient client, string searchText);

    /// <summary>
    /// Starts a search with the given <see cref="Azure.Search.Documents.SearchOptions" /> and search text.
    /// </summary>
    /// <typeparam name="TModel">Model of the entities in the index.</typeparam>
    /// <param name="client"><see cref="Azure.Search.Documents.SearchClient" /> for the index that should be accessed.</param>
    /// <param name="searchText">Use "*" to match all documents.</param>
    /// <param name="options"></param>
    /// <returns>Search Results as <see cref="Azure.Search.Documents.Models.SearchResults" /></returns>
    Task<SearchResults<TModel>> SearchAsync<TModel>(SearchClient client, string searchText, SearchOptions options);

    /// <summary>
    /// Reads a single field from all documents in the index.
    /// </summary>
    /// <typeparam name="TModel">Model of the entities in the index.</typeparam>
    /// <param name="client"><see cref="Azure.Search.Documents.SearchClient" /> for the index that should be accessed.</param>
    /// <param name="fieldSelector">A Lambda expression for the field that should be selected. In the form of: model => model.Property</param>
    /// <returns>List of field values.</returns>
    Task<TField[]> GetFieldFromAllDocumentsAsync<TModel, TField>(SearchClient client, Expression<Func<TModel, TField>> fieldSelector);

    /// <summary>
    /// Starts a spatial search in a radius around the given geo coordinates.
    /// </summary>
    /// <typeparam name="TModel">Model of the entities in the index.</typeparam>
    /// <param name="client"><see cref="Azure.Search.Documents.SearchClient" /> for the index that should be accessed.</param>
    /// <param name="locationFieldName">Name of a field with type <see cref="Microsoft.Spatial.GeographyPoint" />.</param>
    /// <param name="latitude"></param>
    /// <param name="longitude"></param>
    /// <param name="radius">Distance in kilometers.</param>
    /// <returns>Search Results as <see cref="Azure.Search.Documents.Models.SearchResults" /></returns>
    Task<SearchResults<TModel>> SearchGeoSpatial<TModel>(SearchClient client, string locationFieldName, double latitude, double longitude, int radius);

    /// <summary>
    /// Starts a spatial search in a radius around the given geo coordinates.
    /// </summary>
    /// <typeparam name="TModel">Model of the entities in the index.</typeparam>
    /// <param name="client"><see cref="Azure.Search.Documents.SearchClient" /> for the index that should be accessed.</param>
    /// <param name="locationProperty">Property with type <see cref="Microsoft.Spatial.GeographyPoint" /> that should be used as location.</param>
    /// <param name="latitude"></param>
    /// <param name="longitude"></param>
    /// <param name="radius">Distance in kilometers.</param>
    /// <returns>Search Results as <see cref="Azure.Search.Documents.Models.SearchResults" /></returns>
    Task<SearchResults<TModel>> SearchGeoSpatial<TModel>(SearchClient client, Expression<Func<TModel, GeographyPoint>> locationProperty, double latitude, double longitude, int radius);
  }
}
