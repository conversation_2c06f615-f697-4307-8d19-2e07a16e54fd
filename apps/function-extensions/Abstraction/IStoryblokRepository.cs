using System.Threading.Tasks;
using Dornbracht.Functions.Extensions.Models.Storyblok;

namespace Dornbracht.Functions.Extensions.Abstraction
{
  /// <summary>
  /// The storyblok repository uses the following environment variables to connect to Storyblok: STORYBLOK_PREVIEW_API_TOKEN
  /// </summary>
  public interface IStoryblokRepository
  {
    public Task<Stories> GetStories();

    public Task<Stories> GetStories(string filter);

    public Task<StoryResult> GetStory(string fullSlug, string language);

    public Task<StoryResult> GetStory(long id, string language);

    public Task<SpaceResult> GetSpace();
  }
}
