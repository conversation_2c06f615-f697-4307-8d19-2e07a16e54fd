using System.Threading.Tasks;
using Dornbracht.Functions.Extensions.Models.Storyblok;

namespace Dornbracht.Functions.Extensions.Abstraction
{
  /// <summary>
  /// The storyblok management repository uses the following environment variables to connect to Storyblok: STORYBLOK_MANAGEMENT_API_TOKEN
  /// </summary>
  public interface IStoryblokManagementRepository
  {
    public Task<ManagementStoryResult> GetStory(int space, string fullSlug);

    public Task<ManagementStoryResult> GetStory(int space, long id);
  }
}
