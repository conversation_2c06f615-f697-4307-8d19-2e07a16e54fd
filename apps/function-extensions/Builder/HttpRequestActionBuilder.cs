using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;

namespace Dornbracht.Functions.Extensions.Builder
{
  /// <summary>
  /// This builder class can be used to map possible results for an API request.
  /// Model types fo different result types must share a common base type. This is to support pattern matching and to emulate "union type"-like data structures.
  /// </summary>
  /// <remarks>
  /// Result types can be implemented like the following example:
  /// <code>
  /// public record TestResult
  /// {
  ///   public record Success : TestResult
  ///   {
  ///    public string Data { get; init; }
  ///   }
  ///
  ///   public record NotFound() : TestResult();
  ///
  ///   public record Error() : TestResult();
  ///
  ///   private TestResult() { }
  /// }
  /// </code>
  /// </remarks>
  /// <typeparam name="TResult">Common base type of the possible results.</typeparam>
  public class HttpRequestActionBuilder<TResult>
  {
    public HttpRequestActionBuilder(HttpClient client, HttpRequestMessage message)
    {
      Client = client;
      Message = message;
    }

    private HttpClient Client { get; }

    private HttpRequestMessage Message { get; }

    private Dictionary<HttpStatusCode, Func<HttpResponseMessage, Task<TResult>>> mappings =
      new Dictionary<HttpStatusCode, Func<HttpResponseMessage, Task<TResult>>>();

    public HttpRequestActionBuilder<TResult> AddHeader(string name, string value)
    {
      Message.Headers.Add(name, value);
      return this;
    }

    /// <summary>
    /// Adds a payload object as JSON string to the request body.
    /// </summary>
    /// <typeparam name="T">Type of the payload object.</typeparam>
    /// <param name="payload"></param>
    /// <returns></returns>
    public HttpRequestActionBuilder<TResult> AddPayload<T>(T payload)
    {
      Message.Content = JsonContent.Create<T>(payload);
      return this;
    }

    /// <summary>
    /// Maps a JSON response to a model type. This mapping uses System.Text.Json for deserialization.
    /// </summary>
    /// <typeparam name="T">Type of the Model.</typeparam>
    /// <param name="statusCode"><see cref="System.Net.HttpStatusCode" /></param>
    /// <returns></returns>
    public HttpRequestActionBuilder<TResult> MapJson<T>(HttpStatusCode statusCode) where T : TResult
    {
      mappings[statusCode] = async (HttpResponseMessage response) =>
      {
        using (var stream = await response.Content.ReadAsStreamAsync())
        {
          return await JsonSerializer.DeserializeAsync<T>(stream);
        }
      };

      return this;
    }

    /// <summary>
    /// Maps a JSON response to a model type and uses the map callback to collect the return value. This mapping uses System.Text.Json for deserialization.
    /// </summary>
    /// <typeparam name="T">Type of the Model.</typeparam>
    /// <typeparam name="TReturn">Type of the returned data.</typeparam>
    /// <param name="statusCode"><see cref="System.Net.HttpStatusCode" /></param>
    /// <param name="map"></param>
    /// <returns></returns>
    public HttpRequestActionBuilder<TResult> MapJson<T, TReturn>(HttpStatusCode statusCode, Func<T, TReturn> map)
      where TReturn : TResult
    {
      mappings[statusCode] = async (HttpResponseMessage response) =>
      {
        using (var stream = await response.Content.ReadAsStreamAsync())
        {
          var json = await JsonSerializer.DeserializeAsync<T>(stream);

          return map(json);
        }
      };

      return this;
    }

    /// <summary>
    /// Adds a custom mapping for a response message.
    /// </summary>
    /// <param name="statusCode"><see cref="System.Net.HttpStatusCode" /></param>
    /// <param name="map"></param>
    /// <returns></returns>
    public HttpRequestActionBuilder<TResult> Map(HttpStatusCode statusCode,
      Func<HttpResponseMessage, Task<TResult>> map)
    {
      mappings[statusCode] = map;

      return this;
    }

    /// <summary>
    /// Adds a mapping for a "data-less" type to represent a status code.
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="statusCode"><see cref="System.Net.HttpStatusCode" /></param>
    /// <returns></returns>
    public HttpRequestActionBuilder<TResult> Map<T>(HttpStatusCode statusCode) where T : TResult, new()
    {
      mappings[statusCode] = async (HttpResponseMessage response) =>
      {
        return await Task.FromResult<T>(Activator.CreateInstance<T>());
      };
      return this;
    }

    /// <summary>
    /// Executes the request action.
    /// </summary>
    /// <returns>Returns the mapped data.</returns>
    /// <exception cref="NotSupportedException">Throws an exception if no mapping is found for the status code of the response message.</exception>
    public async Task<TResult> ExecuteAsync(bool responseHeadersRead = false)
    {
      HttpResponseMessage response;

      if (responseHeadersRead)
      {
        response = await Client.SendAsync(Message, HttpCompletionOption.ResponseHeadersRead);
      }
      else
      {
        response = await Client.SendAsync(Message);
      }

      if (!mappings.ContainsKey(response.StatusCode))
      {
        throw new NotSupportedException(
          $"Unsupported status code response {response.StatusCode}! Please add a status code mapping. (Url: {response.RequestMessage.RequestUri})");
      }

      var mapping = mappings[response.StatusCode];

      return await mapping(response);
    }
  }
}
