using System;

namespace Dornbracht.Functions.Extensions.Attributes
{

  [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
  public class CustomQueryParameterAttribute : Attribute
  {
    public CustomQueryParameterAttribute(string parameterName, Type type, string description = "", bool required = true, object parameterDefault = null)
    {
      ParameterName = parameterName;
      ParameterType = type;
      Description = description;
      Required = required;
      ParameterDefault = parameterDefault;
    }

    public bool Required { get; set; }

    public object ParameterDefault { get; set; }

    public string ParameterName { get; set; }

    public Type ParameterType { get; set; }

    public string Description { get; set; }
  }
}
