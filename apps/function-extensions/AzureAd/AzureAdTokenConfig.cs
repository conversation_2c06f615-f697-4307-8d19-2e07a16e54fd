namespace Dornbracht.Functions.Extensions.AzureAd
{
  public class AzureAdTokenConfig
  {
    public string Audience { get; set; }

    public string TenantName { get; set; }

    public string ClientId { get; set; }

    public string PolicyName { get; set; }

    public string AuthorizeUrl => $"https://{TenantName}.b2clogin.com/{TenantName}.onmicrosoft.com/{PolicyName}/v2.0/.well-known/openid-configuration";
  }
}
