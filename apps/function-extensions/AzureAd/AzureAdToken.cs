using System.Linq;
using System.Security.Claims;

namespace Dornbracht.Functions.Extensions.AzureAd
{
  /// <summary>
  /// Adapted from: <see href="https://medium.com/cheranga/azure-functions-validate-azure-active-directory-tokens-using-your-own-custom-binding-4b4ff648d8ac" />
  /// </summary>
  /// <param name="User"></param>
  public record AzureAdToken(ClaimsPrincipal User)
  {
    /// <summary>
    /// Reads the user identifier from the 'sub' claim.
    /// </summary>
    /// <returns>False if the sub claim is missing or empty.</returns>
    public bool TryGetUserId(out string id)
    {
      var claim = User.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");

      id = claim?.Value;

      return !string.IsNullOrEmpty(id);
    }

    public bool TryGetUserMailAddress(out string mailAddress)
    {
      var claim = User.Claims.FirstOrDefault(claim => claim.Type.Contains(ClaimTypes.Email));

      mailAddress = claim?.Value;

      return !string.IsNullOrEmpty(mailAddress);
    }
  }
}

