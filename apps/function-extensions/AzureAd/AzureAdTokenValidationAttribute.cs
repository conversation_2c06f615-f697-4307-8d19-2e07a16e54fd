using System;
using Microsoft.Azure.WebJobs.Description;

namespace Dornbracht.Functions.Extensions.AzureAd
{
  /// <summary>
  /// Use this input binding to check for and validate an JWT access token. If no bearer token was found or if the token is invalid 'null' will be returned for the token.
  /// A <see cref="Dornbracht.Functions.Extensions.AzureAd.AzureAdToken" /> object is returned if a token is found and successfully validated.
  /// See the binding class for this attribute: <see cref="Dornbracht.Functions.Extensions.AzureAd.AzureAdTokenValidationBinding"/>
  /// </summary>
  /// <remarks>Adapted from: <see href="https://medium.com/cheranga/azure-functions-validate-azure-active-directory-tokens-using-your-own-custom-binding-4b4ff648d8ac" /></remarks>
  [Binding]
  [AttributeUsage(AttributeTargets.Parameter | AttributeTargets.ReturnValue)]
  public class AzureAdTokenValidationAttribute : Attribute
  {
    public AzureAdTokenValidationAttribute() : this(string.Empty, string.Empty)
    {
    }

    /// <summary>
    /// Check also for roles or claims.
    /// </summary>
    /// <param name="roles">Required role claims for successful validation</param>
    /// <param name="scopes">Required scopes claims for successful validation</param>
    public AzureAdTokenValidationAttribute(string roles, string scopes)
    {
      Roles = roles;
      Scopes = scopes;
    }

    public string Scopes { get; }

    public string Roles { get; }
  }
}
