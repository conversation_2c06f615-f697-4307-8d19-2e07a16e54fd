using System.Threading;
using System.Threading.Tasks;
using Microsoft.Azure.WebJobs.Description;
using Microsoft.Azure.WebJobs.Host.Config;
using Microsoft.Extensions.Configuration;

namespace Dornbracht.Functions.Extensions.AzureAd
{
  /// <summary>
  /// Adapted from: <see href="https://medium.com/cheranga/azure-functions-validate-azure-active-directory-tokens-using-your-own-custom-binding-4b4ff648d8ac" />
  /// </summary>
  [Extension(nameof(AzureAdTokenValidationBinding))]
  public class AzureAdTokenValidationBinding : IExtensionConfigProvider
  {
    private const string AzureAd = nameof(AzureAd);

    public AzureAdTokenValidationBinding(AzureAdTokenValidationService authorizationService, IConfiguration configuration)
    {
      AuthorizationService = authorizationService;
      Configuration = configuration;
    }

    private AzureAdTokenValidationService AuthorizationService { get; }

    private IConfiguration Configuration { get; }

    public void Initialize(ExtensionConfigContext context)
    {
      var rule = context.AddBindingRule<AzureAdTokenValidationAttribute>();
      rule.BindToInput(GetAzureAdTokenAsync);
    }

    private async Task<AzureAdToken> GetAzureAdTokenAsync(AzureAdTokenValidationAttribute attribute, CancellationToken cancellationToken)
    {
      if (attribute == null)
      {
        return null;
      }

      var config = Configuration.GetSection(AzureAd).Get<AzureAdTokenConfig>();


      var claimsPrincipal = await AuthorizationService.GetClaimsPrincipalAsync(attribute, config);
      if (claimsPrincipal == null)
      {
        return null;
      }

      return new AzureAdToken(claimsPrincipal);
    }
  }
}
