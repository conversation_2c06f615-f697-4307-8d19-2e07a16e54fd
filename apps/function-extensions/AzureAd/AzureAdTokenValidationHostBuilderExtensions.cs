using System.IdentityModel.Tokens.Jwt;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;

namespace Dornbracht.Functions.Extensions.AzureAd
{
  public static class AzureAdTokenValidationWebJobsBuilderExtensions
  {
    /// <summary>
    /// Adds the Azure AD token validation binding to the function host.
    /// Adapted from: <see href="https://medium.com/cheranga/azure-functions-validate-azure-active-directory-tokens-using-your-own-custom-binding-4b4ff648d8ac" />
    /// </summary>
    /// <param name="builder"></param>
    public static void AddAzureAdTokenValidationBinding(this IWebJobsBuilder builder)
    {
      // add azure function extension
      builder.AddExtension<AzureAdTokenValidationBinding>();

      // add required services
      builder.Services.AddSingleton<ISecurityTokenValidator, JwtSecurityTokenHandler>();
      builder.Services.AddSingleton<AzureAdTokenValidationService>();
    }
  }
}
