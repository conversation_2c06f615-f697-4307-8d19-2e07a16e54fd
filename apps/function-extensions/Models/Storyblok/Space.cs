using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Functions.Extensions.Models.Storyblok
{
  public record Space
  {
    [JsonPropertyName("id")]
    public int Id { get; init; }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("domain")]
    public string Domain { get; init; }

    [JsonPropertyName("version")]
    public int Version { get; init; }

    [JsonPropertyName("language_codes")]
    public List<string> LanguageCodes { get; init; }
  }
}
