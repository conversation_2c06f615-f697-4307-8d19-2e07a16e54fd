using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using Dornbracht.Functions.Extensions.Languages;
using Dornbracht.Functions.Extensions.Storyblok;

namespace Dornbracht.Functions.Extensions.Models.Storyblok
{
  public record Story
  {
    [JsonPropertyName("id")]
    public int Id { get; init; }

    [JsonPropertyName("uuid")]
    public string Uuid { get; init; }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("slug")]
    public string Slug { get; init; }

    [JsonPropertyName("full_slug")]
    public string FullSlug { get; init; }

    //Provides a full slug with "en" prefix. By default, a full_slug in Storyblok default language is provided without language
    [JsonIgnore]
    public string EnFullSlug
    {
      get
      {
        return $"{LanguageRepository.StoryblokDefaultUrlLanguage}/{FullSlug}";
      }
    }

    [JsonPropertyName("default_full_slug")]
    public string DefaultFullSlug { get; init; }

    [JsonPropertyName("created_at")]
    public string CreatedAt { get; init; }

    [JsonPropertyName("published_at")]
    public string PublishedAt { get; init; }

    [JsonPropertyName("first_published_at")]
    public string FirstPublishedAt { get; init; }

    [JsonPropertyName("release_id")]
    public int? ReleaseId { get; init; }

    [JsonPropertyName("lang")]
    public string Language { get; init; }

    [JsonPropertyName("content")]
    public IDictionary<string, JsonElement> Content { get; init; }

    [JsonPropertyName("position")]
    public int Position { get; init; }

    [JsonPropertyName("is_startpage")]
    public bool IsStartPage { get; init; }

    [JsonPropertyName("parent_id")]
    public int? ParentId { get; init; }

    [JsonPropertyName("group_id")]
    public string GroupId { get; init; }

    [JsonPropertyName("translated_slugs")]
    public List<TranslatedSlug> TranslatedSlugs { get; init; }

    [JsonIgnore]
    public bool IsValidPageType
    {
      get
      {
          return Content.TryGetValue("component", out var pageType) && StoryblokRepository.IsValidPageType(pageType.GetString());
      }
    }
  }
}
