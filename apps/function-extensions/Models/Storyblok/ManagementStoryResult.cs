using System.Text.Json.Serialization;

namespace Dornbracht.Functions.Extensions.Models.Storyblok
{
  public record ManagementStoryResult
  {
    public record Success() : ManagementStoryResult
    {

      [JsonPropertyName("story")]
      public ManagementStory Story { get; init; }
    }

    public record NotAuthorized() : ManagementStoryResult { }

    public record NotFound() : ManagementStoryResult { }

    private ManagementStoryResult() { }
  }
}
