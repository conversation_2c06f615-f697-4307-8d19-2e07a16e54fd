using System.Collections.Generic;

namespace Dornbracht.Functions.Extensions.Models.Sitemap
{
  public record SitemapNode
  {
    public string Url { get; init; }
    public string LastMod {get; init; }
    public Dictionary<string, string> UrlsByLanguage { get; init;}

    public SitemapNode(string url, string lastMod, Dictionary<string, string> urlsByLanguage)
    {
      Url = url;
      LastMod = lastMod;
      UrlsByLanguage = urlsByLanguage;
    }
  }
}
