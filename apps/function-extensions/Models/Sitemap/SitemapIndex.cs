using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;

namespace Dornbracht.Functions.Extensions.Models.Sitemap
{
  public record SitemapIndex : BaseSitemap
  {
    public SitemapIndex(string fileName, IEnumerable<string> sitemaps, string urlDomain) : base(fileName, urlDomain)
    {
      Nodes = sitemaps;
    }

    private IEnumerable<string> Nodes { get; init; }

    public override void WriteXmlStream(Stream stream)
    {
      var settings = new XmlWriterSettings()
      {
        Indent = true,
        Encoding = Encoding.UTF8
      };

      using (XmlWriter writer = XmlWriter.Create(stream, settings))
      {
        writer.WriteStartDocument();

        writer.WriteStartElement("sitemapindex", "http://www.sitemaps.org/schemas/sitemap/0.9");

        foreach (var sitemap in Nodes)
        {
          writer.WriteStartElement("sitemap");
          writer.WriteStartElement("loc");
          writer.WriteString($"{UrlDomain.TrimEnd('/')}/{sitemap}");
          writer.WriteEndElement();
          writer.WriteEndElement();
        }

        //</sitemapindex>
        writer.WriteEndElement();
        writer.WriteEndDocument();
      }
    }
  }
}
