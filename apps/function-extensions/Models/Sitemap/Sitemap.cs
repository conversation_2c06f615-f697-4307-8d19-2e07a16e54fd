using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;

namespace Dornbracht.Functions.Extensions.Models.Sitemap
{
  public record Sitemap : BaseSitemap
  {
    public Sitemap(string fileName, IEnumerable<SitemapNode> nodes, string urlDomain) : base(fileName, urlDomain)
    {
      Nodes = nodes;
    }

    private IEnumerable<SitemapNode> Nodes { get; init; }

    public override void WriteXmlStream(Stream stream)
    {
      var settings = new XmlWriterSettings()
      {
        Indent = true,
        Encoding = Encoding.UTF8
      };

      using (XmlWriter writer = XmlWriter.Create(stream, settings))
      {
        writer.WriteStartDocument(false);

        writer.WriteStartElement("urlset", "http://www.sitemaps.org/schemas/sitemap/0.9");
        writer.WriteAttributeString("xmlns", "xhtml", null, "http://www.w3.org/1999/xhtml");

        foreach (var node in Nodes)
        {
          writer.WriteStartElement("url");
          writer.WriteStartElement("loc");
          writer.WriteString(EnsureDomain(node.Url));
          writer.WriteEndElement();
          writer.WriteStartElement("lastmod");
          writer.WriteString(node.LastMod);
          writer.WriteEndElement();

          if (node.UrlsByLanguage?.Count > 0)
          {
            foreach (var languageKeyValuePair in node.UrlsByLanguage)
            {
              writer.WriteStartElement("xhtml", "link", null);
              writer.WriteAttributeString("rel", "alternate");
              writer.WriteAttributeString("hreflang", languageKeyValuePair.Key);
              writer.WriteAttributeString("href", EnsureDomain(languageKeyValuePair.Value));
              writer.WriteEndElement();
            }
          }
          //</url>
          writer.WriteEndElement();
        }
        //</urlset>
        writer.WriteEndElement();
        writer.WriteEndDocument();
      }
    }
  }
}
