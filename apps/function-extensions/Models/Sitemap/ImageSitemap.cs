using System.Linq;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Xml;

namespace Dornbracht.Functions.Extensions.Models.Sitemap
{
  public record ImageSitemap : BaseSitemap
  {
    public ImageSitemap(string fileName, IEnumerable<ImageSitemapNode> nodes, string urlDomain) : base(fileName, urlDomain)
    {
      Nodes = nodes;
    }

    private IEnumerable<ImageSitemapNode> Nodes { get; init; }

    public override void WriteXmlStream(Stream stream)
    {
      var settings = new XmlWriterSettings()
      {
        Indent = true,
        Encoding = Encoding.UTF8
      };

      using (XmlWriter writer = XmlWriter.Create(stream, settings))
      {
        writer.WriteStartDocument(false);

        writer.WriteStartElement("urlset", "http://www.sitemaps.org/schemas/sitemap/0.9");
        writer.WriteAttributeString("xmlns", "xhtml", null, "http://www.w3.org/1999/xhtml");
        writer.WriteAttributeString("xmlns", "image", null, "http://www.google.com/schemas/sitemap-image/1.1");

        foreach (var node in Nodes)
        {
          if (node.ImageUrls?.Count() > 0)
          {
            writer.WriteStartElement("url");

            writer.WriteStartElement("loc");
            writer.WriteString(EnsureDomain(node.Url));
            writer.WriteEndElement();

            foreach (var imageUrl in node.ImageUrls)
            {
              writer.WriteStartElement("image", "image", null);

              writer.WriteStartElement("image", "loc", null);
              writer.WriteValue(imageUrl);
              writer.WriteEndElement();

              //</image:image>
              writer.WriteEndElement();
            }
            //</url>
            writer.WriteEndElement();
          }
        }
        //</urlset>
        writer.WriteEndElement();
        writer.WriteEndDocument();
      }
    }
  }
}
