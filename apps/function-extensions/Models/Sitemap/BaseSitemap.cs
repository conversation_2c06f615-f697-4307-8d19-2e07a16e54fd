using System.IO;

namespace Dornbracht.Functions.Extensions.Models.Sitemap
{
  public abstract record BaseSitemap
  {
    public string FileName { get; init; }
    public readonly string UrlDomain;

    public BaseSitemap(string fileName, string urlDomain)
    {
      FileName = fileName;
      UrlDomain = urlDomain;
    }

    public abstract void WriteXmlStream(Stream stream);

    protected string EnsureDomain(string url)
    {
      if(url.Contains(UrlDomain))
      {
        return url;
      }
      return UrlDomain.TrimEnd('/') + "/" + url;
    }
  }
}
