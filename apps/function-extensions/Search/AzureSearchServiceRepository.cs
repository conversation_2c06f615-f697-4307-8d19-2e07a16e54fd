using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Globalization;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Azure;
using Azure.Core.Serialization;
using Azure.Search.Documents;
using Azure.Search.Documents.Indexes;
using Azure.Search.Documents.Indexes.Models;
using Azure.Search.Documents.Models;
using Dornbracht.Functions.Extensions.Abstraction;
using Microsoft.Spatial;

namespace Dornbracht.Functions.Extensions.Search
{
  public class AzureSearchServiceRepository : ISearchRepository
  {
    private SearchIndexClient SearchIndexClient { get; init; }

    private AzureKeyCredential DefaultQueryCredentials { get; init; }

    private AzureKeyCredential DefaultManagementCredentials { get; init; }

    private Uri Endpoint { get; init; }

    private ConcurrentDictionary<string, SearchClient> SearchClientCache = new ConcurrentDictionary<string, SearchClient>();

    public AzureSearchServiceRepository()
    {
      Endpoint = new Uri(Environment.GetEnvironmentVariable("SEARCH_SERVICES_ENDPOINT", EnvironmentVariableTarget.Process) ?? string.Empty);
      DefaultManagementCredentials = new AzureKeyCredential(Environment.GetEnvironmentVariable("SEARCH_SERVICES_MANAGEMENT_KEY", EnvironmentVariableTarget.Process) ?? string.Empty);
      DefaultQueryCredentials = new AzureKeyCredential(Environment.GetEnvironmentVariable("SEARCH_SERVICES_QUERY_KEY", EnvironmentVariableTarget.Process) ?? string.Empty);
      SearchIndexClient = new SearchIndexClient(Endpoint, DefaultManagementCredentials);
    }

    public SearchClient GetSearchClient(string indexName, bool managementClient = false)
    {
      string cacheKey = $"{indexName}-{managementClient}";

      if (SearchClientCache.TryGetValue(cacheKey, out var searchClient))
      {
        return searchClient;
      }

      var client = new SearchClient(Endpoint, indexName, managementClient ? DefaultManagementCredentials : DefaultQueryCredentials, new SearchClientOptions
      {
        Serializer = new JsonObjectSerializer(new System.Text.Json.JsonSerializerOptions
        {
          DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        })
      });

      SearchClientCache.TryAdd(cacheKey, client);

      return client;
    }

    public async Task<bool> IndexExistsAsync(string indexName)
    {
      await foreach (var name in SearchIndexClient.GetIndexNamesAsync())
      {
        if (name == indexName)
          return true;
      }

      return false;
    }

    public async Task CreateIndexAsync(SearchIndex index)
    {
      await SearchIndexClient.CreateIndexAsync(index);
    }

    public async Task<SearchIndex> GetIndexAsync(string indexName)
    {
      return await SearchIndexClient.GetIndexAsync(indexName);
    }

    public async Task CreateOrUpdateIndexAsync<TModel>(string indexName, SearchSuggester suggester = null)
    {
      var index = new SearchIndex(indexName)
      {
        Fields = new FieldBuilder().Build(typeof(TModel))
      };

      if (suggester != null)
      {
        index.Suggesters.Add(suggester);
      }

      await SearchIndexClient.CreateOrUpdateIndexAsync(index);
    }

    public async Task<TModel> GetDocumentAsync<TModel>(SearchClient client, string id)
    {
      try
      {
        var response = await client.GetDocumentAsync<TModel>(id);

        return response.Value;
      }
      catch (RequestFailedException e) when (e.Status == 404)
      {
        return default(TModel);
      }
    }

    public async Task IndexDocumentsAsync<TModel>(SearchClient client, IndexDocumentsBatch<TModel> batch)
    {
      await client.IndexDocumentsAsync<TModel>(batch);
    }

    public async Task<SearchResults<TModel>> SearchAsync<TModel>(SearchClient client, string searchText)
    {
      return await SearchAsync<TModel>(client, searchText, null);
    }

    public async Task<SearchResults<TModel>> SearchAsync<TModel>(SearchClient client, string searchText, SearchOptions searchOption)
    {
      var result = await client.SearchAsync<TModel>(searchText, searchOption);

      return result.Value;
    }

    public async Task<TField[]> GetFieldFromAllDocumentsAsync<TModel, TField>(SearchClient client, Expression<Func<TModel, TField>> fieldSelector)
    {
      var propertySelectorDelegate = fieldSelector.Compile();

      var count = await client.GetDocumentCountAsync();
      var pageSize = 500;
      var fieldList = new List<TField>();
      for (int i = 0; i < count.Value / (double)pageSize; i++)
      {
        var pageResult = await SearchAsync<TModel>(client, "*", new SearchOptions { Skip = pageSize * i, Size = pageSize });

        await foreach (var searchResult in pageResult.GetResultsAsync())
        {
          fieldList.Add(propertySelectorDelegate(searchResult.Document));
        }
      }

      return fieldList.ToArray();
    }

    public async Task DeleteIndexAsync(string indexName)
    {
      await SearchIndexClient.DeleteIndexAsync(indexName);
    }

    public async Task<SearchResults<TModel>> SearchGeoSpatial<TModel>(SearchClient client, string pointFieldName, double latitude, double longitude, int radius)
    {
      var searchOptions = new SearchOptions();
      searchOptions.Filter = $"geo.distance({pointFieldName}, geography'POINT({longitude.ToString(CultureInfo.InvariantCulture)} {latitude.ToString(CultureInfo.InvariantCulture)})') lt {radius}";
      searchOptions.OrderBy.Add($"geo.distance({pointFieldName}, geography'POINT({longitude.ToString(CultureInfo.InvariantCulture)} {latitude.ToString(CultureInfo.InvariantCulture)})') asc");

      var result = await client.SearchAsync<TModel>("*", searchOptions);

      return result.Value;
    }

    public async Task<SearchResults<TModel>> SearchGeoSpatial<TModel>(SearchClient client, Expression<Func<TModel, GeographyPoint>> property, double latitude, double longitude, int radius)
    {
      var propertyName = (property.Body as MemberExpression)?.Member.Name;

      if (propertyName == null)
      {
        throw new ArgumentException("Invalid property expression");
      }

      return await SearchGeoSpatial<TModel>(client, propertyName, latitude, longitude, radius);
    }
  }
}
