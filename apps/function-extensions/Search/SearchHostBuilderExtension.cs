using Dornbracht.Functions.Extensions.Abstraction;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;

namespace Dornbracht.Functions.Extensions.Search
{
  public static class SearchServiceHostBuilderExtensions
  {
    /// <summary>
    /// Adds the Azure Search Service repository to the DI container. The repository needs the following environment variables to work properly: SEARCH_SERVICES_ENDPOINT, SEARCH_SERVICES_QUERY_KEY, SEARCH_SERVICES_MANAGEMENT_KEY
    /// </summary>
    /// <param name="builder"></param>
    public static void AddSearchServiceRepository(this IFunctionsHostBuilder builder)
    {
      builder.Services.AddSingleton<ISearchRepository, AzureSearchServiceRepository>();
    }
  }
}
