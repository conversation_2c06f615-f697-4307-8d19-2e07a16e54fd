using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Dornbracht.Functions.Extensions.Abstraction;

namespace Dornbracht.Functions.Extensions.Messaging
{
  public class QueueSerializer : IQueueSerializer
  {
    public async Task<byte[]> SerializeQueueMessage<T>(T message)
    {
      using (var stream = new MemoryStream())
      {
        await JsonSerializer.SerializeAsync(stream, message);

        return stream.ToArray();
      }
    }
  }
}
