using Dornbracht.Functions.Extensions.Abstraction;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;

namespace Dornbracht.Functions.Extensions.Messaging
{
  public static class MessagingHostBuilderExtension
  {
    /// <summary>
    /// Adds messaging services to the DI container.
    /// </summary>
    /// <param name="builder"></param>
    public static void AddMessagingServices(this IFunctionsHostBuilder builder)
    {
      builder.Services.AddSingleton<IQueueSerializer, QueueSerializer>();
    }
  }
}
