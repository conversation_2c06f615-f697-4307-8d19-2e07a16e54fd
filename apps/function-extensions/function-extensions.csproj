<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace>Dornbracht.Functions.Extensions</RootNamespace>
    <AssemblyName>Dornbracht.Functions.Extensions</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Search.Documents" Version="11.3.0" />
    <PackageReference Include="AzureExtensions.Swashbuckle" Version="3.3.2" />
    <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="6.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="6.10.2" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="6.10.2" />
    <PackageReference Include="Microsoft.NET.Sdk.Functions" Version="4.1.1" />
    <PackageReference Include="Microsoft.Spatial" Version="7.12.2" />
    <PackageReference Include="StackExchange.Redis" Version="2.5.61" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.10.2" />
  </ItemGroup>

</Project>
