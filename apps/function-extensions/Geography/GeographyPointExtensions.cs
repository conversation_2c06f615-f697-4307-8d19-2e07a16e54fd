using System;
using Microsoft.Spatial;

namespace Dornbracht.Functions.Extensions.Geography
{
  public static class GeographyPointExtensions
  {
    private const int EarthRadius = 6371 * 1000;

    /// <summary>
    /// Implementation of Haversine formula: <see href="https://en.wikipedia.org/wiki/Haversine_formula" />
    /// </summary>
    /// <param name="point"></param>
    /// <param name="destination"></param>
    /// <returns></returns>
    public static double GetDistance(this GeographyPoint point, GeographyPoint destination)
    {
      // formula needs radians instead of degrees
      var lat1 = point.Latitude * Math.PI / 180;
      var lat2 = destination.Latitude * Math.PI / 180;
      var deltaLat = (destination.Latitude - point.Latitude) * Math.PI / 180;
      var deltaLong = (destination.Longitude - point.Longitude) * Math.PI / 180;

      return 2 * EarthRadius * Math.Asin(Math.Sqrt(Math.Pow(Math.Sin(deltaLat / 2), 2) + Math.Cos(lat1) * Math.Cos(lat2) * Math.Pow(Math.Sin(deltaLong / 2), 2)));
    }
  }
}
