using System.Collections.Generic;

namespace Dornbracht.Functions.Extensions.Languages
{
  public static class LanguageRepository
  {
    public static readonly string StoryblokDefaultLanguage = "default";
    public static readonly string StoryblokDefaultUrlLanguage = "en";
    public static readonly List<string> StoryblokLanguages = new() {
      "de",
      "en",
      "en-us",
      "es",
      "fr",
      "it"};

    //Key = Url Language, Value = Storyblok Language
    public static readonly Dictionary<string, string> LanguageMappingDictionary = new()
    {
      { "en", "en" },
      { "fr-fr", "fr" },
      { "it-it", "it" },
      { "de-at", "de" },
      { "de-de", "de" },
      { "de-ch", "de" },
      { "fr-ch", "fr" },
      { "it-ch", "it" },
      { "es-es", "es" },
      { "es-mx", "es" },
      { "en-be", "en" },
      { "fr-be", "fr" },
      { "en-ca", "en" },
      { "fr-ca", "fr" },
      { "en-cn", "en" },
      { "en-dk", "en" },
      { "en-fi", "en" },
      { "en-nl", "en" },
      { "en-no", "en" },
      { "en-pl", "en" },
      { "en-pt", "en" },
      { "en-ru", "en" },
      { "en-se", "en" },
      { "en-cz", "en" },
      { "en-tr", "en" },
      { "en-ae", "en" },
      { "en-gb", "en" },
      { "en-us", "en-us" }
    };

    public static List<string> GetUrlLanguages(string storyblokLanguage)
    {
      var languages = new List<string>();
      foreach (var languageMappingKeyValuePair in LanguageMappingDictionary)
      {
        if (languageMappingKeyValuePair.Value == storyblokLanguage)
        {
          languages.Add(languageMappingKeyValuePair.Key);
        }
      }
      return languages;
    }
  }
}
