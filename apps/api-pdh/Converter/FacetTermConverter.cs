using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Search;

namespace Dornbracht.Api.Pdh.Converter
{
  public class FacetTermConverter : JsonConverter<FacetTerm>
  {
    public override FacetTerm Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
      // create clone of object reference
      Utf8JsonReader clone = reader;

      if (clone.TokenType != JsonTokenType.StartObject)
      {
        throw new JsonException("Can not convert non-object into object!");
      }

      // read JSON tokens
      while (clone.Read())
      {
        // skip if empty JSON object
        if (clone.TokenType == JsonTokenType.EndObject)
        {
          return null;
        }

        // check properties for value type to decide what type should be used
        if (clone.TokenType == JsonTokenType.PropertyName)
        {
          // read property name
          var propertyName = clone.GetString();

          // read value
          clone.Read();

          if (propertyName == "value")
          {
            switch (clone.TokenType)
            {
              case JsonTokenType.True:
              case JsonTokenType.False:
                return JsonSerializer.Deserialize<BoolFacetTerm>(ref reader);
              case JsonTokenType.Number:
                return JsonSerializer.Deserialize<NumberFacetTerm>(ref reader);
              case JsonTokenType.String:
                return JsonSerializer.Deserialize<StringFacetTerm>(ref reader);
            }
          }
        }
      }

      return null;
    }

    public override void Write(Utf8JsonWriter writer, FacetTerm value, JsonSerializerOptions options)
    {
      switch (value)
      {
        case NumberFacetTerm numberTerm:
          JsonSerializer.Serialize(writer, numberTerm);
          break;
        case StringFacetTerm stringTerm:
          JsonSerializer.Serialize(writer, stringTerm);
          break;
      }
    }
  }
}
