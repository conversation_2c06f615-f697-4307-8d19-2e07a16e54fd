using System;
using System.Threading.Tasks;
using System.Net.Http.Headers;
using System.Text;
using Newtonsoft.Json;
using System.Net.Http;
using Dornbracht.Api.Pdh.Models.Pdh;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Pdh.Models.EventGrid;
using Microsoft.AspNetCore.Authentication;

namespace Dornbracht.Api.Pdh.Services
{
  /// <summary>
  /// Class <c>MailService</c> responsible for sending mails.
  /// </summary>
  public static class MailService
  {
    /// <summary>
    /// Method <c>CreateMessage</c> creates the MailjetMessages object for the passed parameters.
    /// </summary>
    public static MailjetMessages CreateMessage(int templateId, string senderMail, string recipientMail, string language, string uri)
    {
      // Create the email message
      MailjetAddress from = new MailjetAddress();
      from.email = senderMail;
      from.name = senderMail;

      MailjetAddress to = new MailjetAddress();
      to.email = recipientMail;
      to.name = recipientMail;

      Dictionary<string, string> variables = new Dictionary<string, string>();
      variables.Add("download_language", language.ToLower());
      variables.Add("download_link", uri);

      MailjetMessage mailjetMessage = new MailjetMessage();
      mailjetMessage.from = from;
      mailjetMessage.templateLanguage = true;
      mailjetMessage.templateId = templateId;
      mailjetMessage.to = new MailjetAddress[1];
      mailjetMessage.to[0] = to;
      mailjetMessage.variables = variables;

      MailjetMessages mailjetMessages = new MailjetMessages();
      mailjetMessages.messages = new MailjetMessage[1];
      mailjetMessages.messages[0] = mailjetMessage;

      return mailjetMessages;
    }

    /// <summary>
    /// Method <c>GetApiCredentials</c> encode the API credentials.
    /// </summary>
    public static string EncodeCredentials(string apiKey, string secretKey)
    {
      byte[] credentialBytes = Encoding.UTF8.GetBytes(apiKey + ":" + secretKey);
      return Convert.ToBase64String(credentialBytes);
    }

    public static EventGridDownloadCompleted DeserializeEventGrid(ILogger log, string payload)
    {
      log.LogDebug("EmailSend.Run EventGridEvent received...");

      try
      {
        EventGridDownloadCompleted data = JsonConvert.DeserializeObject<EventGridDownloadCompleted>(payload);

        if (!string.IsNullOrEmpty(data.RecipientEMail))
        {
          return data;
        }
        else
        {
          log.LogError("Event without RecipientEMail received.");
        }
      }
      catch (JsonSerializationException ex)
      {
        log.LogError("Error deserialize event {}", ex.Message);
      }
      catch (Exception ex)
      {
        log.LogError("Error processing event {}", ex.Message);
      }
      return null;
    }


    /// <summary>
    /// Method <c>SendMailBundleDownloadable</c> sends the bundle downloadable email to the passed recipient.
    /// </summary>
    public static bool SendMailBundleDownloadable(ILogger log, string recipientMail, string language)
    {
      log.LogDebug("SendMailBundleDownloadable...");

      string domain = Environment.GetEnvironmentVariable("DORNBRACHT_DOMAIN", EnvironmentVariableTarget.Process);

      if (string.IsNullOrEmpty(domain))
      {
        throw new InvalidOperationException("Dornbracht domain not set.");
      }

      domain = domain.EndsWith('/') ? domain : domain + "/";
      string uri = domain + "downloads/my-downloads";

      // properties
      string serviceUri = Environment.GetEnvironmentVariable("MAILJET-API-URL", EnvironmentVariableTarget.Process);
      string senderMail = Environment.GetEnvironmentVariable("MAILJET-NOREPLY", EnvironmentVariableTarget.Process);

      if (string.IsNullOrEmpty(serviceUri))
      {
        throw new InvalidOperationException("Mailjet serviceUri not set.");
      }

      if (string.IsNullOrEmpty(senderMail))
      {
        throw new InvalidOperationException("Mailjet no reply mail not set.");
      }

      // credentials
      string apiKey = Environment.GetEnvironmentVariable("MAILJET-API-KEY", EnvironmentVariableTarget.Process);
      string secretKey = Environment.GetEnvironmentVariable("MAILJET-SECRET-KEY", EnvironmentVariableTarget.Process);

      if (string.IsNullOrEmpty(apiKey))
      {
        throw new InvalidOperationException("Mailjet apikey not set.");
      }

      if (string.IsNullOrEmpty(secretKey))
      {
        throw new InvalidOperationException("Mailjet apikey not set.");
      }

      string credentialEncoded = EncodeCredentials(apiKey, secretKey);

      // mailjet template ID
      string templateIdString =
        Environment.GetEnvironmentVariable("MAILJET-TEMPLATE-ID-DOWNLOAD", EnvironmentVariableTarget.Process);

      if (string.IsNullOrEmpty(templateIdString))
      {
        throw new InvalidOperationException("Mailjet template id not set.");
      }

      int templateId = int.Parse(templateIdString);

      // Create the MailJet client
      using (HttpClient client = new HttpClient())
      {
        client.BaseAddress = new Uri(serviceUri);
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", credentialEncoded);

        // Serialize the message to JSON
        MailjetMessages mailjetMessages = CreateMessage(
          templateId,
          senderMail,
          recipientMail,
          language,
          uri);
        string json = JsonConvert.SerializeObject(mailjetMessages);

        // Send the email using POST request
        var response = client.PostAsync("send", new StringContent(json, Encoding.UTF8, "application/json"));

        if (!response.Result.IsSuccessStatusCode)
        {
          var message = response.Result.Content.ReadAsStringAsync();
          log.LogError(message.Result);
          return false;
        }

        return true;
      }
    }
  }
}
