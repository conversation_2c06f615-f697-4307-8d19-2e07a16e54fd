using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Dornbracht.Api.Pdh.Models.Pdh.Search;
using Dornbracht.Api.Pdh.Models.Pdh.Search.DocumentBundle;
using Dornbracht.Functions.Extensions.Builder;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Services
{
  public class PdhSearchServiceClient : BasePdhServiceClient
  {
    private const string WebProductCategory = "WEB_Product";

    private const string WebDocumentBundleCategory = "WEB_DocumentBundle";

    private const string DefaultOrder = "standard";

    private const int DefaultPageSize = 15;

    private const string DefaultSearchText = "*";

    public PdhSearchServiceClient(IHttpClientFactory httpClientFactory, ILogger log) : base(httpClientFactory, log)
    {
    }

    protected override string BaseUrl => Environment.GetEnvironmentVariable("DORNBRACHT_PDH_SEARCH_URL", EnvironmentVariableTarget.Process);

    protected override void SetDefaultHeaders(HttpRequestMessage requestMessage)
    {
    }

    private HttpRequestActionBuilder<TResult> BuildSearchRequest<TResult>(IEnumerable<string> categories, string language, string country, string text = DefaultSearchText, int skip = 0, int top = DefaultPageSize, string order = DefaultOrder, IEnumerable<string> facets = null, IEnumerable<string> fields = null)
    {
      var categoriesParameter = "";
      if (categories != null)
      {
        categoriesParameter = string.Join('&', categories.Select(category => $"categories={category}"));
      }
      var facetsParameter = "facets=All";
      if (facets != null)
      {
        facetsParameter = string.Join('&', facets.Select(facet => $"facets={facet}"));
      }

      var fieldList = string.Empty;
      if (fields != null)
      {
        fieldList = string.Join('&', fields.Select(field => $"fields={field}"));
      }

      var builder = CreateRequestBuilder<TResult>(HttpMethod.Post, $"api/docs?{categoriesParameter}&language={language}&country={country}&orderby={order}&skip={skip}&top={top}&{facetsParameter}&count=true{(!string.IsNullOrEmpty(fieldList) ? $"&{fieldList}" : string.Empty)}&text={text}");

      return builder;
    }

    /// <summary>
    /// Searches products based on search text and optionally facet filters. Valid facets are returned in the search result.
    /// </summary>
    /// <remarks>
    /// The search result is paged. The total amount of pages is included in the response.
    /// </remarks>
    /// <param name="language"></param>
    /// <param name="country"></param>
    /// <param name="text"></param>
    /// <param name="filter"></param>
    /// <param name="page"></param>
    /// <param name="includePrice"></param>
    /// <param name="order"></param>
    /// <returns></returns>
    public async Task<SearchResult> SearchWebProducts(string language, string country, string text, FacetFilter[] filter = null, int page = 0, bool includePrice = false, string order = null)
    {
      return await BuildSearchRequest<SearchResult>(new[] { WebProductCategory }, language, country, text, page * DefaultPageSize, DefaultPageSize, !string.IsNullOrEmpty(order) ? order : DefaultOrder, new[] { "All" })
        .AddPayload(filter ?? Array.Empty<FacetFilter>())
        .MapJson<CategoryListResult.Success, SearchResult>(HttpStatusCode.OK, (result) =>
        {
          var hitList = result.HitLists.FirstOrDefault(list => list.Name == WebProductCategory);

          if (hitList == null)
          {
            return new SearchResult.Success { Facets = new Dictionary<string, Facet>(), Results = Array.Empty<SearchDocument>() };
          }

          // Workaround: fields parameter currently is not working
          if (!includePrice)
          {
            for (int i = 0; i < hitList.Documents.Length; i++)
            {
              hitList.Documents[i].Price = null;
              hitList.Documents[i].PriceWithVat = null;
              hitList.Documents[i].Currency = string.Empty;
            }
          }

          return new SearchResult.Success { Page = page, TotalPages = (int)Math.Floor((double)hitList.TotalCount / DefaultPageSize), TotalCount = hitList.TotalCount, Facets = result.Facets, Results = hitList.Documents };
        })
        .Map<SearchResult.Error>(HttpStatusCode.InternalServerError)
        .ExecuteAsync();
    }

    /// <summary>
    /// Searches document bundles based on search text. Valid facets are returned in the search result.
    /// </summary>
    /// <param name="language"></param>
    /// <param name="country"></param>
    /// <param name="text"></param>
    /// <param name="filter"></param>
    /// <param name="page"></param>
    /// <param name="order"></param>
    /// <returns></returns>
    public async Task<DocumentBundleSearchResult> SearchWebDocumentBundles(string language, string country, string text, DocumentBundleFacetFilter[] filter = null, int page = 0, string order = null)
    {
      return await BuildSearchRequest<DocumentBundleSearchResult>(new[] { WebDocumentBundleCategory }, language, country, text, page * DefaultPageSize, DefaultPageSize, !string.IsNullOrEmpty(order) ? order : DefaultOrder, new[] { "All" })
        .AddPayload(filter ?? Array.Empty<DocumentBundleFacetFilter>())
        .MapJson<DocumentBundleResult.Success, DocumentBundleSearchResult>(HttpStatusCode.OK, (result) =>
        {
          var hitList = result.HitLists.FirstOrDefault(list => list.Name == WebDocumentBundleCategory);

          if (hitList == null)
          {
            return new DocumentBundleSearchResult.Success { Facets = new Dictionary<string, DocumentBundleFacet>(), Results = Array.Empty<SearchDocumentBundle>() };
          }

            return new DocumentBundleSearchResult.Success {  Page = page, TotalPages = (int)Math.Floor((double)hitList.TotalCount / DefaultPageSize), TotalCount = hitList.TotalCount, Facets = result.Facets, Results = hitList.Documents };
        })
        .Map<DocumentBundleSearchResult.NotFound>(HttpStatusCode.NotFound)
        .Map<DocumentBundleSearchResult.Error>(HttpStatusCode.InternalServerError)
        .ExecuteAsync();
    }
  }
}
