using System;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Dornbracht.Functions.Extensions.Builder;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Models.Pdh.Documents;

namespace Dornbracht.Api.Pdh.Services
{

  public class PdhDocumentBundleServiceClient : BasePdhServiceClient
  {

    private const string Channel = "WEB";
    private const string ApiVersion = "2.1";

    public PdhDocumentBundleServiceClient(IHttpClientFactory httpClientFactory, ILogger log) : base(httpClientFactory, log)
    {
    }

    protected override string BaseUrl => Environment.GetEnvironmentVariable("DORNBRACHT_PDH_PRODUCT_URL", EnvironmentVariableTarget.Process);

    protected override void SetDefaultHeaders(HttpRequestMessage requestMessage)
    {
      requestMessage.Headers.Add("Db-Channel", Channel);
      requestMessage.Headers.Add("Db-Api-Version", ApiVersion);
    }

    protected HttpRequestActionBuilder<TResult> BuildRequest<TResult>(HttpMethod method, string requestUrl, string market, string language, string dataLanguage, string user = null)
    {
      var builder = CreateRequestBuilder<TResult>(method, requestUrl);

      builder
      .AddHeader("Db-Ui-Language", language)
      .AddHeader("Db-Data-Language", dataLanguage)
      .AddHeader("Db-Country", market);

      if (!string.IsNullOrEmpty(user))
      {
        builder.AddHeader("Db-OnBehalfOf", user);
      }

      return builder;
    }


    /// <summary>
    /// Returns document bundles.
    /// </summary>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<DocumentBundleListResult> GetDocumentBundles(string market, string language, string userId)
    {
      return await BuildRequest<DocumentBundleListResult>(HttpMethod.Get, $"odata/documentbundles", market, language, language, userId)
          .MapJson<DocumentBundleListResult.Success>(HttpStatusCode.OK)
          .Map<DocumentBundleListResult.NotFound>(HttpStatusCode.NotFound)
          .ExecuteAsync();
    }

    /// <summary>
    /// Returns document bundle state.
    /// </summary>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="bundleId"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<DocumentBundleListEntryResult> GetDocumentBundleState(string market, string language, int bundleId, string userId)
    {
      return await BuildRequest<DocumentBundleListEntryResult>(HttpMethod.Get, $"odata/documentbundles/{bundleId}", market, language, language, userId)
          .MapJson<DocumentBundleListEntryResult.Success>(HttpStatusCode.OK)
          .Map<DocumentBundleListEntryResult.NotFound>(HttpStatusCode.NotFound)
          .ExecuteAsync();
    }

    /// <summary>
    /// Returns a specific document.
    /// </summary>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="bundleId"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    public async Task<DocumentResult> GetBundleContent(string market, string language, int bundleId, string userId)
    {
      return await GetBundleContent(market, language, bundleId, string.Empty, userId);
    }

    public async Task<DocumentResult> GetBundleContent(string market, string language, int bundleId, string fileName, string userId)
    {
      return await BuildRequest<DocumentResult>(HttpMethod.Get, $"odata/documentbundles/{bundleId}/content", market, language, language, userId).Map(HttpStatusCode.OK, async (response) =>
      {
        var stream = await response.Content.ReadAsStreamAsync();

        return new DocumentResult.Success { Data = stream, FileName = fileName };
      })
      .Map<DocumentResult.NotFound>(HttpStatusCode.NotFound)
      .Map<DocumentResult.Error>(HttpStatusCode.Gone)
      .ExecuteAsync(true);
    }

    public async Task<DocumentBundleListEntryResult> AddDocumentBundle(string market, string language, DocumentBundleAddConceptRequest conceptRequest, string userId)
    {
      return await BuildRequest<DocumentBundleListEntryResult>(HttpMethod.Post, $"odata/documentbundles", market, language, language, userId)
        .AddPayload(conceptRequest)
        .MapJson<DocumentBundleListEntryResult.Success>(HttpStatusCode.OK)
        .MapJson<DocumentBundleListEntryResult.Success>(HttpStatusCode.Accepted)
        .Map<DocumentBundleListEntryResult.NotFound>(HttpStatusCode.NotFound)
        .Map<DocumentBundleListEntryResult.Error>(HttpStatusCode.InternalServerError)
        .ExecuteAsync();
    }

    public async Task<DocumentBundleListEntryResult> AddDocumentBundle(string market, string language, DocumentBundleAddCategoryRequest categoryRequest, string userId)
    {
      return await BuildRequest<DocumentBundleListEntryResult>(HttpMethod.Post, $"odata/documentbundles", market, language, language, userId)
        .AddPayload(categoryRequest)
        .MapJson<DocumentBundleListEntryResult.Success>(HttpStatusCode.OK)
        .MapJson<DocumentBundleListEntryResult.Success>(HttpStatusCode.Accepted)
        .Map<DocumentBundleListEntryResult.NotFound>(HttpStatusCode.NotFound)
        .Map<DocumentBundleListEntryResult.Error>(HttpStatusCode.InternalServerError)
        .ExecuteAsync();
    }

     public async Task<DocumentBundleListEntryResult> RegenerateDocumentBundle(string market, string language, int bundleId, string userId)
    {
      return await BuildRequest<DocumentBundleListEntryResult>(HttpMethod.Post, $"odata/documentbundles/{bundleId}/regenerate()", market, language, language, userId)
        .MapJson<DocumentBundleListEntryResult.Success>(HttpStatusCode.OK)
        .MapJson<DocumentBundleListEntryResult.Success>(HttpStatusCode.Accepted)
        .Map<DocumentBundleListEntryResult.NotFound>(HttpStatusCode.NotFound)
        .ExecuteAsync();
    }
  }
}
