using System;
using System.Net.Http;
using Dornbracht.Api.Pdh.Configuration;
using Dornbracht.Functions.Extensions.Builder;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Services
{
  public abstract class BasePdhServiceClient
  {

    private HttpClient Client { get; init; }

    protected string ValidProductFilter => $"(((validTo gt {DateTime.UtcNow:O} and validFrom le {DateTime.UtcNow:O}) or (validTo eq null and validFrom eq null) or (validTo eq null and validFrom le {DateTime.UtcNow:O}) or (validTo gt {DateTime.UtcNow:O} and validFrom eq null)) and isPublished eq true and isDeleted eq false)";

    protected BasePdhServiceClient(IHttpClientFactory httpClientFactory, ILogger log)
    {
      Client = httpClientFactory.CreateClient(HttpClientNames.PDH);
      Log = log;
    }

    protected abstract string BaseUrl { get; }

    private ILogger Log { get; }

    protected abstract void SetDefaultHeaders(HttpRequestMessage requestMessage);

    private HttpRequestMessage CreateRequest(HttpMethod method, string requestUrl)
    {
      var request = new HttpRequestMessage(method, $"{BaseUrl.TrimEnd('/')}/{requestUrl.TrimStart('/')}");

      SetDefaultHeaders(request);

      return request;
    }

    protected HttpRequestActionBuilder<TResult> CreateRequestBuilder<TResult>(HttpMethod method, string requestUrl)
    {
      var request = CreateRequest(method, requestUrl);
      var builder = new HttpRequestActionBuilder<TResult>(Client, request);

      return builder;
    }
  }
}
