using System.Collections.Generic;
using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Functions.Extensions.Builder;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Services
{
  public class PdhProductsServiceClient : BasePdhServiceClient
  {
    private const string Channel = "WEB";
    private const string ApiVersion = "2.2";

    public PdhProductsServiceClient(IHttpClientFactory httpClientFactory, ILogger log) : base(httpClientFactory, log)
    {
    }

    protected override string BaseUrl =>
      Environment.GetEnvironmentVariable("DORNBRACHT_PDH_PRODUCT_URL", EnvironmentVariableTarget.Process);

    protected override void SetDefaultHeaders(HttpRequestMessage requestMessage)
    {
      requestMessage.Headers.Add("Db-Channel", Channel);
      requestMessage.Headers.Add("Db-Api-Version", ApiVersion);
    }

    protected HttpRequestActionBuilder<TResult> BuildRequest<TResult>(HttpMethod method, string requestUrl,
      string market, string language, string dataLanguage, string user = null)
    {
      var builder = CreateRequestBuilder<TResult>(method, requestUrl);

      builder
        .AddHeader("Db-Ui-Language", language)
        .AddHeader("Db-Data-Language", dataLanguage)
        .AddHeader("Db-Country", market);

      if (!string.IsNullOrEmpty(user))
      {
        builder.AddHeader("Db-OnBehalfOf", user);
      }

      return builder;
    }

    /// <summary>
    /// Validate the passed productNumbers for the passed market and language.
    /// </summary>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="productNumberToValidate">A list of all product numbers to valudate</param>
    /// <param name="userId">Optional: If a user id is set, price information will be returned and the user id is used for the "on behalf of" header of the PDH API.</param>
    /// <returns>Returns all valid product numbers of the passed product nu,bers</returns>
    public async Task<string[]> PostValidateProduct(string market, string language, string[] productNumberToValidate,
      string userId = null)
    {
      return await BuildRequest<string[]>(HttpMethod.Post, $"api/products/validate", market, language, language,
          userId)
        .AddPayload(productNumberToValidate)
        .MapJson<string[]>(HttpStatusCode.OK)
        .ExecuteAsync();
    }


    /// <summary>
    /// Returns basic product details for a product.
    /// </summary>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="number"></param>
    /// <param name="userId">Optional: If a user id is set, price information will be returned and the user id is used for the "on behalf of" header of the PDH API.</param>
    /// <returns></returns>
    public async Task<ProductResult> GetProduct(string market, string language, string number, string userId = null)
    {
      return await BuildRequest<ProductResult>(HttpMethod.Get, $"api/products/{number}", market, language, language,
          userId)
        .MapJson<ProductResult.Success>(HttpStatusCode.OK)
        .Map<ProductResult.NotFound>(HttpStatusCode.NotFound)
        .ExecuteAsync();
    }

    /// <summary>
    /// Returns basic product details for a list of product numbers.
    /// </summary>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="productNumbers"></param>
    /// <param name="userId">Optional: If a user id is set, price information will be returned and the user id is used for the "on behalf of" header of the PDH API.</param>
    /// <returns></returns>
    public async Task<ProductResult[]> GetProductsByNumbers(string market, string language, string[] productNumbers,
      string userId = null)
    {
      // currently "filter" is not allowed in the new API
      // var filter = string.Join(" or ", productNumbers.Select(number => $"productNumber eq '{number.Trim()}'"));
      // var url = $"odata/products?$expand=images&$filter={filter}&$count=true";

      // return await BuildRequest<ProductResultList>(HttpMethod.Get, url, market, language, language, userId)
      //          .MapJson<ProductResultList.Success>(HttpStatusCode.OK)
      //          .Map<ProductResultList.NotFound>(HttpStatusCode.NotFound)
      //          .ExecuteAsync();

      var requests = new List<Task<ProductResult>>();

      foreach (var number in productNumbers)
      {
        requests.Add(GetProduct(market, language, number.Trim(), userId));
      }

      return await Task.WhenAll(requests);
    }

    /// <summary>
    /// Returns all details for a product. Including relations (variants, sets, components, ...)
    /// </summary>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="productNumber"></param>
    /// <param name="userId">Optional: If a user id is set, price information will be returned and the user id is used for the "on behalf of" header of the PDH API.</param>
    /// <returns></returns>
    public async Task<ProductResult> GetProductDetails(string market, string language, string productNumber,
      string userId = null)
    {
      try
      {
        string path =
          $"/odata/Products/{productNumber}/v2.details";
        string parameter =
          "$expand=documents,groups,attributes,relations($expand=alternatives/related/target),parts/product,xtraoptions($expand=products/target)";
        string uri = path + "?" + parameter;

        // Bestehende Implementierung für den API-Aufruf
        var result = await BuildRequest<ProductResult>(HttpMethod.Get, uri, market, language, language, userId)
          .MapJson<ProductResult.WithDetails>(HttpStatusCode.OK)
          .Map<ProductResult.NotFound>(HttpStatusCode.NotFound)
          .ExecuteAsync();

        // Wenn das Produkt gefunden wurde, aber weitere Prüfungen erforderlich sind
        if (result is ProductResult.WithDetails details)
        {
          // Prüfen, ob das Produkt für den angegebenen Markt verfügbar ist
          if (!IsProductAvailableForMarket(details, market))
          {
            return new ProductResult.MarketUnavailable
            {
              ProductNumber = productNumber,
              MarketName = market
            };
          }

          // Prüfen, ob das Produkt noch nicht veröffentlicht ist
          if (IsProductPreview(details))
          {
            return new ProductResult.Preview
            {
              ProductNumber = productNumber
            };
          }

          // Prüfen, ob das Produkt historisch ist und Nachfolgeprodukte hat
          var successors = GetSuccessors(details);
          if (successors != null && successors.Count > 0)
          {
            return new ProductResult.Historic
            {
              ProductNumber = productNumber,
              Successors = successors
            };
          }

          // Wenn alle Prüfungen bestanden wurden, das Produkt zurückgeben
          return details;
        }

        return result;
      }
      catch (Exception ex)
      {
        // Fehlerbehandlung
        _log.LogError(ex, $"Error getting product details for {productNumber} in {market}/{language}");
        return new ProductResult.NotFound();
      }
    }

    // Hilfsmethoden zur Prüfung der verschiedenen Fehlerfälle
    private bool IsProductAvailableForMarket(ProductResult.WithDetails product, string market)
    {
      // Implementierung der Marktprüfung
      // z.B. Prüfen, ob das Produkt für den angegebenen Markt verfügbar ist
      var marketAttribute = product.Attributes?.FirstOrDefault(attr => attr.Name == "AvailableMarkets");
      if (marketAttribute != null && !string.IsNullOrEmpty(marketAttribute.Value))
      {
        var availableMarkets = marketAttribute.Value.Split(',');
        return availableMarkets.Contains(market);
      }
      return true; // Standardmäßig verfügbar
    }

    private bool IsProductPreview(ProductResult.WithDetails product)
    {
      // Implementierung der Vorschauprüfung
      // z.B. Prüfen, ob das Produkt noch nicht veröffentlicht ist
      var previewAttribute = product.Attributes?.FirstOrDefault(attr => attr.Name == "IsPreview");
      return previewAttribute != null && previewAttribute.Value == "true";
    }

    private List<Successor> GetSuccessors(ProductResult.WithDetails product)
    {
      // Implementierung der Nachfolgerproduktprüfung
      // Prüfen, ob das Produkt historisch ist und Nachfolgeprodukte hat

      var successors = new List<Successor>();

      // Prüfen, ob es Relationen vom Typ "Nachfolger" oder "Ersatz" gibt
      var replacementRelations = product.Relations?.Where(rel =>
        rel.Type == "Nachfolger" || rel.Type == "Ersatz");

      if (replacementRelations != null)
      {
        foreach (var relation in replacementRelations)
        {
          if (relation.Alternatives?.Any() == true)
          {
            foreach (var alternative in relation.Alternatives)
            {
              if (alternative.Related?.Any() == true)
              {
                foreach (var related in alternative.Related)
                {
                  var relatedProduct = related.Target;

                  successors.Add(new Successor
                  {
                    Number = relatedProduct.ProductNumber,
                    Name = relatedProduct.Name,
                    Compatible = relation.Type == "Ersatz", // Ersatzprodukte sind kompatibel, Nachfolgeprodukte nicht
                    Slug = relatedProduct.Url
                  });
                }
              }
            }
          }
        }
      }

      return successors;
    }

    public async Task<SurfaceListResult> GetSurfaces(string market, string language)
    {
      return await BuildRequest<SurfaceListResult>(HttpMethod.Get, "/odata/productgrouptypes/OberflaechentypBeschreibung/groups", market, language, language)
        .MapJson<SurfaceListResult.Success>(HttpStatusCode.OK)
        .ExecuteAsync();
    }
  }
}
