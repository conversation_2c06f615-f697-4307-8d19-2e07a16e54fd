using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System;
using System.Net.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using System.Net;
using Dornbracht.Api.Pdh.Models.Pdh.Documents;
using Dornbracht.Functions.Extensions.Builder;

namespace Dornbracht.Api.Pdh.Services
{
  public class PdhProductListsServiceClient : BasePdhDataServiceClient
  {
    public PdhProductListsServiceClient(IHttpClientFactory httpClientFactory, ILogger log) : base(httpClientFactory,
      log)
    {
    }

    protected override string BaseUrl =>
      Environment.GetEnvironmentVariable("DORNBRACHT_PDH_DATA_URL", EnvironmentVariableTarget.Process);

    private const string FallbackListName = "My Watchlist";

    private static Dictionary<string, string> TemporaryListNames => new Dictionary<string, string>
    {
      { "de", "Meine Merkliste" },
      { "en", "My Watchlist" },
      { "fr", "Ma liste de favoris" },
      { "it", "La mia lista di preferiti" },
      { "es", "Mi lista de favoritos" },
    };

    public async Task<ProductListResult> CreateProductList(string market, string language, string user, string name,
      string comment = "")
    {
      var payload = new CreateProductListModel(user, name) { Comment = comment };

      return await BuildRequest<ProductListResult>(HttpMethod.Post, $"api/ProductLists", market, language, language,
          user)
        .AddPayload(payload)
        .MapJson<ProductListResult.ProductList>(HttpStatusCode.Created)
        .Map<ProductListResult.Forbidden>(HttpStatusCode.Forbidden)
        .Map<ProductListResult.NotFound>(HttpStatusCode.NotFound)
        .ExecuteAsync();
    }

    public async Task<ProductListResult> UpdateProductList(string market, string language, string user, int listId,
      string name, string comment)
    {
      return await BuildRequest<ProductListResult>(HttpMethod.Post, $"api/ProductLists", market, language, language,
          user)
        .AddPayload(new UpdateProductListModel(user, listId, name, comment))
        .MapJson<ProductListResult.ProductList>(HttpStatusCode.Created)
        .Map<ProductListResult.Forbidden>(HttpStatusCode.Forbidden)
        .Map<ProductListResult.NotFound>(HttpStatusCode.NotFound)
        .ExecuteAsync();
    }

    public async Task<ProductListDeltaResult> AddEntryToProductList(
      string market,
      string language,
      string user,
      int listId,
      string productNumber,
      int amount,
      int? xtraOptionId = null)
    {
      HttpRequestActionBuilder<ProductListDeltaResult> builder;
      if (null == xtraOptionId)
      {
        // normal product
        builder = BuildRequest<ProductListDeltaResult>(HttpMethod.Post,
          $"api/ProductLists/{listId}/Products?choice=NONE", market, language, language, user);
        builder.AddPayload(new AddProductListEntry(productNumber, market, amount));
      }
      else
      {
        // XtraOption
        builder = BuildRequest<ProductListDeltaResult>(HttpMethod.Post,
          $"api/ProductLists/{listId}/Products?choice=FORCE", market, language, language, user);
        builder.AddPayload(new AddProductListEntryXtra(productNumber, market, xtraOptionId.Value, amount));
      }

      builder.MapJson<ProductListDeltaResult.Success>(HttpStatusCode.OK)
        .Map<ProductListDeltaResult.Conflict>(HttpStatusCode.Conflict)
        .Map<ProductListDeltaResult.Forbidden>(HttpStatusCode.Forbidden)
        .Map<ProductListDeltaResult.NotFound>(HttpStatusCode.NotFound);

      return await builder.ExecuteAsync();
    }


    public async Task<ProductListDeltaResult> RemoveEntryFromProductList(string market, string language, string user,
      int listId, int entryId)
    {
      return await BuildRequest<ProductListDeltaResult>(HttpMethod.Delete,
          $"api/ProductLists/{listId}/Products?entryId={entryId}", market, language, language, user)
        .Map<ProductListDeltaResult.Deleted>(HttpStatusCode.OK)
        .Map<ProductListDeltaResult.Forbidden>(HttpStatusCode.Forbidden)
        .Map<ProductListDeltaResult.NotFound>(HttpStatusCode.NotFound)
        .ExecuteAsync();
    }

    public async Task<ProductListSearchResult> GetProductLists(string market, string language, string user)
    {
      return await BuildRequest<ProductListSearchResult>(HttpMethod.Get,
          $"odata/ProductLists?$filter=owner eq '{user}'", market, language, language, user)
        .MapJson<ProductListSearchResult.Success>(HttpStatusCode.OK)
        .ExecuteAsync();
    }

    public async Task<ProductListSearchResult> DeleteProductList(string market, string language, string user,
      int listId)
    {
      return await BuildRequest<ProductListSearchResult>(HttpMethod.Delete, $"api/ProductLists/{listId}", market,
          language, language, user)
        .Map<ProductListSearchResult.Deleted>(HttpStatusCode.OK)
        .Map<ProductListSearchResult.NotFound>(HttpStatusCode.NotFound)
        .Map<ProductListSearchResult.Forbidden>(HttpStatusCode.Forbidden)
        .ExecuteAsync();
    }

    public async Task<ProductListResult> CopyProductList(string market, string language, string user, int listId)
    {
      return await BuildRequest<ProductListResult>(HttpMethod.Put, $"api/ProductLists?id={listId}", market, language,
          language, user)
        .MapJson<ProductListResult.ProductList>(HttpStatusCode.Created)
        .Map<ProductListResult.Forbidden>(HttpStatusCode.Forbidden)
        .Map<ProductListResult.NotFound>(HttpStatusCode.NotFound)
        .ExecuteAsync();
    }

    public async Task<ProductEntryListResult> GetProductEntries(string market, string language, string user, int listId)
    {
      return await BuildRequest<ProductEntryListResult>(HttpMethod.Get, $"odata/ProductLists({listId})/entries", market,
          language, language, user)
        .MapJson<ProductEntryListResult.Success>(HttpStatusCode.OK)
        .Map<ProductEntryListResult.Forbidden>(HttpStatusCode.Forbidden)
        .Map<ProductEntryListResult.NotFound>(HttpStatusCode.NotFound)
        .ExecuteAsync();
    }

    public async Task<ProductListResult> GetProductListDetails(string market, string language, string user, int listId)
    {
      return await BuildRequest<ProductListResult>(HttpMethod.Get,
          $"odata/ProductLists({listId})?$expand=entries($expand=children($levels=max);$orderby=order)", market,
          language, language, user)
        .MapJson<ProductListResult.ProductList>(HttpStatusCode.OK)
        .Map<ProductListResult.Forbidden>(HttpStatusCode.Forbidden)
        .Map<ProductListResult.NotFound>(HttpStatusCode.NotFound)
        .ExecuteAsync();
    }

    public async Task<ProductListResult> ReorderProductEntries(string market, string language, string user, int listId,
      Dictionary<int, int> productListOrder)
    {
      if (await GetProductListDetails(market, language, user, listId) is ProductListResult.ProductList productList)
      {
        // update the Order property for each product list entry to the new value from the reorder list
        foreach (var (order, productListEntry) in from orderEntry in productListOrder
                 let productListEntry = productList.Entries.FirstOrDefault(entry => entry.Id == orderEntry.Key)
                 where productListEntry != null
                 select (orderEntry.Value, productListEntry))
        {
          productListEntry.Order = order;
        }

        // reorder product list entries by the new Order values
        productList.Entries = productList.Entries.OrderBy(e => e.Order).ToList();

        return await BuildRequest<ProductListResult>(HttpMethod.Post, $"api/ProductLists", market, language, language,
            user)
          .AddPayload(productList)
          .MapJson<ProductListResult.ProductList>(HttpStatusCode.Created)
          .Map<ProductListResult.Forbidden>(HttpStatusCode.Forbidden)
          .Map<ProductListResult.NotFound>(HttpStatusCode.NotFound)
          .ExecuteAsync();
      }

      return new ProductListResult.NotFound();
    }

    public async Task<ProductListFactSheetResult> DownloadFactSheet(string market, string language, string user,
      int listId)
    {
      return await BuildRequest<ProductListFactSheetResult>(HttpMethod.Post, "docs/Bundle", market, language, language,
          user)
        .AddPayload(new DocumentBundleRequest<ProductListFactsheetBundleDefinition, ProductListFactsheetBundleScope>()
        {
          Definition = new ProductListFactsheetBundleDefinition { Name = "WebListFactsheets" },
          Scope = new ProductListFactsheetBundleScope { ListId = listId }
        })
        .Map(HttpStatusCode.OK, async (response) =>
        {
          var stream = await response.Content.ReadAsStreamAsync();
          return new ProductListFactSheetResult.Success { Data = stream };
        })
        .Map<ProductListFactSheetResult.Forbidden>(HttpStatusCode.Forbidden)
        .Map<ProductListFactSheetResult.NotFound>(HttpStatusCode.NotFound)
        .Map<ProductListFactSheetResult.Error>(HttpStatusCode.InternalServerError)
        .ExecuteAsync();
    }

    public async Task<TransferUserResult> TransferUserData(string from, string to,
      bool persistTemporaryEntities = false)
    {
      return await BuildRequest<TransferUserResult>(HttpMethod.Post, $"api/Users/<USER>",
          "de", "de")
        .AddPayload(new TransferUserModel { PersistTemporaryEntities = persistTemporaryEntities })
        .Map<TransferUserResult.Success>(HttpStatusCode.OK)
        .Map<TransferUserResult.Forbidden>(HttpStatusCode.Forbidden)
        .ExecuteAsync();
    }

    public async Task<ProductListResult> CreateTemporaryProductList(string market, string language, Guid user)
    {
      var name = TemporaryListNames.ContainsKey(language) ? TemporaryListNames[language] : FallbackListName;
      var payload = new CreateTemporaryProductListModel(user.ToString("N"), name);

      return await BuildRequest<ProductListResult>(HttpMethod.Post, $"api/ProductLists", market, language, language,
          user.ToString("N"))
        .AddPayload(payload)
        .MapJson<ProductListResult.ProductList>(HttpStatusCode.Created)
        .Map<ProductListResult.Forbidden>(HttpStatusCode.Forbidden)
        .Map<ProductListResult.NotFound>(HttpStatusCode.NotFound)
        .ExecuteAsync();
    }

    public async Task<(bool, ProductListResult.ProductList)> TryGetTemporaryList(string market, string language,
      Guid user, bool createIfMissing = true)
    {
      var listRequest = await GetProductLists(market, language, user.ToString("N"));

      if (listRequest is ProductListSearchResult.Success lists)
      {
        if (lists.Value.Any())
        {
          return (true, lists.Value[0]);
        }
        else if (createIfMissing)
        {
          var listCreationResult = await CreateTemporaryProductList(market, language, user);

          if (listCreationResult is ProductListResult.ProductList createdList)
          {
            return (true, createdList);
          }
        }
      }

      return (false, null);
    }

    public async Task<ProductListResult> GetTemporaryProductListDetails(string market, string language, Guid user)
    {
      if (await TryGetTemporaryList(market, language, user) is (true, var temporaryList))
      {
        return await GetProductListDetails(market, language, user.ToString("N"), temporaryList.Id);
      }

      return new ProductListResult.NotFound();
    }

    public async Task<ProductListDeltaResult> AddEntryToTemporaryProductList(
      string market,
      string language,
      Guid user,
      string productNumber,
      int amount,
      int? xtraOptionId = null)
    {
      if (await TryGetTemporaryList(market, language, user) is (true, var temporaryList))
      {
        return await AddEntryToProductList(
          market,
          language,
          user.ToString("N"),
          temporaryList.Id,
          productNumber,
          amount,
          xtraOptionId);
      }

      return new ProductListDeltaResult.NotFound();
    }

    public async Task<ProductListDeltaResult> RemoveEntryFromTemporaryProductList(string market, string language,
      Guid user, int entryId)
    {
      if (await TryGetTemporaryList(market, language, user, false) is (true, var temporaryList))
      {
        return await RemoveEntryFromProductList(market, language, user.ToString("N"), temporaryList.Id, entryId);
      }

      return new ProductListDeltaResult.Deleted();
    }

    public async Task<ProductListResult> ReorderTemporaryProductEntries(string market, string language, Guid user,
      Dictionary<int, int> productListOrder)
    {
      if (await TryGetTemporaryList(market, language, user) is (true, var temporaryList))
      {
        return await ReorderProductEntries(market, language, user.ToString("N"), temporaryList.Id, productListOrder);
      }

      return new ProductListResult.NotFound();
    }

    public async Task<ProductListFactSheetResult> DownloadTemporaryFactSheet(string market, string language, Guid user)
    {
      if (await TryGetTemporaryList(market, language, user) is (true, var temporaryList))
      {
        return await DownloadFactSheet(market, language, user.ToString("N"), temporaryList.Id);
      }

      return new ProductListFactSheetResult.NotFound();
    }
  }
}
