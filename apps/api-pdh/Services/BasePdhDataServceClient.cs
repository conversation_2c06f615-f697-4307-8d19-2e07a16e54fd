using System.Net.Http;
using Dornbracht.Functions.Extensions.Builder;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Services
{
  public abstract class BasePdhDataServiceClient : BasePdhServiceClient
  {
    private const string Channel = "WEB";
    private const string ApiVersion = "7.2";
    private const string DefaultUser = "<EMAIL>";

    protected BasePdhDataServiceClient(IHttpClientFactory httpClientFactory, ILogger log) : base(httpClientFactory, log)
    {
    }

    protected override void SetDefaultHeaders(HttpRequestMessage requestMessage)
    {
      requestMessage.Headers.Add("Db-Channel", Channel);
      requestMessage.Headers.Add("Db-Api-Version", ApiVersion);
    }

    protected HttpRequestActionBuilder<TResult> BuildRequest<TResult>(HttpMethod method, string requestUrl, string market, string language, string dataLanguage, string user)
    {
      var builder = CreateRequestBuilder<TResult>(method, requestUrl);

      return builder
      .AddHeader("Db-OnBehalfOf", user ?? DefaultUser)
      .AddHeader("Db-Ui-Language", language)
      .AddHeader("Db-Data-Language", dataLanguage)
      .AddHeader("Db-Country", market);
    }

    protected HttpRequestActionBuilder<TResult> BuildRequest<TResult>(HttpMethod method, string requestUrl, string market, string language, string dataLanguage)
    {
      return BuildRequest<TResult>(method, requestUrl, market, language, dataLanguage, DefaultUser);
    }

    protected HttpRequestActionBuilder<TResult> BuildRequest<TResult>(HttpMethod method, string requestUrl, string market, string language)
    {
      return BuildRequest<TResult>(method, requestUrl, market, language, language, DefaultUser);
    }

    protected HttpRequestActionBuilder<TResult> BuildRequest<TResult>(string requestUrl, string market, string language)
    {
      return BuildRequest<TResult>(HttpMethod.Get, requestUrl, market, language, language, DefaultUser);
    }
  }
}
