using System;
using System.Threading.Tasks;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Logging;
using System.IO;
using System.Collections.Generic;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Functions.Extensions.Languages;
using System.Linq;
using Dornbracht.Functions.Extensions.Models.Sitemap;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;
using Azure.Storage.Blobs.Models;
using Dornbracht.Api.Pdh.Extensions;

namespace Dornbracht.Api.Pdh.Services
{
  public class SitemapService
  {
    readonly string[] ChannelLanguages = { "de", "en", "en-US", "it", "fr", "es" };

    private IEnumerable<string> ChannelCountries
    {
      get
      {
        var countries = new List<string>();
        foreach (var key in LanguageRepository.LanguageMappingDictionary.Keys)
        {
          var channelKey = key;
          if (channelKey.Contains('-'))
          {
            countries.Add(channelKey.Substring(channelKey.IndexOf('-') + 1).ToUpper());
          }
        }
        countries.Add("XY");
        return countries.Distinct();
      }
    }

    public SitemapService(PdhDataServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
      Domain = Environment.GetEnvironmentVariable("DORNBRACHT_DOMAIN", EnvironmentVariableTarget.Process);
    }

    private string Domain { get; init; }
    private PdhDataServiceClient PdhServiceClient { get; }

    public async Task BuildSitemaps(ILogger log)
    {
      var sitemapChannelData = await GetSitemapChannelData();

      foreach (var country in ChannelCountries)
      {
        log.LogInformation($"Build Sitemap for {country}");

        var sitemapNodes = new List<SitemapNode>();
        foreach (var product in sitemapChannelData)
        {
          if (!product.IsDeleted && product.Countries.Any(c => c == country))
          {
            var urlsByLanguage = FetchUrlsByLanguage(product);
            var urlsByCountry = BuildUrlsByCountry(urlsByLanguage);

            if (urlsByCountry.ContainsKey(country))
            {
              foreach (var countryLangUrl in urlsByCountry[country])
              {
                var alternateUrls = FetchUrlAlternates(product, urlsByCountry, countryLangUrl.Substring(0, countryLangUrl.IndexOf('/')));
                sitemapNodes.Add(new SitemapNode(countryLangUrl, string.Format("{0:yyyy-MM-dd}", DateTime.UtcNow), alternateUrls));
              }

            }
          }
        }

        UploadSitemap(new Sitemap($"ProductSitemap_{country}.xml", sitemapNodes, Domain), log);
      }
    }


    /// <summary>
    /// Collects all product channel data
    /// </summary>
    /// <returns></returns>
    private async Task<IEnumerable<ChannelData>> GetSitemapChannelData()
    {
      //Get all channel data for building all market specific sitemaps
      var channelData = new List<ChannelData>();
      var top = PdhDataServiceClient.DefaultPageSize;
      var channelDataResult = await PdhServiceClient.GetChannelData(ChannelLanguages, top, 0);

      if (channelDataResult is ChannelDataResult.Success channelDataResultSuccess)
      {
        channelData.AddRange(channelDataResultSuccess.Data);

        for (int i = top; i < channelDataResultSuccess.Count; i += top)
        {
          top = Math.Min(top, channelDataResultSuccess.Count - i);

          channelDataResult = await PdhServiceClient.GetChannelData(ChannelLanguages, top, i);
          if (channelDataResult is ChannelDataResult.Success channelDataIterationResultSuccess)
          {
            channelData.AddRange(channelDataIterationResultSuccess.Data);
          }
          else
          {
            break;
          }
        }
      }
      return channelData;
    }

    /// <summary>
    /// Collects all urls by language for given product
    /// i.e. "de", "/bad/luxus-badarmaturen/meta/wannen-einhandbatterie-für-wannenrand--bzw-fliesenrandmontage-29300660-93"
    /// </summary>
    /// <returns></returns>
    private Dictionary<string, string> FetchUrlsByLanguage(ChannelData product)
    {
      var urlsByLanguage = new Dictionary<string, string>();
      foreach (var localizedProductChannelData in product.LocalizedProductChannelData)
      {
        //Get all language specific urls for given product
        var urlProperty = localizedProductChannelData.ChannelProperties.FirstOrDefault(channelProperty => channelProperty.Name == ChannelProperty.UrlPropertyName);
        if (!string.IsNullOrEmpty(urlProperty.Value))
        {
          urlsByLanguage.Add(localizedProductChannelData.Language, $"/{product.ProductNumber}/{urlProperty.Value.TrimStart('/')}");
        }
      }
      return urlsByLanguage;
    }

    /// <summary>
    /// Builds language specific urls by country for given urls by language
    /// i.e. "DE", "de-de/produkte/bad/luxus-badarmaturen/meta/wannen-einhandbatterie-für-wannenrand--bzw-fliesenrandmontage-29300660-93"
    /// i.e. "MX", "es-mx/productos/baño/ducha-premium/ducha-lateral-de-masaje-articulada-sin-regulación-de-caudal-28518979-93"
    /// </summary>
    /// <returns></returns>
    private Dictionary<string, List<string>> BuildUrlsByCountry(Dictionary<string, string> urlsByLanguage)
    {
      var urlsByCountry = new Dictionary<string, List<string>>();
      foreach (var urlByLanguage in urlsByLanguage)
      {
        foreach (var languageKeyValuePair in LanguageRepository.LanguageMappingDictionary)
        {
          if (urlByLanguage.Key.Equals(languageKeyValuePair.Value, StringComparison.InvariantCultureIgnoreCase))
          {
            var urlLanguage = languageKeyValuePair.Key;
            var urlProduct = urlByLanguage.Value.EscapeProductUrl().EnsureProductUrlPrefix(languageKeyValuePair.Value);
            var urlByCountry = urlLanguage + urlProduct;

            if (!languageKeyValuePair.Key.Contains('-'))
            {
              urlsByCountry.Add("XY", new List<string> { urlByCountry });
            }
            else if (!urlsByCountry.ContainsKey(languageKeyValuePair.Key.Substring(languageKeyValuePair.Key.IndexOf('-') + 1).ToUpper()))
            {
              urlsByCountry.Add(languageKeyValuePair.Key.Substring(languageKeyValuePair.Key.IndexOf('-') + 1).ToUpper(), new List<string> { urlByCountry });
            }
            else
            {
              urlsByCountry[languageKeyValuePair.Key.Substring(languageKeyValuePair.Key.IndexOf('-') + 1).ToUpper()].Add(urlByCountry);
            }
          }
        }
      }
      return urlsByCountry;
    }

    /// <summary>
    /// Builds hreflang set for given product in all existing countries in product.Countries
    /// link rel="alternate" hreflang="de-ch" href="https://website-dev.dornbracht.com/de-ch/produkte/tauchrohr-0510104970090"
    /// link rel="alternate" hreflang="x-default" href="https://website-dev.dornbracht.com/en/products/immersion-tube-0510104970090"
    /// link rel="alternate" hreflang="en-be" href="https://website-dev.dornbracht.com/en-be/products/immersion-tube-0510104970090"
    /// </summary>
    /// <returns></returns>
    private Dictionary<string, string> FetchUrlAlternates(ChannelData product, Dictionary<string, List<string>> urlsByCountry, string currentLanguageAndMarket)
    {
      var alternateUrls = new Dictionary<string, string>();
      foreach (var urlByCountryKeyValuePair in urlsByCountry)
      {
        if (product.Countries.Contains(urlByCountryKeyValuePair.Key))
        {
          foreach (var countryLanguageUrl in urlByCountryKeyValuePair.Value)
          {
            var language = countryLanguageUrl.Substring(0, countryLanguageUrl.IndexOf('/'));
            if (language == LanguageRepository.StoryblokDefaultUrlLanguage)
            {
              language = "x-default";
            }

            if (language == currentLanguageAndMarket)
            {
              continue;
            }

            alternateUrls.Add(language, countryLanguageUrl);
          }
        }
      }
      return alternateUrls;
    }

    private void UploadSitemap(Sitemap sitemap, ILogger log)
    {
      var options = new BlobUploadOptions
      {
        HttpHeaders = new BlobHttpHeaders { ContentType = "application/xml" }
      };

      var containerClient = new BlobContainerClient(Environment.GetEnvironmentVariable("SITEMAP_STORAGE_CONNECTION_STRING", EnvironmentVariableTarget.Process), Environment.GetEnvironmentVariable("SITEMAP_STORAGE_CONTAINER_NAME", EnvironmentVariableTarget.Process));

      try
      {
        log.LogInformation($"Uploading sitemap {sitemap.FileName}");
        var client = containerClient.GetBlobClient(sitemap.FileName);

        using (var ms = new MemoryStream())
        {
          sitemap.WriteXmlStream(ms);
          ms.Position = 0;
          client.Upload(ms, options);
        }
      }
      catch (Azure.RequestFailedException failedException)
      {
        log.LogWarning(failedException, "SITEMAP BUILDER: Failed to upload Sitemap " + sitemap.FileName);
      }
    }

  }


}
