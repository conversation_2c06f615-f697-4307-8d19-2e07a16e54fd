using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Pdh.Models.Pdh.Products;

namespace Dornbracht.Api.Pdh.Services
{
  public class PdhDataServiceClient : BasePdhDataServiceClient
  {
    public const int DefaultPageSize = 500;

    public PdhDataServiceClient(IHttpClientFactory httpClientFactory, ILogger log) : base(httpClientFactory, log)
    {
    }

    protected override string BaseUrl => Environment.GetEnvironmentVariable("DORNBRACHT_PDH_DATA_URL", EnvironmentVariableTarget.Process);

    /// <summary>
    /// Returns a specific document.
    /// </summary>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<DocumentResult> GetDocument(string market, string language, string id)
    {
      return await GetDocument(market, language, id, string.Empty);
    }

    public async Task<DocumentResult> GetDocument(string market, string language, string id, string fileName)
    {
      return await BuildRequest<DocumentResult>($"docs/Document({id})", market, language)
      .Map(HttpStatusCode.OK, async (response) =>
      {
        var stream = await response.Content.ReadAsStreamAsync();

        return new DocumentResult.Success { Data = stream, FileName = fileName };
      })
      .Map<DocumentResult.NotFound>(HttpStatusCode.NotFound)
      .Map<DocumentResult.Error>(HttpStatusCode.InternalServerError)
      .ExecuteAsync();
    }

    /// <summary>
    /// Returns channel data (showing in which countries the products are available) for the given languages.
    /// </summary>
    /// <param name="languages">The hub supports: de,en,en-us,es,fr,it,da,nl,no,pl,pt,ru,sv,tr,zh-cn</param>
    /// <param name="top">Top can only be lower than or equal to 500</param>
    /// <param name="skip"></param>
    /// <returns></returns>
    public async Task<ChannelDataResult> GetChannelData(string[] languages, int top = 500, int skip = 0)
    {
      top = Math.Min(500, top);

      var languageParams = string.Join('&', languages.Select(lang => $"languages={lang}"));

      return await BuildRequest<ChannelDataResult>($"/odata/Products/ChannelMarketProducts?$count=true&$top={top}&$skip={skip}&$orderby=id&{languageParams}", "de", "de")
          .MapJson<ChannelDataResult.Success>(HttpStatusCode.OK)
          .ExecuteAsync();
    }

    /// <summary>
    /// Return all valid countries for the given product
    /// </summary>
    /// <param name="productNumber"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <returns></returns>
    public async Task<CountryListResult> GetCountries(string productNumber, string market, string language)
    {
      return await BuildRequest<CountryListResult>(HttpMethod.Get, $"/odata/Products(number='{productNumber}')/Countries", market, language, language)
      .MapJson<CountryListResult.Success>(HttpStatusCode.OK)
      .ExecuteAsync();
    }
  }
}
