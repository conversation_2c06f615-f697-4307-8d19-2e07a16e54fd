using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record AddProductListEntryXtra
  {
    public AddProductListEntryXtra(
      string productNumber,
      string countryCode,
      int xtraOptionId,
      int amount = 1,
      string type = "XtraService",
      bool overrideErrors = false)
    {
      ProductNumber = productNumber;
      Amount = amount;
      CountryCode = countryCode;
      Type = type;
      OverrideErrors = overrideErrors;
      XtraOptionId = xtraOptionId;
    }


    [JsonPropertyName("productNumber")] public string ProductNumber { get; init; }

    [JsonPropertyName("countryCode")] public string CountryCode { get; init; }

    [JsonPropertyName("amount")] public int Amount { get; init; }
    [JsonPropertyName("xtraOptionId")] public int XtraOptionId { get; init; }

    [JsonPropertyName("type")] public string Type { get; init; }
    [JsonPropertyName("overrideErrors")] public bool OverrideErrors { get; init; }
  }
}
