using System.IO;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record ProductListFactSheetResult
  {
    private ProductListFactSheetResult() { }

    public record Success : ProductListFactSheetResult
    {
      public Stream Data { get; init; }
    }

    public record NotFound : ProductListFactSheetResult { }

    public record Forbidden : ProductListFactSheetResult { }

    public record Error : ProductListFactSheetResult { }
  }
}
