using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record AddProductListEntry
  {
    public AddProductListEntry(
      string productNumber,
      string countryCode,
      int amount = 1,
      string type = "Product",
      bool overrideErrors = false)
    {
      ProductNumber = productNumber;
      Amount = amount;
      CountryCode = countryCode;
      Type = type;
      OverrideErrors = overrideErrors;
    }


    [JsonPropertyName("productNumber")] public string ProductNumber { get; init; }

    [JsonPropertyName("countryCode")] public string CountryCode { get; init; }

    [JsonPropertyName("amount")] public int Amount { get; init; }

    [JsonPropertyName("type")] public string Type { get; init; }
    [JsonPropertyName("overrideErrors")] public bool OverrideErrors { get; init; }
  }
}
