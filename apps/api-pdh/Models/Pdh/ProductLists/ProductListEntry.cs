using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record ProductListEntry
  {
    public const string DefaultType = "Product";

    [JsonPropertyName("id")]
    public int Id { get; set; }

    [JsonPropertyName("type")]
    public string Type { get; set; } = DefaultType;

    [JsonPropertyName("productNumber")]
    public string ProductNumber { get; set; }

    [JsonPropertyName("productFormattedNumber")]
    public string ProductFormattedNumber { get; set; }

    [JsonPropertyName("countryCode")]
    public string CountryCode { get; set; }

    [JsonPropertyName("amount")]
    public int Amount { get; set; }

    [JsonPropertyName("order")]
    public double Order { get; set; }

    [JsonPropertyName("productId")]
    public int? ProductId { get; set; }

    [JsonPropertyName("productName")]
    public string ProductName { get; set; }

    [JsonPropertyName("productClass")]
    public int? ProductClass { get; set; }

    [JsonPropertyName("productThumbImageUrl")]
    public string ProductThumbImageUrl { get; set; }

    [JsonPropertyName("productVariantName")]
    public string ProductVariantName { get; set; }

    [JsonPropertyName("productVariantCode")]
    public string ProductVariantCode { get; set; }

    [JsonPropertyName("productBrand")]
    public string ProductBrand { get; set; }

    [JsonPropertyName("productSeries")]
    public string ProductSeries { get; set; }

    [JsonPropertyName("marketName")]
    public string MarketName { get; set; }

    [JsonPropertyName("catalogPriceCurrency")]
    public string CatalogPriceCurrency { get; set; }

    [JsonPropertyName("catalogPrice")]
    public double? CatalogPrice { get; init; }

    [JsonPropertyName("catalogPriceWithVat")]
    public double? CatalogPriceWithVat { get; init; }

    [JsonPropertyName("listId")]
    public int ListId { get; set; }

    [JsonPropertyName("channelProperties")]
    public IEnumerable<ProductListEntryChannelProperty> ChannelProperties { get; set; }

    [JsonPropertyName("children")]
    public IEnumerable<ProductListEntry> Children { get; set; }

    [JsonPropertyName("specialProductionName")]
    public string SpecialProductionName { get; set; }

    [JsonPropertyName("specialProductionDescription")]
    public string SpecialProductionDescription { get; set; }

    [JsonPropertyName("xtraOptionId")]
    public int? XtraOptionId { get; set; }

  }
}
