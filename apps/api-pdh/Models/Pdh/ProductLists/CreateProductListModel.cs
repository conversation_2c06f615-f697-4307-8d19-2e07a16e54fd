using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record CreateProductListModel
  {
    private CreateProductListModel() { }

    public CreateProductListModel(string owner, string name)
    {
      Owner = owner;
      Name = name;
    }

    public const string DefaultEntityName = "product-lists";

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("owner")]
    public string Owner { get; init; }

    [JsonPropertyName("comment")]
    public string Comment { get; init; }

    [JsonPropertyName("entityName")]
    public string EntityName { get; init; } = DefaultEntityName;

  }
}
