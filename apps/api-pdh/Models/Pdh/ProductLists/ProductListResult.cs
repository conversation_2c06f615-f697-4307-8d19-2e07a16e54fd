using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record ProductListResult
  {

    public record ProductList : ProductListResult
    {
      [JsonPropertyName("id")]
      public int Id { get; init; }

      [JsonPropertyName("name")]
      public string Name { get; init; }

      [Json<PERSON>ropertyName("owner")]
      public string Owner { get; init; }

      [JsonPropertyName("comment")]
      public string Comment { get; init; }

      [JsonPropertyName("entityName")]
      public string EntityName { get; init; }

      [JsonPropertyName("entries")]
      public List<ProductListEntry> Entries { get; set; }

      [Json<PERSON>ropertyName("createdAtUtc")]
      public DateTime CreatedAt { get; init; }

      [JsonPropertyName("modifiedAtUtc")]
      public DateTime UpdatedAt { get; init; }

      [JsonPropertyName("entriesCount")]
      public int EntriesCount { get; init; }
    }
    public record NotFound : ProductListResult { }

    public record Forbidden : ProductListResult { }
  }
}
