using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record ProductEntryListResult
  {
    private ProductEntryListResult() { }

    public record Success : ProductEntryListResult
    {
      [JsonPropertyName("value")]
      public ProductListEntry[] Value { get; init; }
    }

    public record NotFound : ProductEntryListResult { }

    public record Forbidden : ProductEntryListResult { }
  }
}
