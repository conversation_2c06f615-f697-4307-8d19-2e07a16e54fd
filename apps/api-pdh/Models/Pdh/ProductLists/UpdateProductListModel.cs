using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record UpdateProductListModel
  {
    private UpdateProductListModel() { }

    public UpdateProductListModel(string owner, int id, string name, string comment)
    {
      Owner = owner;
      Id = id;
      Name = name;
      Comment = comment;
    }

    [JsonPropertyName("id")]
    public int Id { get; init; }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("comment")]
    public string Comment { get; init; }

    [JsonPropertyName("owner")]
    public string Owner { get; init; }
  }
}
