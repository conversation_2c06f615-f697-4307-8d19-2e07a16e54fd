using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record ProductListSearchResult
  {
    private ProductListSearchResult() { }




    public record Success : ProductListSearchResult
    {
      [JsonPropertyName("value")]
      public ProductListResult.ProductList[] Value { get; init; }
    }

    public record Deleted : ProductListSearchResult { }

    public record NotFound : ProductListSearchResult { }

    public record Forbidden : ProductListSearchResult { }
  }
}
