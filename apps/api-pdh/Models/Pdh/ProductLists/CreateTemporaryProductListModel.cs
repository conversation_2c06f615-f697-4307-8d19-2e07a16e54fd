using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record CreateTemporaryProductListModel : CreateProductListModel
  {
    public CreateTemporaryProductListModel(string owner, string name) : base(owner, name)
    {
    }

    public const string UsageTypeTemporary = "Temporary";

    [JsonPropertyName("usageType")]
    public string UsageType { get; set; } = UsageTypeTemporary;
  }
}
