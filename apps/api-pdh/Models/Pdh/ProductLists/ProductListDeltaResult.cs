using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.ProductLists
{
  public record ProductListDeltaResult
  {
    private ProductListDeltaResult() { }

    public record Success : ProductListDeltaResult
    {
      [JsonPropertyName("entriesDelta")]
      public ProductListEntry[] Delta { get; init; }

      [JsonPropertyName("entriesCount")]
      public int Count { get; init; }
    }

    public record NotFound : ProductListDeltaResult { }

    public record Forbidden : ProductListDeltaResult { }

    public record Deleted : ProductListDeltaResult { }

    public record Conflict : ProductListDeltaResult { }
  }
}
