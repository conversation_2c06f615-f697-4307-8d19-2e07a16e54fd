using System.IO;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products
{
  public record DocumentResult
  {
    public record Success : DocumentResult
    {
      public Stream Data { get; init; }

      public string FileName { get; set; }

    }

    public record NotFound() : DocumentResult();

    public record Error() : DocumentResult();

    private DocumentResult() { }
  }
}
