using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products
{
  public record ProductResult
  {
    private ProductResult()
    {
    }

    public record NotFound() : ProductResult
    {
      [JsonPropertyName("type")]
      public string Type { get; init; } = "Nil";

      [JsonPropertyName("message")]
      public string Message { get; init; } = "TBD";

      [JsonPropertyName("title")]
      public string Title { get; init; } = "Product not found";

      [JsonPropertyName("status")]
      public int Status { get; init; } = 404;

      [JsonPropertyName("productNumber")]
      public string ProductNumber { get; init; }
    }

    public record Preview() : ProductResult
    {
      [JsonPropertyName("type")]
      public string Type { get; init; } = "Preview";

      [JsonPropertyName("message")]
      public string Message { get; init; } = "TBD";

      [JsonPropertyName("title")]
      public string Title { get; init; } = "Product not found";

      [JsonPropertyName("status")]
      public int Status { get; init; } = 404;

      [JsonPropertyName("productNumber")]
      public string ProductNumber { get; init; }
    }

    public record Historic() : ProductResult
    {
      [JsonPropertyName("type")]
      public string Type { get; init; } = "Historic";

      [JsonPropertyName("message")]
      public string Message { get; init; } = "TBD";

      [JsonPropertyName("title")]
      public string Title { get; init; } = "Product not found";

      [JsonPropertyName("status")]
      public int Status { get; init; } = 404;

      [JsonPropertyName("productNumber")]
      public string ProductNumber { get; init; }

      [JsonPropertyName("successors")]
      public List<Successor> Successors { get; init; }
    }

    public record MarketUnavailable() : ProductResult
    {
      [JsonPropertyName("type")]
      public string Type { get; init; } = "Market";

      [JsonPropertyName("message")]
      public string Message { get; init; } = "TBD";

      [JsonPropertyName("title")]
      public string Title { get; init; } = "Product not found";

      [JsonPropertyName("status")]
      public int Status { get; init; } = 404;

      [JsonPropertyName("productNumber")]
      public string ProductNumber { get; init; }

      [JsonPropertyName("marketName")]
      public string MarketName { get; init; }
    }

    public record Success() : ProductResult
    {
      [JsonPropertyName("id")] public int Id { get; init; }

      public string Url
      {
        get
        {
          var attribute = Attributes?.FirstOrDefault(prop => prop.Name == ProductAttribute.UrlAttributeName);

          return attribute?.Value ?? string.Empty;
        }
      }

      public string Title
      {
        get
        {
          var attribute = Attributes?.FirstOrDefault(prop => prop.Name == ProductAttribute.TitleAttributeName);

          return attribute?.Value ?? string.Empty;
        }
      }

      public string MetaDescription
      {
        get
        {
          var attribute = Attributes?.FirstOrDefault(prop => prop.Name == ProductAttribute.DescriptionAttributeName);

          return attribute?.Value ?? string.Empty;
        }
      }

      /// <summary>
      /// Currently there are thew following types ( language-independend )
      /// <list type="table">
      /// <listheader>
      /// <term>type</term>
      /// <term>description</term>
      /// </listheader>
      /// <item>
      /// <term>0</term>
      /// <term>regular product</term>
      /// </item>
      /// <item>
      /// <term>1</term>
      /// <term>Set of products ( bundle )</term>
      /// </item>
      /// <item>
      /// <term>2</term>
      /// <term>Special production</term>
      /// </item>
      /// <item>
      /// <term>99</term>
      /// <term>Dummy product</term>
      /// </item>
      /// </list>
      /// </summary>
      [JsonPropertyName("productClass")]
      public int ProductClass { get; init; }

      [JsonPropertyName("productNumber")] public string ProductNumber { get; init; }

      [JsonPropertyName("formattedProductNumber")]
      public string FormattedProductNumber { get; init; }

      [JsonPropertyName("name")] public string Name { get; init; }

      [JsonPropertyName("brand")] public string Brand { get; init; }

      [JsonPropertyName("concept")] public string Series { get; init; }

      [JsonPropertyName("conceptDescription")] public string ConceptDescription { get; init; }

      [JsonPropertyName("variant")] public string VariantName { get; init; }

      [JsonPropertyName("variantCode")] public string VariantCode { get; init; }

      [JsonPropertyName("isDefaultVariant")] public bool IsDefaultVariant { get; init; }

      [JsonPropertyName("images")] public IEnumerable<ProductImage> Images { get; init; }

      [JsonPropertyName("price")] public ProductPrice? Price { get; set; }

      [JsonPropertyName("attributes")] public IEnumerable<ProductAttribute> Attributes { get; init; }
    }

    public record WithDetails() : ProductResult.Success
    {
      private const string VariantRelationType = "Variante";

      private const string AccessoryRelationType = "ZubehoerBenoetigt";

      [JsonPropertyName("texts")] public IEnumerable<ProductText> Texts { get; init; }

      [JsonPropertyName("documents")] public IEnumerable<ProductDocument> Documents { get; init; }

      [JsonPropertyName("relations")] public IEnumerable<ProductRelation> Relations { get; init; }

      [JsonPropertyName("sets")] public IEnumerable<ProductSetEntry> Sets { get; init; }

      [JsonPropertyName("parts")] public IEnumerable<ProductSetEntry> Parts { get; init; }

      [JsonPropertyName("xtraOptions")] public IEnumerable<XtraOption> XtraOptions { get; init; }

      public IEnumerable<ProductResult.Success> Variants
      {
        get { return GetRelationsForType(VariantRelationType); }
      }

      public IEnumerable<ProductResult.Success> Accessories
      {
        get { return GetRelationsForType(AccessoryRelationType); }
      }

      private IEnumerable<ProductResult.Success> GetRelationsForType(string type)
      {
        return Relations?.FirstOrDefault(rel => rel.Type == type)?.Alternatives.SelectMany(alt => alt.Related)
          .Select(related => related.Target) ?? Array.Empty<ProductResult.Success>();
      }
    }
  }

  public record Successor
  {
    [JsonPropertyName("number")]
    public string Number { get; init; }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("compatible")]
    public bool Compatible { get; init; }

    [JsonPropertyName("slug")]
    public string Slug { get; init; }
  }
}
