using System.Reflection.Emit;
using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products
{
  public record CountryListResult
  {

    private CountryListResult()
    {

    }

    public record Success() : CountryListResult
    {
      [JsonPropertyName("value")]
      public Country[] Countries { get; init; }
    }
  }
}
