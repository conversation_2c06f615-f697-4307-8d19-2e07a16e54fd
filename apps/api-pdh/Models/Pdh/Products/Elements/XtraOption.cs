using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

public record struct XtraOption
{
  [JsonPropertyName("id")]
  public int? Id { get; init; }
  [JsonPropertyName("number")]
  public string Number { get; init; }
  [JsonPropertyName("configCode")]
  public string ConfigCode { get; init; }
  [JsonPropertyName("category")]
  public string Category { get; init; }
  [JsonPropertyName("categoryCode")]
  public string CategoryCode { get; init; }
  [JsonPropertyName("dimension")]
  public string Dimension { get; init; }
  [JsonPropertyName("dimensionCode")]
  public string DimensionCode { get; init; }
  [JsonPropertyName("configDimension")]
  public string ConfigDimension { get; init; }
  [JsonPropertyName("name")]
  public string Name { get; init; }
  [JsonPropertyName("configName")]
  public string ConfigName { get; init; }
  [JsonPropertyName("leadTime")]
  public int? LeadTime { get; init; }
  [Json<PERSON>ropertyName("imageUrl")]
  public string ImageUrl { get; init; }
  [JsonPropertyName("agioWithoutVat")]
  public XtraOptionPrice? AgioWithoutVat { get; set; }
  [JsonPropertyName("agioWithVat")]
  public XtraOptionPrice? AgioWithVat { get; set; }
  [JsonPropertyName("priceWithoutVat")]
  public XtraOptionPrice? PriceWithoutVat { get; set; }
  [JsonPropertyName("totalWithVat")]
  public XtraOptionPrice? TotalWithVat { get; set; }
  [JsonPropertyName("totalWithoutVat")]
  public XtraOptionPrice? TotalWithoutVat { get; set; }
  [JsonPropertyName("priceWithVat")]
  public XtraOptionPrice? PriceWithVat { get; set; }
  [JsonPropertyName("products")]
  public IEnumerable<XtraOptionProducts> Products { get; init; }
}
