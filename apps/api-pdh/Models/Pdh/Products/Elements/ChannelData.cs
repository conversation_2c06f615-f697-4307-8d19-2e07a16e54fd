using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products
{
  public record ChannelData
  {

    [JsonPropertyName("id")]
    public int Id { get; init; }

    [JsonPropertyName("number")]
    public string ProductNumber { get; init; }

    [JsonPropertyName("baseNumber")]
    public string BaseProductNumber { get; init; }

    [JsonPropertyName("isDefault")]
    public bool IsDefault { get; init; }

    [JsonPropertyName("isDeleted")]
    public bool IsDeleted { get; init; }

    [JsonPropertyName("countries")]
    public string[] Countries { get; init; }

    [JsonPropertyName("localizedProductChannelData")]
    public IEnumerable<LocalizedProductChannelData> LocalizedProductChannelData { get; init; }
  }
}
