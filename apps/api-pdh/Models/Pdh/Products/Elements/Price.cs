using System;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products.Elements
{
  public record struct ProductPrice
  {
    [JsonPropertyName("withoutVat")]
    public double? Value { get; init; }

    [JsonPropertyName("withVat")]
    public double? ValueWithVat { get; init; }

    [JsonPropertyName("currency")]
    public string Currency { get; init; }

    [JsonPropertyName("validFrom")]
    public DateTime? ValidFrom { get; init; }
  }
}
