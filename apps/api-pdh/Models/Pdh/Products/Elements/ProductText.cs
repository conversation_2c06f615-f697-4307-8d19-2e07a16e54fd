using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products.Elements
{
  public record struct ProductText
  {
    [JsonPropertyName("text")]
    public string Text { get; init; }

    [JsonPropertyName("order")]
    public int Order { get; init; }

    [JsonPropertyName("isBold")]
    public bool IsBold { get; init; }

    [JsonPropertyName("isHeader")]
    public bool IsHeader { get; init; }

    [JsonPropertyName("isNote")]
    public bool IsNote { get; init; }
  }
}
