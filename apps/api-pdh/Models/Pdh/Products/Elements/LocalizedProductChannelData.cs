using System.Collections.Generic;
using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products
{
  public record LocalizedProductChannelData
  {
    [JsonPropertyName("language")]
    public string Language { get; init; }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("channelProperties")]
    public IEnumerable<ChannelProperty> ChannelProperties { get; init; }
  }
}
