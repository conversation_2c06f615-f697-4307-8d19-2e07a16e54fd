using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products.Elements
{
  public record struct ChannelProperty
  {
    public const string UrlPropertyName = "URLPDPWebsiteSlug";
    public const string TitlePropertyName = "TitleWebsite";
    public const string DescriptionPropertyName = "MetaDescriptionWebsite";

    [JsonPropertyName("value")]
    public string Value { get; init; }

    [JsonPropertyName("name")]
    public string Name { get; init; }
  }
}
