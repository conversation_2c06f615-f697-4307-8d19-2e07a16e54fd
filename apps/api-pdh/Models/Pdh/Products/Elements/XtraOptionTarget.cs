using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

public record struct XtraOptionTarget
{
  [JsonPropertyName("attributes")]
  public IEnumerable<ProductAttribute> Attributes { get; init; }
  [JsonPropertyName("isInCurrentProgram")]
  public bool? IsInCurrentProgram { get; init; }
  [JsonPropertyName("id")]
  public int? Id { get; init; }
  [JsonPropertyName("baseNumber")]
  public string BaseNumber { get; init; }
  [JsonPropertyName("productNumber")]
  public string ProductNumber { get; init; }
  [JsonPropertyName("formattedProductNumber")]
  public string FormattedProductNumber { get; init; }
  [JsonPropertyName("productClass")]
  public int? ProductClass { get; init; }
  [JsonPropertyName("name")]
  public string Name { get; init; }
  [JsonPropertyName("variant")]
  public string Variant { get; init; }
  [JsonPropertyName("variantCode")]
  public string VariantCode { get; init; }
  [JsonPropertyName("isDefaultVariant")]
  public bool? IsDefaultVariant { get; init; }
  [JsonPropertyName("deliveryDateControlType")]
  public string DeliveryDateControlType { get; init; }
  [JsonPropertyName("entityName")]
  public string EntityName { get; init; }
  [JsonPropertyName("images")]
  public IEnumerable<ProductImage> Images { get; init; }
  [JsonPropertyName("deliveryCategory")]
  public XtraOptionDeliveryCategory? DeliveryCategory { get; init; }
  [JsonPropertyName("price")]
  public ProductPrice? Price { get; set; }
}
