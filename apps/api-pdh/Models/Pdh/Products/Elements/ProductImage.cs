using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products.Elements
{

  public record struct ProductImage
  {
    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("contentType")]
    public string ContentType { get; init; }

    [JsonPropertyName("webFull")]
    public ProductImageFormat? Full { get; init; }

    [JsonPropertyName("webMedium")]
    public ProductImageFormat? Medium { get; init; }

    [JsonPropertyName("webThumb")]
    public ProductImageFormat? Thumb { get; init; }
  }
}
