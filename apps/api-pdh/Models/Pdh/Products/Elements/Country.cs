using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products.Elements
{
  public record struct Country
  {
    [JsonPropertyName("isoCode")]
    public string IsoCode { get; init; }

    [JsonPropertyName("displayName")]
    public string DisplayName { get; init; }

    [JsonPropertyName("currencyCode")]
    public string CurrencyCode { get; init; }

    [JsonPropertyName("currencySymbol")]
    public string CurrencySymbol { get; init; }
  }
}
