using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products.Elements
{
  public record struct ProductAttribute
  {
    public const string UrlAttributeName = "URLPDPWebsiteSlug";
    public const string TitleAttributeName = "TitleWebsite";
    public const string DescriptionAttributeName = "MetaDescriptionWebsite";

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("caption")]
    public string Caption { get; init; }

    [JsonPropertyName("group")]
    public string Group { get; init; }

    [JsonPropertyName("tab")]
    public string Tab { get; init; }

    [JsonPropertyName("value")]
    public string Value { get; init; }

    [JsonPropertyName("order")]
    public int Order { get; init; }
  }
}
