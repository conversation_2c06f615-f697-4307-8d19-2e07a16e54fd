using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products.Elements
{
  public record ProductRelation
  {
    /// <summary>
    /// Atm the following types are known: ( language-independend )
    /// <list>
    /// <item>
    /// <term>
    /// Beziehungen
    /// </term>
    /// <description>
    /// no idea
    /// </description>
    /// </item>
    /// <item>
    /// <term>
    /// Passendes Zubehör
    /// </term>
    /// <description>
    /// cross-selling
    /// </description>
    /// </item>
    /// <item>
    /// <term>
    /// ZubehoerEmpfohlenPrint
    /// </term>
    /// <description>
    /// optional accessory
    /// </description>
    /// </item>
    /// <item>
    /// <term>
    /// Produktvariante
    /// </term>
    /// <description>
    /// somewhat similar product
    /// </description>
    /// </item>
    /// <item>
    /// <term>
    /// Variante
    /// </term>
    /// <description>
    /// different variants of the same base-product
    /// </description>
    /// </item>
    /// <item>
    /// <term>
    /// ZubehoerBenoetigt
    /// </term>
    /// <description>
    /// mandatory accessory
    /// </description>
    /// </item>
    /// <item>
    /// <term>
    /// ZubehoerBenoetigtVon
    /// </term>
    /// <description>
    /// mandatory accessory of another product
    /// </description>
    /// </item>
    /// <item>
    /// <term>
    /// Attrappe
    /// </term>
    /// <description>
    /// dummy
    /// </description>
    /// </item>
    /// <item>
    /// <term>
    /// Acrylmuster
    /// </term>
    /// <description>
    /// sample displayed on a plate of arycl
    /// </description>
    /// </item>
    /// <item>
    /// <term>
    /// Anwendungskomponente
    /// </term>
    /// <description>
    /// parts of the application example ( set )
    /// </description>
    /// </item>
    /// </list>
    /// </summary>
    [JsonPropertyName("type")]
    public string Type { get; init; }

    [JsonPropertyName("alternatives")]
    public IEnumerable<ProductAlternative> Alternatives { get; init; }
  }
}
