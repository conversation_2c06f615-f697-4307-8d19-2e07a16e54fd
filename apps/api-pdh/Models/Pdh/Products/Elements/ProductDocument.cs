using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Products.Elements
{
  public record ProductDocument
  {
    [JsonPropertyName("docId")]
    public string Id { get; init; }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("type")]
    public string Type { get; init; }

    [JsonPropertyName("typeName")]
    public string TypeName { get; init; }

    [JsonPropertyName("fileType")]
    public string FileType { get; init; }

    [JsonPropertyName("validFrom")]
    public DateTime? ValidFrom { get; init; }

    [JsonPropertyName("validTo")]
    public DateTime? ValidTo { get; init; }

    [JsonPropertyName("size")]
    public long? Size { get; set; }
  }
}
