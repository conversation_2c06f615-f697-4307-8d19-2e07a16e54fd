using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles.Elements;
using Dornbracht.Api.Pdh.Models.Pdh.Documents;

namespace Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles
{
  public record DocumentBundleAddConceptRequest()
  {
    [JsonPropertyName("recipientEMail")]
    public string RecipientEMail { get; init; }

    [JsonPropertyName("definition")]
    public Definition Definition { get; init; }

    [JsonPropertyName("scope")]
    public ConceptScope Scope { get; init; }
  }
}

