using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles.Elements;

namespace Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles
{
  public record DocumentBundleListEntryResult
  {
    private DocumentBundleListEntryResult() { }

    public record NotFound() : DocumentBundleListEntryResult { }

    public record Error() : DocumentBundleListEntryResult { }

    public record Success() : DocumentBundleListEntryResult
    {
      [JsonPropertyName("id")]
      public int Id { get; init; }

      [JsonPropertyName("key")]
      public string Key { get; init; }

      [JsonPropertyName("generatedAtUtc")]
      public string GeneratedAtUtc { get; init; }

      [JsonPropertyName("status")]
      public string Status { get; init; }

      [JsonPropertyName("size")]
      public long? Size { get; init; }

      [JsonPropertyName("definition")]
      public Definition Definition { get; init; }

      [JsonPropertyName("scope")]
      public GenericScope Scope { get; init; }

    }
  }
}
