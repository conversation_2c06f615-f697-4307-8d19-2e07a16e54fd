using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles
{
  public record DocumentBundleListResult
  {
    private DocumentBundleListResult() { }

    public record NotFound() : DocumentBundleListResult { }

    public record Success() : DocumentBundleListResult
    {
      [JsonPropertyName("value")]
      public DocumentBundleListEntryResult.Success[] Value { get; init; }
    }
  }
}
