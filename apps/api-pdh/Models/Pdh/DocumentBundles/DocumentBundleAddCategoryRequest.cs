using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles.Elements;

namespace Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles
{
  public record DocumentBundleAddCategoryRequest()
  {
    [JsonPropertyName("recipientEMail")]
    public string RecipientEMail { get; init; }

    [JsonPropertyName("definition")]
    public Definition Definition { get; init; }

    [JsonPropertyName("scope")]
    public CategoryScope Scope { get; init; }
  }
}

