using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh
{
  public class MailjetMessage
  {
    [JsonPropertyName("From")]
    public MailjetAddress from { get; set; }
    [JsonPropertyName("To")]
    public MailjetAddress[] to { get; set; }
    [JsonPropertyName("TemplateLanguage")]
    public bool templateLanguage {get; set;}
    [JsonPropertyName("TemplateID")]
    public int templateId { get; set; }
    [JsonPropertyName("Variables")]
    public Dictionary<string, string> variables {get; set;}
  }
}
