using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search
{
  public record Facet
  {
    /// <summary>
    /// Unique name of the facet.
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("unit")]
    public string Unit { get; init; }

    /// <summary>
    /// Possible values for the facet filter. (These values may have children.)
    /// </summary>
    [JsonPropertyName("terms")]
    public FacetTerm[] Terms { get; init; }
  }
}
