using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search.DocumentBundle
{
 public record struct SearchDocumentBundle
  {
    [JsonPropertyName("key")]
    public string Key { get; init; }

    [JsonPropertyName("imageUrl")]
    public string ImageUrl { get; init; }

    [JsonPropertyName("bundleKey")]
    public string BundleKey { get; init; }

    [JsonPropertyName("productConcept")]
    public KeyNameMapping? ProductConcept { get; init; }

    [JsonPropertyName("productCategory")]
    public KeyNameMapping? ProductCategory { get; init; }

    [JsonPropertyName("bundle")]
    public KeyNameMapping Bundle { get; init; }

    [JsonPropertyName("score")]
    public double? Score { get; init; }

  }
}
