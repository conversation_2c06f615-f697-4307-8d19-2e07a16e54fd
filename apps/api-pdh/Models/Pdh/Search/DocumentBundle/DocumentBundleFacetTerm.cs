using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search.DocumentBundle
{
 public record struct DocumentBundleFacetTerm
  {
    [JsonPropertyName("label")]
    public string Label { get; init; }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("value")]
    public string Value { get; init; }

    [JsonPropertyName("count")]
    public int Count { get; init; }

    [JsonPropertyName("children")]
    public DocumentBundleFacetTerm[] Children { get; init; }
  }
}
