using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search.DocumentBundle
{
  public record DocumentBundleSearchResult
  {
    public record Success : DocumentBundleSearchResult
    {
      [JsonPropertyName("page")]
      public int Page { get; init; }

      [JsonPropertyName("totalPages")]
      public int TotalPages { get; init; }

      [JsonPropertyName("totalCount")]
      public int TotalCount { get; init; }

      [JsonPropertyName("results")]
      public SearchDocumentBundle[] Results { get; init; }

      [JsonPropertyName("facets")]
      public Dictionary<string, DocumentBundleFacet> Facets { get; init; }
    }

    public record NotFound : DocumentBundleSearchResult { }

    public record Error : DocumentBundleSearchResult { }

    private DocumentBundleSearchResult() { }
  }
}
