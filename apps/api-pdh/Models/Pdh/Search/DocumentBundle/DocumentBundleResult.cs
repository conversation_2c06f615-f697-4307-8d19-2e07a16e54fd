using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search.DocumentBundle
{
  public record DocumentBundleResult
  {
    public record Success : DocumentBundleResult
    {
      [JsonPropertyName("hitLists")]
      public DocumentBundleHitList[] HitLists { get; init; }

      [JsonPropertyName("facets")]
      public Dictionary<string, DocumentBundleFacet> Facets { get; init; }
    }

    public record Error() : DocumentBundleResult();

    private DocumentBundleResult() { }
  }
}
