using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search
{
  public record struct DocumentBundleFacetFilter
  {
    /// <summary>
    /// Name of the filter. See keys of <see cref="SearchResult.Success.Facets" />
    /// </summary>
    [JsonPropertyName("name")]
    [Required]
    public string Name { get; init; }

    /// <summary>
    /// Values of the selected filter terms. See <see cref="Facet.Terms" />
    /// </summary>
    [JsonPropertyName("values")]
    public string[] Values { get; init; }

    /// <summary>
    /// True, if the values need translation (from DE value), false otherwise.
    /// </summary>
    [JsonPropertyName("isLanguageNeutral")]
    [DefaultValue(false)]
    public bool IsLanguageNeutral { get; init; }
  }
}
