using System.ComponentModel;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search
{
  /// <summary>
  /// Use the Range filter property instead of the Values array for ranged filters like price.
  /// </summary>
  public record struct FacetRange
  {
    [JsonPropertyName("from")]
    public int? From { get; init; }

    [JsonPropertyName("fromInclusive")]
    [DefaultValue(true)]
    public bool FromInclusive { get; init; }

    [JsonPropertyName("to")]
    public int? To { get; init; }

    [JsonPropertyName("toInclusive")]
    [DefaultValue(false)]
    public bool ToInclusive { get; init; }
  }
}
