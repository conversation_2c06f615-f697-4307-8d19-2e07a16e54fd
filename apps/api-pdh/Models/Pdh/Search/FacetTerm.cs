using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Converter;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search
{
  [KnownType(typeof(BoolFacetTerm))]
  [KnownType(typeof(StringFacetTerm))]
  [KnownType(typeof(NumberFacetTerm))]
  [JsonConverter(typeof(FacetTermConverter))]
  public abstract class FacetTerm
  {
    /// <summary>
    /// Unique name of the filter term.
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; init; }

    /// <summary>
    /// Language specific label of the filter term.
    /// </summary>
    [JsonPropertyName("label")]
    public string Label { get; init; }

    /// <summary>
    /// Number of results that are referencing this facet term.
    /// </summary>
    [JsonPropertyName("count")]
    public int Count { get; init; }

    /// <summary>
    /// Child terms
    /// </summary>
    [JsonPropertyName("children")]
    public IEnumerable<FacetTerm> Children { get; init; } = new FacetTerm[0];
  }
}
