using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search
{
  public record SearchResult
  {
    public record Success : SearchResult
    {
      [JsonPropertyName("page")]
      public int Page { get; init; }

      [JsonPropertyName("totalPages")]
      public int TotalPages { get; init; }

      [JsonPropertyName("totalCount")]
      public int TotalCount { get; init; }

      [JsonPropertyName("results")]
      public SearchDocument[] Results { get; init; }

      [JsonPropertyName("facets")]
      public Dictionary<string, Facet> Facets { get; init; }
    }

    public record NotFound : SearchResult { }

    public record Error : SearchResult { }

    private SearchResult() { }
  }
}
