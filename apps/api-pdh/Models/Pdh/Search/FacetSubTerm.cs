using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search
{
  public class FacetSubTerm
  {
    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("label")]
    public string Label { get; init; }

    [JsonPropertyName("value")]
    public FacetTerm Value { get; init; }

    [JsonPropertyName("count")]
    public int Count { get; init; }
  }
}
