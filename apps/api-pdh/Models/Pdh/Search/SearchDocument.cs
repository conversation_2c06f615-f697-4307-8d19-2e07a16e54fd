using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search
{
  public record struct SearchDocument
  {
    [JsonPropertyName("key")]
    public string Key { get; init; }

    [JsonPropertyName("language")]
    public string Language { get; init; }

    [JsonPropertyName("productNumber")]
    public string ProductNumber { get; init; }

    [JsonPropertyName("slug")]
    public string Url { get; init; }

    [JsonPropertyName("metaDescription")]
    public string Description { get; init; }

    [JsonPropertyName("productFormattedNumber")]
    public string FormattedProductNumber { get; init; }

    [JsonPropertyName("productName")]
    public string Name { get; init; }

    [JsonPropertyName("concepts")]
    public string[] Concepts { get; init; }

    [JsonPropertyName("productImageUrl")]
    public string ImageUrl { get; init; }

    [JsonPropertyName("priceWithoutVat")]
    public double? Price { get; set; }

    [JsonPropertyName("priceWithVat")]
    public double? PriceWithVat { get; set; }

    [JsonPropertyName("priceCurrency")]
    public string Currency { get; set; }

    [JsonPropertyName("score")]
    public double Score { get; init; }

    [JsonPropertyName("variantName")]
    public string VariantName { get; set; }

  }
}
