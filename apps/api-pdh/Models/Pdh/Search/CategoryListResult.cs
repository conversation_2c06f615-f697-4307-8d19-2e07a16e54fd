using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Pdh.Search
{
  public record CategoryListResult
  {
    public record Success : CategoryListResult
    {
      [JsonPropertyName("hitLists")]
      public HitList[] HitLists { get; init; }

      [JsonPropertyName("facets")]
      public Dictionary<string, Facet> Facets { get; init; }
    }

    public record Error() : CategoryListResult();

    private CategoryListResult() { }
  }
}
