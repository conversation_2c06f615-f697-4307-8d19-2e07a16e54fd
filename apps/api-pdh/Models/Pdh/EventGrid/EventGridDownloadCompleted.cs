using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.EventGrid
{
  public class EventGridDownloadCompleted
  {
    [JsonPropertyName("RecipientLanguage")]
    public string RecipientLanguage { get; set; }
    [JsonPropertyName("RecipientEMail")]
    public string RecipientEMail { get; set; }
    [JsonPropertyName("ActivityStatus")]
    public string ActivityStatus { get; set; }

    [JsonIgnore]
    public bool IsSucceeded => ActivityStatus.Equals("Succeeded");
  }
}
