using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Requests
{
  public record AddDownloadToBundleListRequest
  {
    [JsonPropertyName("definitionName")]
    [Required]
    public string DefinitionName { get; init; }

    [JsonPropertyName("scopeValue")]
    [Required]
    public string ScopeValue { get; init; }

    [JsonPropertyName("scope")]
    [Required]
    public string Scope { get; init; }

  }
}
