using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Requests
{
  public record TransferUserDataRequest
  {
    /// <summary>
    /// Current owner of the objects.
    /// </summary>
    [JsonPropertyName("from")]
    [Required]
    public string From { get; init; }

    /// <summary>
    /// New owner of the objects.
    /// </summary>
    [JsonPropertyName("to")]
    [Required]
    public string To { get; init; }
  }
}
