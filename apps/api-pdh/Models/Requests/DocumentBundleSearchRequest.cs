using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Requests
{
  public record DocumentBundleSearchRequest
  {
    /// <summary>
    /// Use * to match all products.
    /// </summary>
    [JsonPropertyName("text")]
    [Required]
    [DefaultValue("*")]
    [MinLength(1)]
    public string Text { get; init; }

    [JsonPropertyName("page")]
    [Required]
    [DefaultValue(0)]
    [Range(0, int.MaxValue)]
    public int Page { get; init; }

    [JsonPropertyName("filters")]
    public DocumentBundleFacetFilterRequest[] Filters { get; init; }

    /// <summary>
    /// True, if the filter values need translation (in case of a predefined filter on first page load from DE value or name), false otherwise.
    /// </summary>
    [JsonPropertyName("isLanguageNeutral")]
    [DefaultValue(false)]
    public bool IsLanguageNeutral { get; init; }

    /// <summary>
    /// Defines the ordering of the returned hitlist. Use Field asc|desc - syntax ( odata like ). Separate multiple fields by comma. Default is asc.
    /// If a Field is not applicable to a source the field will be skipped in the sort order. Defaults to whatever is configured for the search category.
    /// If "standard" is specified the results will be sorted according to their product-type- and surface-ranking.
    /// Use "ranking" for score based ordering. Note that no score-based ranking is applied on fuzzy search, wildcard or regex queries, or an empty search ( aka *-search ).
    /// </summary>
    [JsonPropertyName("order")]
    [DefaultValue("standard")]
    public string Order { get; init; }
  }
}
