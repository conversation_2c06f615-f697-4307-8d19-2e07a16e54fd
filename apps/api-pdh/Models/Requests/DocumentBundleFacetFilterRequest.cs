using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Search;

namespace Dornbracht.Api.Pdh.Models.Requests
{
  public record struct DocumentBundleFacetFilterRequest
  {
    /// <summary>
    /// Name of the filter. See names of <see cref="SearchResult.Success.Facets" />
    /// </summary>
    [JsonPropertyName("name")]
    [Required]
    public string Name { get; init; }

    /// <summary>
    /// Values of the selected filter terms (language specific). See <see cref="Facet.Terms" />. Multiple value are combined with logical OR.
    ///
    /// Facets should be technically selected on the website via (unique) name. The value send to the API is the language specific value.
    /// Exception: Initial load with predefined filter. In this case the unique name should be used and also set <see cref="ProductSearchRequest.IsLanguageNeutral"/> to true.
    ///
    /// For range filters use the range property instead.
    /// </summary>
    [JsonPropertyName("values")]
    public string[] Values { get; init; }
  }
}
