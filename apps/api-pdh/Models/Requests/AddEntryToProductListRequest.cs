using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Requests
{
  public record AddEntryToProductListRequest
  {
    private const int DefaultAmount = 1;

    /// <summary>
    /// Only non-formatted product numbers are supported.
    /// </summary>
    [JsonPropertyName("productNumber")]
    [Required]
    public string ProductNumber { get; init; }

    [JsonPropertyName("amount")]
    [Range(1, int.MaxValue)]
    [DefaultValue(DefaultAmount)]
    public int Amount { get; init; } = DefaultAmount;

    [JsonPropertyName("xtraOptionId")]
    public int? XtraOptionId { get; init; } = null;
  }
}
