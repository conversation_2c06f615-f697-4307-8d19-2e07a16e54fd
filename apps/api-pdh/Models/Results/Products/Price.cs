using System;
using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

namespace Dornbracht.Api.Pdh.Models.Results.Products
{
  public record struct Price
  {

    public Price(ProductPrice apiResult)
    {
      Value = apiResult.Value;
      ValueWithVat = apiResult.ValueWithVat;
      Currency = apiResult.Currency;
    }

    [JsonPropertyName("price")]
    public double? Value { get; init; }

    [JsonPropertyName("priceWithVat")]
    public double? ValueWithVat { get; init; }

    [JsonPropertyName("currency")]
    public string Currency { get; init; }
  }
}
