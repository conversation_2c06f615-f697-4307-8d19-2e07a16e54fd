using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

namespace Dornbracht.Api.Pdh.Models.Results.Products
{

  public record struct ImageFormat
  {

    public ImageFormat(ProductImageFormat apiResult)
    {
      Url = apiResult.Url;
      Height = apiResult.Height;
      Width = apiResult.Width;
    }

    [JsonPropertyName("url")]
    public string Url { get; init; }

    [JsonPropertyName("height")]
    public int Height { get; init; }

    [JsonPropertyName("width")]
    public int Width { get; set; }
  }
}
