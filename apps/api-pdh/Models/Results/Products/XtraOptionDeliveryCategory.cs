using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Results.Products;

public record XtraOptionDeliveryCategory
{
  public XtraOptionDeliveryCategory(Pdh.Products.Elements.XtraOptionDeliveryCategory apiResult)
  {
    Code = apiResult.Code;
    Days = apiResult.Days;
  }
  [JsonPropertyName("code")]
  public string Code { get; init; }
  [JsonPropertyName("days")]
  public int? Days { get; init; }
}
