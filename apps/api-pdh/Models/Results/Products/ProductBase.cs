using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products;

namespace Dornbracht.Api.Pdh.Models.Results.Products
{
  public record ProductBase
  {
    public ProductBase(ProductResult.Success apiResult)
    {
      Id = apiResult.Id;
      Url = apiResult.Url;
      ConceptDescription = apiResult.ConceptDescription;
      Title = apiResult.Title;
      MetaDescription = apiResult.MetaDescription;
      ProductNumber = apiResult.ProductNumber;
      FormattedProductNumber = apiResult.FormattedProductNumber;
      Name = apiResult.Name;
      Brand = apiResult.Brand;
      Concept = apiResult.Series;
      VariantName = apiResult.VariantName;
      VariantCode = apiResult.VariantCode;
      IsDefaultVariant = apiResult.IsDefaultVariant;
      Images = apiResult.Images?.Select(image => new Image(image));
      Price = apiResult.Price.HasValue ? new Price(apiResult.Price.Value) : null;
    }

    [JsonPropertyName("id")] public int Id { get; init; }

    [JsonPropertyName("url")] public string Url { get; init; }

    [JsonPropertyName("conceptDescription")] public string ConceptDescription { get; init; }

    [JsonPropertyName("title")] public string Title { get; init; }

    [JsonPropertyName("metaDescription")] public string MetaDescription { get; init; }

    [JsonPropertyName("productNumber")] public string ProductNumber { get; init; }

    [JsonPropertyName("formattedNumber")] public string FormattedProductNumber { get; init; }

    [JsonPropertyName("name")] public string Name { get; init; }

    [JsonPropertyName("brand")] public string Brand { get; init; }

    [JsonPropertyName("concept")] public string Concept { get; init; }

    [JsonPropertyName("variant")] public string VariantName { get; init; }

    [JsonPropertyName("variantCode")] public string VariantCode { get; init; }

    [JsonPropertyName("isDefaultVariant")] public bool IsDefaultVariant { get; init; }

    [JsonPropertyName("images")] public IEnumerable<Image> Images { get; init; }

    [JsonPropertyName("price")] public Price? Price { get; set; }
  }
}
