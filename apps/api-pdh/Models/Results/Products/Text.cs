using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

namespace Dornbracht.Api.Pdh.Models.Results.Products
{
  public record struct Text
  {

    public Text(ProductText apiResult)
    {
      Value = apiResult.Text;
      IsBold = apiResult.IsBold;
      IsHeader = apiResult.IsHeader;
      IsNote = apiResult.IsNote;
    }

    [JsonPropertyName("text")]
    public string Value { get; init; }

    [JsonPropertyName("isBold")]
    public bool IsBold { get; init; }

    [JsonPropertyName("isHeader")]
    public bool IsHeader { get; init; }

    [JsonPropertyName("isNote")]
    public bool IsNote { get; init; }
  }
}
