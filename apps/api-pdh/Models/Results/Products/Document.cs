using System;
using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

namespace Dornbracht.Api.Pdh.Models.Results.Products
{
  public record struct Document
  {

    public Document(ProductDocument apiResult)
    {
      Id = apiResult.Id;
      Name = apiResult.Name;
      Type = apiResult.Type;
      TypeName = apiResult.TypeName;
      FileType = apiResult.FileType;
      ValidFrom = apiResult.ValidFrom;
      ValidTo = apiResult.ValidTo;
      Size = apiResult.Size;
    }

    [JsonPropertyName("docId")]
    public string Id { get; init; }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("type")]
    public string Type { get; init; }

    [JsonPropertyName("typeName")]
    public string TypeName { get; init; }

    [JsonPropertyName("fileType")]
    public string FileType { get; init; }

    [JsonPropertyName("validFrom")]
    public DateTime? ValidFrom { get; init; }

    [JsonPropertyName("validTo")]
    public DateTime? ValidTo { get; init; }

    [JsonPropertyName("size")]
    public long? Size { get; set; }

  }
}
