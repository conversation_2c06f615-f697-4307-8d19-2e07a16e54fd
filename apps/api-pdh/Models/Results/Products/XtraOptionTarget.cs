using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Results.Products;

public record XtraOptionTarget
{
  public XtraOptionTarget(Pdh.Products.Elements.XtraOptionTarget apiResult)
  {
    Id = apiResult.Id;
    Attributes = apiResult.Attributes?.Select(a => new Attribute(a)) ?? Array.Empty<Attribute>();
    IsInCurrentProgram = apiResult.IsInCurrentProgram;
    BaseNumber = apiResult.BaseNumber;
    ProductNumber = apiResult.ProductNumber;
    FormattedProductNumber = apiResult.FormattedProductNumber;
    ProductClass = apiResult.ProductClass;
    Name = apiResult.Name;
    Variant = apiResult.Variant;
    VariantCode = apiResult.VariantCode;
    IsDefaultVariant = apiResult.IsDefaultVariant;
    DeliveryDateControlType = apiResult.DeliveryDateControlType;
    EntityName = apiResult.EntityName;
    Price = apiResult.Price.HasValue ? new Price(apiResult.Price.Value) : null;
    Images = apiResult.Images?.Select(a => new Image(a)) ?? Array.Empty<Image>();
    DeliveryCategory = apiResult.DeliveryCategory.HasValue ? new XtraOptionDeliveryCategory(apiResult.DeliveryCategory.Value) : null;
  }

  [JsonPropertyName("attributes")]
  public IEnumerable<Attribute> Attributes { get; init; }
  [JsonPropertyName("isInCurrentProgram")]
  public bool? IsInCurrentProgram { get; init; }
  [JsonPropertyName("id")]
  public int? Id { get; init; }
  [JsonPropertyName("baseNumber")]
  public string BaseNumber { get; init; }
  [JsonPropertyName("productNumber")]
  public string ProductNumber { get; init; }
  [JsonPropertyName("formattedProductNumber")]
  public string FormattedProductNumber { get; init; }
  [JsonPropertyName("productClass")]
  public int? ProductClass { get; init; }
  [JsonPropertyName("name")]
  public string Name { get; init; }
  [JsonPropertyName("variant")]
  public string Variant { get; init; }
  [JsonPropertyName("variantCode")]
  public string VariantCode { get; init; }
  [JsonPropertyName("isDefaultVariant")]
  public bool? IsDefaultVariant { get; init; }
  [JsonPropertyName("deliveryDateControlType")]
  public string DeliveryDateControlType { get; init; }
  [JsonPropertyName("entityName")]
  public string EntityName { get; init; }
  [JsonPropertyName("images")]
  public IEnumerable<Image> Images { get; init; }
  [JsonPropertyName("price")]
  public Price? Price { get; set; }
  [JsonPropertyName("deliveryCategory")]
  public XtraOptionDeliveryCategory DeliveryCategory { get; init; }
}
