using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products;

namespace Dornbracht.Api.Pdh.Models.Results.Products
{
  public record ProductDetails : ProductBase
  {
    public ProductDetails(ProductResult.WithDetails apiResult) : base(apiResult)
    {
      Attributes = apiResult.Attributes?.OrderBy(attribute => attribute.Order).Select(attribute => new Attribute(attribute)) ?? Array.Empty<Attribute>();
      Texts = apiResult.Texts?.Select(text => new Text(text)) ?? Array.Empty<Text>();
      Documents = apiResult.Documents?.Select(document => new Document(document)) ?? Array.Empty<Document>();
      Sets = apiResult.Sets?.Where(set => set.Product != null).OrderBy(set => set.Position).Select(set => new Product(set.Product)) ?? Array.Empty<Product>();
      Parts = apiResult.Parts?.Where(part => part.Product != null).OrderBy(part => part.Position).Select(part => new Product(part.Product)) ?? Array.Empty<Product>();
      Variants = apiResult.Variants?.Select(variant => new Product(variant)) ?? Array.Empty<Product>();
      Accessories = apiResult.Accessories?.Select(accessory => new Product(accessory)) ?? Array.Empty<Product>();
      XtraOptions = apiResult.XtraOptions?.Select(x => new XtraOption(x)) ?? Array.Empty<XtraOption>();
    }

    [JsonPropertyName("attributes")]
    public IEnumerable<Attribute> Attributes { get; init; }

    [JsonPropertyName("xtraOptions")]
    public IEnumerable<XtraOption> XtraOptions { get; init; }

    [JsonPropertyName("texts")]
    public IEnumerable<Text> Texts { get; init; }

    [JsonPropertyName("documents")]
    public IEnumerable<Document> Documents { get; init; }

    [JsonPropertyName("sets")]
    public IEnumerable<Product> Sets { get; init; }

    [JsonPropertyName("parts")]
    public IEnumerable<Product> Parts { get; init; }

    [JsonPropertyName("variants")]
    public IEnumerable<Product> Variants { get; init; }

    [JsonPropertyName("accessories")]
    public IEnumerable<Product> Accessories { get; init; }

  }
}
