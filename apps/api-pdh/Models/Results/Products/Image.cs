using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

namespace Dornbracht.Api.Pdh.Models.Results.Products
{

  public record struct Image
  {
    public Image(ProductImage apiResult)
    {
      Name = apiResult.Name;
      Type = apiResult.ContentType;
      Full = apiResult.Full.HasValue ? new ImageFormat(apiResult.Full.Value) : null;
      Medium = apiResult.Medium.HasValue ? new ImageFormat(apiResult.Medium.Value) : null;
      Thumb = apiResult.Thumb.HasValue ? new ImageFormat(apiResult.Thumb.Value) : null;
    }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("type")]
    public string Type { get; init; }

    [JsonPropertyName("full")]
    public ImageFormat? Full { get; init; }

    [JsonPropertyName("medium")]
    public ImageFormat? Medium { get; init; }

    [JsonPropertyName("thumb")]
    public ImageFormat? Thumb { get; init; }
  }
}
