using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Results.Products;

public record XtraOptionPrice
{
  public XtraOptionPrice(Pdh.Products.Elements.XtraOptionPrice apiResult)
  {
    Currency = apiResult.Currency;
    Value = apiResult.Value;
  }

  [JsonPropertyName("currency")]
  public string Currency { get; init; }
  [JsonPropertyName("value")]
  public double? Value { get; init; }
}
