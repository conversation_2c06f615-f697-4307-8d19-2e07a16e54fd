using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Results.Products;

public record XtraOptionProducts
{
  public XtraOptionProducts(Pdh.Products.Elements.XtraOptionProducts apiResult)
  {
    Amount = apiResult.Amount;
    Target = apiResult.Target.HasValue ? new XtraOptionTarget(apiResult.Target.Value) : null;
  }

  [JsonPropertyName("amount")]
  public int? Amount { get; init; }
  [JsonPropertyName("target")]
  public XtraOptionTarget Target { get; init; }
}
