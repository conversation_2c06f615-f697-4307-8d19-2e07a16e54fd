using System.Text.Json.Serialization;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;

namespace Dornbracht.Api.Pdh.Models.Results.Products
{
  public record struct Attribute
  {
    public Attribute(ProductAttribute apiResult)
    {
      Name = apiResult.Name;
      Caption = apiResult.Caption;
      Group = apiResult.Group;
      Tab = apiResult.Tab;
      Value = apiResult.Value;
    }

    [JsonPropertyName("name")]
    public string Name { get; init; }

    [JsonPropertyName("caption")]
    public string Caption { get; init; }

    [JsonPropertyName("group")]
    public string Group { get; init; }

    [JsonPropertyName("tab")]
    public string Tab { get; init; }

    [JsonPropertyName("value")]
    public string Value { get; init; }
  }
}
