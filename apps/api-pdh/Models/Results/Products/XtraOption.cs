using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;

namespace Dornbracht.Api.Pdh.Models.Results.Products;

public record XtraOption
{
  public XtraOption(Pdh.Products.Elements.XtraOption apiResult)
  {
    Id = apiResult.Id;
    Number = apiResult.Number;
    ConfigCode = apiResult.ConfigCode;
    Category = apiResult.Category;
    CategoryCode = apiResult.CategoryCode;
    Dimension = apiResult.Dimension;
    DimensionCode = apiResult.DimensionCode;
    ConfigDimension = apiResult.ConfigDimension;
    Name = apiResult.Name;
    ConfigName = apiResult.ConfigName;
    LeadTime = apiResult.LeadTime;
    ImageUrl = apiResult.ImageUrl;
    AgioWithoutVat = apiResult.AgioWithoutVat.HasValue ? new XtraOptionPrice(apiResult.AgioWithoutVat.Value) : null;
    AgioWithVat = apiResult.AgioWithVat.HasValue ? new XtraOptionPrice(apiResult.AgioWithVat.Value) : null;
    PriceWithoutVat = apiResult.PriceWithoutVat.HasValue ? new XtraOptionPrice(apiResult.PriceWithoutVat.Value) : null;
    TotalWithVat = apiResult.TotalWithVat.HasValue ? new XtraOptionPrice(apiResult.TotalWithVat.Value) : null;
    TotalWithoutVat = apiResult.TotalWithoutVat.HasValue ? new XtraOptionPrice(apiResult.TotalWithoutVat.Value) : null;
    PriceWithVat = apiResult.PriceWithVat.HasValue ? new XtraOptionPrice(apiResult.PriceWithVat.Value) : null;
    Products = apiResult.Products?.Select(a => new XtraOptionProducts(a)) ?? Array.Empty<XtraOptionProducts>();
  }

  [JsonPropertyName("id")]
  public int? Id { get; init; }
  [JsonPropertyName("number")]
  public string Number { get; init; }
  [JsonPropertyName("configCode")]
  public string ConfigCode { get; init; }
  [JsonPropertyName("category")]
  public string Category { get; init; }
  [JsonPropertyName("categoryCode")]
  public string CategoryCode { get; init; }
  [JsonPropertyName("dimension")]
  public string Dimension { get; init; }
  [JsonPropertyName("dimensionCode")]
  public string DimensionCode { get; init; }
  [JsonPropertyName("configDimension")]
  public string ConfigDimension { get; init; }
  [JsonPropertyName("name")]
  public string Name { get; init; }
  [JsonPropertyName("configName")]
  public string ConfigName { get; init; }
  [JsonPropertyName("leadTime")]
  public int? LeadTime { get; init; }
  [JsonPropertyName("imageUrl")]
  public string ImageUrl { get; init; }
  [JsonPropertyName("agioWithoutVat")]
  public XtraOptionPrice AgioWithoutVat { get; set; }
  [JsonPropertyName("agioWithVat")]
  public XtraOptionPrice AgioWithVat { get; set; }
  [JsonPropertyName("priceWithoutVat")]
  public XtraOptionPrice PriceWithoutVat { get; set; }
  [JsonPropertyName("totalWithVat")]
  public XtraOptionPrice TotalWithVat { get; set; }
  [JsonPropertyName("totalWithoutVat")]
  public XtraOptionPrice TotalWithoutVat { get; set; }
  [JsonPropertyName("priceWithVat")]
  public XtraOptionPrice PriceWithVat { get; set; }
  [JsonPropertyName("products")]
  public IEnumerable<XtraOptionProducts> Products { get; init; }
}
