# PDH Azure Functions

## Prerequisites

- Azure Functions VS Code Extensions

## Configuration

- Create `local.settings.json` file with the following properties:

  ```json
  {
    "IsEncrypted": false,
    "Values": {
      "AzureWebJobsStorage": "",
      "FUNCTIONS_WORKER_RUNTIME": "dotnet",
      "API_BASE_URL": "http://localhost:7071",
      "SITEMAP_STORAGE_CONNECTION_STRING": "",
      "SITEMAP_STORAGE_CONTAINER_NAME": "$web",
      "DORNBRACHT_PDH_DATA_URL": "https://pis-web-dataservice-site-0-t-d50.azurewebsites.net",
      "DORN<PERSON>ACHT_PDH_SEARCH_URL": "https://pis-web-dataservice-site-0-t-d50.azurewebsites.net",
      "DORNBRACHT_PDH_PRODUCT_URL": "https://pis-web-productservice-site-0-t-d50.azurewebsites.net",
      "DORNBRACHT_PDH_CLIENT_CERTIFICATE": "{base64 encoded certificate}",
      "DORNBRACHT_PDH_CLIENT_CERTIFICATE_PASSWORD": "{cert password}",
      "AzureAd:TenantName": "identityb2c0dd50",
      "AzureAd:ClientId": "5fd6a63a-73be-45d2-b364-c927e5e4b2c7",
      "AzureAd:PolicyName": "b2c_1a_signup_signin_splitemailverificationandsignup",
      "AzureAd:Audience": "4f496729-bdf5-47f8-9d19-70be9d856d85",
      "DORNBRACHT_DOMAIN": "https://website-dev.dornbracht.com/",
      "MAILJET-API-KEY": "xxxx",
      "MAILJET-SECRET-KEY": "yyyy"
    },
    "Host": {
      "CORS": "*"
    }
  }
  ```

- Fill the environment variables with the appropriate values.

You can base64-encode the certificate pfx file with the following PS script:

```ps1
[convert]::ToBase64String((Get-Content -path "Dornbracht.DEP.Business.PDH.Dornbracht.pfx" -Encoding byte))
```
