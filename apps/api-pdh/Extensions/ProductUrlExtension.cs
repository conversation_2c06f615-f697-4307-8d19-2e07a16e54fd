using System.Net;
using System.Linq;
using System.Text.RegularExpressions;
namespace Dornbracht.Api.Pdh.Extensions
{
  public static class ProductUrlExtension
  {
    /// <summary>
    /// Returns escaped product url. Should be equal to productHelpers.js escaping function.
    /// </summary>
    /// <param name="productUrl"></param>
    /// <returns></returns>
    public static string EscapeProductUrl(this string productUrl)
    {
      var encodedUrlParts =  productUrl.Split('/').Select(p => {
        var part = Regex.Replace(p.ToLower(), "\\s|%20|!|'|\\(|\\)|\\[|\\]|_|\\.|~|\\||,|:|;|\\*|\\?|\"|<|>|\\\\|#|@|=|&|\\+|%|\\$", "-");
        return WebUtility.UrlEncode(Regex.Replace(part, "[-]+", "-"));
      });

      return string.Join('/', encodedUrlParts);
    }

    /// <summary>
    /// Returns url with language and "products" prefix.
    /// </summary>
    /// <param name="productUrl"></param>
    /// <param name="language">Url language</param>
    /// <returns></returns>
    public static string EnsureProductUrlPrefix(this string productUrl, string language)
    {
      string productPrefix;
      switch (language)
      {
        case "de":
          productPrefix = "produkte";
          break;
        case "it":
          productPrefix = "prodotti";
          break;
        case "es":
          productPrefix = "productos";
          break;
        case "fr":
          productPrefix = "produits";
          break;
        default:
          productPrefix = "products";
          break;
      }

      return string.Concat("/", productPrefix, productUrl);
    }
  }
}
