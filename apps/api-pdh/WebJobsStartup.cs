using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Hosting;

[assembly: WebJobsStartup(typeof(Dornbracht.Api.Pdh.WebJobsStartup))]
namespace Dornbracht.Api.Pdh
{
  public class WebJobsStartup : IWebJobsStartup
  {
    public void Configure(IWebJobsBuilder builder)
    {
      // add Azure AD token validation binding
      builder.AddAzureAdTokenValidationBinding();
    }
  }
}
