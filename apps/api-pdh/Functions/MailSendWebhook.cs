using System.Threading.Tasks;
using Dornbracht.Api.Pdh.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Functions.Extensions.Attributes;
using Azure.Messaging.EventGrid;
using System;
using Azure.Messaging.EventGrid.SystemEvents;


namespace Dornbracht.Api.Pdh.Functions
{
  public class MailSendWebhook
  {
    /// <summary>
    /// Event trigger webhook for sending emails with downloadable document bundle links
    /// </summary>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Describing message</response>
    [FunctionName(nameof(MailSendWebhook))]
    [SwaggerTag("DocumentBundle Endpoints")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<IActionResult> Run(
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "documentbundles/sendmail")] HttpRequest req, ILogger log)
    {
          BinaryData events = await BinaryData.FromStreamAsync(req.Body);
          EventGridEvent[] eventGridEvents = EventGridEvent.ParseMany(events);

          foreach(var eventGridEvent in eventGridEvents)
          {
              if (eventGridEvent.TryGetSystemEventData(out object eventData))
            {
                // Handle the subscription validation event
                if (eventData is SubscriptionValidationEventData subscriptionValidationEventData)
                {
                    var responseData = new
                    {
                        ValidationResponse = subscriptionValidationEventData.ValidationCode
                    };

                    return new OkObjectResult(responseData);
                }
                if (eventGridEvent.EventType == "ActivityNotificationChangedEvent")
                {
                    var data = MailService.DeserializeEventGrid(log, eventGridEvent.Data.ToString());

                    if(data != null)
                    {
                        bool success = MailService.SendMailBundleDownloadable(log, data.RecipientEMail, data.RecipientLanguage);
                        if(success)
                        {
                          return new OkObjectResult("Mail successfully sent");
                        }
                    }
                }
            }
          }
          return new OkObjectResult(string.Empty);
        }
    }
}


