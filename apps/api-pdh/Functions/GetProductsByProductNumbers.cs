using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Pdh.Services;
using Microsoft.AspNetCore.Http;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Functions.Extensions.AzureAd;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Api.Pdh.Models.Results.Products;
using System.Collections.Generic;
using Microsoft.Extensions.Options;
using Dornbracht.Api.Pdh.Options;

namespace Dornbracht.Api.Pdh.Functions
{
  public class GetProductsByProductNumbers
  {
    public GetProductsByProductNumbers(PdhProductsServiceClient serviceClient, IOptions<ProductsApiOptions> options)
    {
      PdhServiceClient = serviceClient;
      Options = options;
    }

    private PdhProductsServiceClient PdhServiceClient { get; }

    private IOptions<ProductsApiOptions> Options { get; }

    /// <summary>
    /// Requests channel data and metadata for a list of product numbers.
    /// </summary>
    /// <remarks>
    /// Optional: User authentication via JWT access token. If the token is successfully validated, prices are included in the response.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="productNumbers">Pipe-separated list of product numbers. Only non-formatted product numbers are supported.</param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">List of product details.</response>
    [FunctionName(nameof(GetProductsByProductNumbers))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Endpoints")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "products/{market}/{language}/{productNumbers}/list")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, string productNumbers, ILogger log)
    {
      var numbers = productNumbers.Split('|');
      var apiResult = await PdhServiceClient.GetProductsByNumbers(market, language, numbers, token != null && token.TryGetUserId(out var userId) ? userId : null);
      var result = new List<Product>();

      foreach (var productResult in apiResult)
      {
        switch (productResult)
        {
          case ProductResult.Success data:
            result.Add(new Product(data));
            break;
        }
      }

      req.AddMaxAgeHttpCacheHeader(Options.Value.HttpCacheTimeout, HttpCacheType.Private);
      req.AddVaryByAuthorization();
      return new OkObjectResult(result);
    }
  }
}
