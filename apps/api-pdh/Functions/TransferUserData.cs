using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class TransferUserData
  {
    public TransferUserData(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Transfers all owned objects (specs and lists) to another user.
    /// </summary>
    /// <remarks>This function requires a function key for authentication. Use with care in production environment!</remarks>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Transfer of user data successful.</response>
    /// <response code="400">Transfer failed.</response>
    [FunctionName(nameof(TransferUserData))]
    [SwaggerTag("Administration")]
    [Produces("application/json")]
    [Consumes(typeof(TransferUserDataRequest), "application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Function, "post", Route = "productlists/transfer")][RequestBodyType(typeof(TransferUserDataRequest), "Transfer information")] HttpRequest req, ILogger log)
    {
      var data = await req.ReadFromJsonAsync<TransferUserDataRequest>();
      var result = await PdhServiceClient.TransferUserData(data.From, data.To);

      switch (result)
      {
        case TransferUserResult.Success success:
          return new OkResult();
      }

      return new BadRequestResult();
    }
  }
}
