using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles;
using Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles.Elements;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class DocumentBundlesListAdd
  {
    public DocumentBundlesListAdd(PdhDocumentBundleServiceClient serviceClient)
    {
      PdhDocumentBundleServiceClient = serviceClient;

    }

    private PdhDocumentBundleServiceClient PdhDocumentBundleServiceClient { get; }




    /// <summary>
    /// Adds download bundle to list. Authentication required
    /// </summary>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">List of document bundles</response>
    /// <response code="404">User not found</response>
    [FunctionName(nameof(DocumentBundlesListAdd))]
    [RequiresUserAccessToken]
    [SwaggerTag("DocumentBundle Endpoints")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(DocumentBundleListEntryResult.Success[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "documentbundles/{market}/{language}/add")][RequestBodyType(typeof(AddDownloadToBundleListRequest), "Bundle download head data")] HttpRequest req, [AzureAdTokenValidation] AzureAdToken token, string market, string language, ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId) && token.TryGetUserMailAddress(out var userAddress))
      {

        var requestData = await req.ReadFromJsonAsync<AddDownloadToBundleListRequest>();

        var definition = new Definition(){ Name = requestData.DefinitionName };

        DocumentBundleListEntryResult apiResult;
        if(requestData.Scope == "Category")
        {
            var documentBundleAddRequestData = new DocumentBundleAddCategoryRequest{ Definition = definition, RecipientEMail = userAddress,  Scope = new CategoryScope(){ CategoryPath = requestData.ScopeValue }};

            apiResult = await PdhDocumentBundleServiceClient.AddDocumentBundle(market, language, documentBundleAddRequestData, userId);
        }
        else if(requestData.Scope == "Concept")
        {
            var documentBundleAddRequestData = new DocumentBundleAddConceptRequest{ Definition = definition, RecipientEMail = userAddress,  Scope = new ConceptScope(){ ConceptName = requestData.ScopeValue }};

            apiResult = await PdhDocumentBundleServiceClient.AddDocumentBundle(market, language, documentBundleAddRequestData, userId);
        }
        else
        {
            return new BadRequestResult();
        }


        switch (apiResult)
        {
          case DocumentBundleListEntryResult.Success delta:
            return new OkObjectResult(delta);
          case DocumentBundleListEntryResult.NotFound _:
            return new NotFoundResult();
        }

        return new BadRequestResult();
      }

      return new UnauthorizedResult();
    }
  }
}
