using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class ProductListDelete
  {
    public ProductListDelete(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Deletes a product list
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token. Only product lists of the current user can be deleted.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="listId"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Successfully deleted.</response>
    /// <response code="403">List can not be deleted because the user has no access to it.</response>
    /// <response code="404">Product list not found.</response>
    /// <response code="401">Missing or invalid access token.</response>
    [FunctionName(nameof(ProductListDelete))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Lists")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "delete", Route = "productlists/{market}/{language}/{listId:int}")][SwaggerIgnore] HttpRequest req, [AzureAdTokenValidation][SwaggerIgnore] AzureAdToken token, string market, string language, int listId, ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId))
      {
        var apiResult = await PdhServiceClient.DeleteProductList(market, language, userId, listId);

        switch (apiResult)
        {
          case ProductListSearchResult.Deleted _:
            return new OkResult();
          case ProductListSearchResult.Forbidden _:
            return new ForbidResult("Bearer");
          case ProductListSearchResult.NotFound _:
            return new NotFoundResult();
        }
        return new BadRequestResult();
      }

      return new UnauthorizedResult();
    }
  }
}
