using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class ProductListCopy
  {
    public ProductListCopy(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Copies a product list
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token. Only product lists of the current user can be copied.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="listId"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Successfully copied</response>
    /// <response code="403">List can not be copied because the user has no access to it</response>
    /// <response code="404">Product list not found</response>
    /// <response code="401">Missing or invalid access token.</response>
    [FunctionName(nameof(ProductListCopy))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Lists")]
    [ProducesResponseType(typeof(ProductListResult.ProductList), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = "productlists/{market}/{language}/{listId:int}/copy")][SwaggerIgnore] HttpRequest req, [AzureAdTokenValidation][SwaggerIgnore] AzureAdToken token, string market, string language, int listId, ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId))
      {
        var apiResult = await PdhServiceClient.CopyProductList(market, language, userId, listId);

        switch (apiResult)
        {
          case ProductListResult.Forbidden _:
            return new ForbidResult("Bearer");
          case ProductListResult.NotFound _:
            return new NotFoundResult();
          case ProductListResult.ProductList list:
            return new OkObjectResult(list);
        }
        return new BadRequestResult();
      }

      return new UnauthorizedResult();
    }
  }
}
