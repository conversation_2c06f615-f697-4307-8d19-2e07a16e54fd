using System;
using System.Threading.Tasks;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class TemporaryProductListDetails
  {
    public TemporaryProductListDetails(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Gets the details of the temporary product list for the given user.
    /// </summary>
    /// <param name="req"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="owner"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Product list details.</response>
    /// <response code="400">Invalid owner.</response>
    /// <response code="404">No temporary list exists for the given owner.</response>
    [FunctionName(nameof(TemporaryProductListDetails))]
    [SwaggerTag("Temporary Product Lists")]
    [ProducesResponseType(typeof(ProductListResult.ProductList), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "productlists/temporary/{market}/{language}/{owner}")] HttpRequest req, string market, string language, string owner, ILogger log)
    {
      if (Guid.TryParse(owner, out var ownerId))
      {
        var apiResult = await PdhServiceClient.GetTemporaryProductListDetails(market, language, ownerId);

        req.AddNoCacheHeader();

        if (apiResult is ProductListResult.ProductList)
        {
          return new OkObjectResult(apiResult);
        }

        return new NotFoundResult();
      }

      return new BadRequestResult();
    }
  }
}
