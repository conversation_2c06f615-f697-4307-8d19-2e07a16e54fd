using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Pdh.Services;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Linq;
using Dornbracht.Functions.Extensions.Attributes;

namespace Dornbracht.Api.Pdh
{
  public class BuildProductSitemapManual
  {
    public BuildProductSitemapManual(SitemapService sitemapeService)
    {
      SitemapService = sitemapeService;
    }

    private SitemapService SitemapService { get; }

    /// <summary>
    /// Builds a XML Sitemap in all defined languages. Usage only for testing purposes.
    /// </summary>
    /// <remarks>This function requires a function host (admin) key for authentication.</remarks>
    /// <param name="req"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    [SwaggerTag("Administration")]
    [ProducesResponseType(StatusCodes.Status202Accepted)]
    [FunctionName(nameof(BuildProductSitemapManual))]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Admin, "get", Route = "products/buildproductsitemap")] HttpRequest req, ILogger log)
    {
      await SitemapService.BuildSitemaps(log);

      return new OkObjectResult("") { StatusCode = 202 };
    }
  }
}
