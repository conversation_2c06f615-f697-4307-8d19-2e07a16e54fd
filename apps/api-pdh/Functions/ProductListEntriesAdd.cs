using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class ProductListEntriesAdd
  {
    public ProductListEntriesAdd(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Adds a product to the given product list of the current user.
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token. Products can only be added to product lists of th current user.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="listId"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Delta to the original product list.</response>
    /// <response code="403">Product can not be added to the list because the user has no access to it.</response>
    /// <response code="404">Product list or product not found.</response>
    /// <response code="409">Product has multiple mandatory accessories. A choice is required. (Not supported yet)</response>
    /// <response code="401">Missing or invalid access token.</response>
    [FunctionName(nameof(ProductListEntriesAdd))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Lists")]
    [Consumes(typeof(AddEntryToProductListRequest), "application/json")]
    [ProducesResponseType(typeof(ProductListDeltaResult.Success), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    public async Task<IActionResult> Run(
      [HttpTrigger(AuthorizationLevel.Anonymous,
        "post",
        Route = "productlists/{market}/{language}/{listId:int}/entries")]
      [RequestBodyType(typeof(AddEntryToProductListRequest), "Product entry head data")]
      HttpRequest req,
      [AzureAdTokenValidation] AzureAdToken token,
      string market,
      string language,
      int listId,
      ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId))
      {
        var requestData = await req.ReadFromJsonAsync<AddEntryToProductListRequest>();
        var apiResult = await PdhServiceClient.AddEntryToProductList(
          market,
          language,
          userId,
          listId,
          requestData.ProductNumber,
          requestData.Amount,
          requestData.XtraOptionId);

        switch (apiResult)
        {
          case ProductListDeltaResult.Success delta:
            return new OkObjectResult(delta);
          case ProductListDeltaResult.Forbidden _:
            return new ForbidResult("Bearer");
          case ProductListDeltaResult.NotFound _:
            return new NotFoundResult();
          case ProductListDeltaResult.Conflict _:
            return new ConflictResult();
        }

        return new BadRequestResult();
      }

      return new UnauthorizedResult();
    }
  }
}
