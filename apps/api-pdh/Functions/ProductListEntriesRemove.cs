using System;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class ProductListEntriesRemove
  {
    public ProductListEntriesRemove(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Removes an entry from the given product list.
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token. Product entries can only be removed from lists that the user has access to.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="listId"></param>
    /// <param name="entryId"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Entry was removed successfully.</response>
    /// <response code="403">Entry can not be removed from the list because the user has no access to it.</response>
    /// <response code="404">Product list or entry not found.</response>
    /// <response code="401">Missing or invalid access token.</response>
    [FunctionName(nameof(ProductListEntriesRemove))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Lists")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "delete", Route = "productlists/{market}/{language}/{listId:int}/entries/{entryId:int}")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, int listId, int entryId, ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId))
      {
        var apiResult = await PdhServiceClient.RemoveEntryFromProductList(market, language, userId, listId, entryId);

        switch (apiResult)
        {
          case ProductListDeltaResult.Deleted _:
            return new OkResult();
          case ProductListDeltaResult.Forbidden _:
            return new ForbidResult("Bearer");
          case ProductListDeltaResult.NotFound _:
            return new NotFoundResult();
        }

        return new OkObjectResult(Array.Empty<ProductListResult.ProductList>());
      }

      return new UnauthorizedResult();
    }
  }
}
