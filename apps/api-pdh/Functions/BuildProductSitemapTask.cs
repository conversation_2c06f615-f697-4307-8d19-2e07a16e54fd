using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.Pdh.Services;
using System.Linq;

namespace Dornbracht.Api.Pdh
{
  public class BuildProductSitemapTask
  {
    public BuildProductSitemapTask(SitemapService sitemapeService)
    {
      SitemapService = sitemapeService;
    }

    private SitemapService SitemapService { get; }

    /// <summary>
    /// Builds a XML Sitemap in all defined languages
    /// </summary>
    /// <param name="timer"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    [FunctionName(nameof(BuildProductSitemapTask))]
    public async Task Run([TimerTrigger("0 0 1 * * *")] TimerInfo timer, ILogger log)
    {
      await SitemapService.BuildSitemaps(log);
    }
  }
}
