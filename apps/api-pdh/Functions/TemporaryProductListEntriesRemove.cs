using System;
using System.Threading.Tasks;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class TemporaryProductListEntriesRemove
  {
    public TemporaryProductListEntriesRemove(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Removes a product from the temporary product list of the given anonymous user.
    /// </summary>
    /// <param name="req"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="owner"></param>
    /// <param name="entryId"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Entry was removed successfully.</response>
    /// <response code="403">Entry can not be removed from the list because the user has no access to it.</response>
    /// <response code="404">Product list or entry not found.</response>
    /// <response code="400">Invalid owner.</response>
    [FunctionName(nameof(TemporaryProductListEntriesRemove))]
    [SwaggerTag("Temporary Product Lists")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "delete", Route = "productlists/temporary/{market}/{language}/{owner}/entries/{entryId:int}")] HttpRequest req, string market, string language, string owner, int entryId, ILogger log)
    {

      if (!Guid.TryParse(owner, out var ownerId))
      {
        return new BadRequestResult();
      }

      var apiResult = await PdhServiceClient.RemoveEntryFromTemporaryProductList(market, language, ownerId, entryId);

      switch (apiResult)
      {
        case ProductListDeltaResult.Deleted _:
          return new OkResult();
        case ProductListDeltaResult.Forbidden _:
          return new ForbidResult("Bearer");
        case ProductListDeltaResult.NotFound _:
          return new NotFoundResult();
      }

      return new OkObjectResult(Array.Empty<ProductListResult.ProductList>());
    }
  }
}
