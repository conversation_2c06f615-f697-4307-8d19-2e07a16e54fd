using System;
using System.Threading.Tasks;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Api.Pdh.Options;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Dornbracht.Api.Pdh.Functions
{
  public class DownloadDocument
  {
    public DownloadDocument(PdhDataServiceClient serviceClient, IOptions<ProductsApiOptions> options)
    {
      PdhServiceClient = serviceClient;
      Options = options;
    }

    private PdhDataServiceClient PdhServiceClient { get; }

    private IOptions<ProductsApiOptions> Options { get; }

    /// <summary>
    /// Downloads the document for a given document id.
    /// </summary>
    /// <param name="req"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="id"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Document download.</response>
    /// <response code="404">Document not found.</response>
    [FunctionName(nameof(DownloadDocument))]
    [SwaggerTag("Document Endpoints")]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "documents/download/{market}/{language}/{id}")] HttpRequest req, string market, string language, string id, ILogger log)
    {
      var result = await PdhServiceClient.GetDocument(market, language, id);

      switch (result)
      {
        case DocumentResult.Success document:
          var fileName = "document.pdf";

          if (req.Query.TryGetValue("name", out var name))
            fileName = name;

          req.AddMaxAgeHttpCacheHeader(Options.Value.HttpCacheTimeout, HttpCacheType.Private);
          req.HttpContext.Response.Headers.ContentDisposition = $"inline; filename={fileName}";

          return new FileStreamResult(document.Data, "application/octet-stream");
      }

      return new NotFoundResult();
    }
  }
}
