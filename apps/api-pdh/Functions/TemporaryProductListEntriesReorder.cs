using System;
using System.Linq;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class TemporaryProductListEntriesReorder
  {
    public TemporaryProductListEntriesReorder(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Reorder product entries for the the temporary product list of the given anonymous user.
    /// </summary>
    /// <param name="req"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="owner"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Delta to the original product list.</response>
    /// <response code="403">Product can not be added to the list because the user has no access to it.</response>
    /// <response code="404">Product list or product not found.</response>
    /// <response code="409">Product has multiple mandatory accessories. A choice is required. (Not supported yet)</response>
    /// <response code="400">Invalid owner.</response>
    [FunctionName(nameof(TemporaryProductListEntriesReorder))]
    [SwaggerTag("Temporary Product Lists")]
    [Consumes(typeof(AddEntryToProductListRequest), "application/json")]
    [ProducesResponseType(typeof(ProductListDeltaResult.Success), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = "productlists/temporary/{market}/{language}/{owner}/entries")][RequestBodyType(typeof(ProductListEntryOrderRequest[]), "Product list entry order")] HttpRequest req, string market, string language, string owner, ILogger log)
    {
      var requestData = await req.ReadFromJsonAsync<ProductListEntryOrderRequest[]>();

      if (!Guid.TryParse(owner, out var ownerId))
      {
        return new BadRequestResult();
      }

      var apiResult = await PdhServiceClient.ReorderTemporaryProductEntries(market, language, ownerId, requestData.ToDictionary(e => e.Id, e => e.Order));

      switch (apiResult)
      {
        case ProductListResult.ProductList list:
          return new OkObjectResult(list);
        case ProductListResult.Forbidden _:
          return new ForbidResult("Bearer");
        case ProductListResult.NotFound _:
          return new NotFoundResult();
      }

      return new BadRequestResult();
    }
  }
}
