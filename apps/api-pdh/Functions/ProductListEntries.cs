using System;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class ProductListEntries
  {
    public ProductListEntries(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Lists all product entries for the given product list.
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token. Product entries can only be listed for lists that the user has access to.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="listId"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">List of all products on the list.</response>
    /// <response code="403">No access to the requested product list.</response>
    /// <response code="404">Product list not found.</response>
    /// <response code="401">Missing or invalid access token.</response>
    [FunctionName(nameof(ProductListEntries))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Lists")]
    [ProducesResponseType(typeof(ProductListEntry[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "productlists/{market}/{language}/{listId:int}/entries")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, int listId, ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId))
      {
        var apiResult = await PdhServiceClient.GetProductEntries(market, language, userId, listId);

        switch (apiResult)
        {
          case ProductEntryListResult.Success data:
            req.AddNoCacheHeader();
            return new OkObjectResult(data.Value);
          case ProductEntryListResult.Forbidden _:
            return new ForbidResult("Bearer");
          case ProductEntryListResult.NotFound _:
            return new NotFoundResult();
        }

        return new OkObjectResult(Array.Empty<ProductListResult.ProductList>());
      }

      return new UnauthorizedResult();
    }
  }
}
