using System.Net.Http;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Functions.Extensions.Swagger;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;

namespace Dornbracht.Api.Pdh.Functions
{
  public static class Swagger
  {
    [SwaggerIgnore]
    [FunctionName(nameof(SwaggerJson))]
    public static async Task<HttpResponseMessage> SwaggerJson(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "products/swagger/json")] HttpRequestMessage req,
        [SwashBuckleClient] ISwashBuckleClient swashBuckleClient)
    {
      return await swashBuckleClient.CreateSwaggerJsonDocumentResponseAsync(req);
    }

    [SwaggerIgnore]
    [FunctionName(nameof(SwaggerUi))]
    public static async Task<HttpResponseMessage> SwaggerUi(
        [HttpTrigger(AuthorizationLevel.Function, "get", Route = "products/swagger/ui")] HttpRequestMessage req,
        [SwashBuckleClient] ISwashBuckleClient swashBuckleClient)
    {
      return await swashBuckleClient.CreateSwaggerUIResponseAsync(req, "products/swagger/json");
    }
  }
}
