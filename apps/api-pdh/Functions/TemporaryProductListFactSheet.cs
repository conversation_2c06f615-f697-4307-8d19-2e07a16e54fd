using System;
using System.Threading.Tasks;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class TemporaryProductListFactSheet
  {
    public TemporaryProductListFactSheet(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Downloads a combined fact sheet for the temporary product list of the given anonymous user.
    /// </summary>
    /// <param name="req"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="owner"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Fact sheet download.</response>
    /// <response code="403">No access to the requested product list.</response>
    /// <response code="404">Product list not found.</response>
    /// <response code="401">Missing or invalid access token.</response>
    [FunctionName(nameof(TemporaryProductListFactSheet))]
    [SwaggerTag("Temporary Product Lists")]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status409Conflict)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "productlists/temporary/{market}/{language}/{owner}/sheet")] HttpRequest req, string market, string language, string owner, ILogger log)
    {

      if (!Guid.TryParse(owner, out var ownerId))
      {
        return new BadRequestResult();
      }

        var apiResult = await PdhServiceClient.DownloadTemporaryFactSheet(market, language, ownerId);

        switch (apiResult)
        {
          case ProductListFactSheetResult.Success sheet:
            string fileName = $"db_List_temporary_specs_{DateTime.Now:yyddMM}.zip";
            req.HttpContext.Response.Headers.ContentDisposition = $"inline; filename={fileName}";
            req.AddNoCacheHeader();
            return new FileStreamResult(sheet.Data, "application/octet-stream");
          case ProductListFactSheetResult.Forbidden _:
            return new ForbidResult("Bearer");
          case ProductListFactSheetResult.NotFound _:
            return new NotFoundResult();
        }

        return new BadRequestResult();
    }
  }
}
