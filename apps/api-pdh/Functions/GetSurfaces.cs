using System;
using System.Threading.Tasks;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Api.Pdh.Options;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Dornbracht.Api.Pdh.Functions;

public class GetSurfaces {
  private PdhProductsServiceClient PdhServiceClient { get; }

  private IOptions<ProductsApiOptions> Options { get; }

  public GetSurfaces(PdhProductsServiceClient serviceClient, IOptions<ProductsApiOptions> options) {
    PdhServiceClient = serviceClient;
    Options = options;
  }


  [FunctionName(nameof(GetSurfaces))]
  [SwaggerTag("Product Endpoints")]
  [Produces("application/json")]
  [ProducesResponseType(typeof(String), StatusCodes.Status200OK)]
  [ProducesResponseType(StatusCodes.Status404NotFound)]
  public async Task<IActionResult> Run(
    [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "products/surfaces/{market}/{language}")] HttpRequest req, string market, string language, ILogger log)
  {
    var result = await PdhServiceClient.GetSurfaces(market, language);
    switch (result)
    {
      case SurfaceListResult.Success data:
        req.AddMaxAgeHttpCacheHeader(Options.Value.HttpCacheTimeout, HttpCacheType.Private);
        return new OkObjectResult(data.Surfaces);
    }

    return new NotFoundResult();
  }
}
