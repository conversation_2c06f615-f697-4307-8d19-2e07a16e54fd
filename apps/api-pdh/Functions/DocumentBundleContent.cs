using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Functions.Extensions;

namespace Dornbracht.Api.Pdh.Functions
{
  public class DocumentBundleContent
  {
    public DocumentBundleContent(PdhDocumentBundleServiceClient serviceClient)
    {
      PdhDocumentBundleServiceClient = serviceClient;

    }

    private PdhDocumentBundleServiceClient PdhDocumentBundleServiceClient { get; }

    /// <summary>
    /// Get bundle content for given bundle id. Authentication required
    /// </summary>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="bundleId"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">The bundle content was successfully retrieved</response>
    /// <response code="404">The bundle does not exists</response>
    [FunctionName(nameof(DocumentBundleContent))]
    [RequiresUserAccessToken]
    [SwaggerTag("DocumentBundle Endpoints")]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status410Gone)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "documentbundles/{market}/{language}/{bundleId:int}/content")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, int bundleId, ILogger log)
    {
      var fileName = "Bundle.zip";
      if (req.Query.TryGetValue("name", out var name))
      {
        fileName = name;
      }

      var result = await PdhDocumentBundleServiceClient.GetBundleContent(market, language, bundleId, fileName, token != null && token.TryGetUserId(out var userId) ? userId : null);

      switch (result)
      {
        case DocumentResult.Success document:
          req.AddNoCacheHeader();
          req.HttpContext.Response.Headers.ContentDisposition = $"inline; filename={document.FileName}";
          return new FileStreamResult(document.Data, "application/octet-stream");

        case DocumentResult.Error:
          return new StatusCodeResult(410);
      }

      return new NotFoundResult();
    }
  }
}
