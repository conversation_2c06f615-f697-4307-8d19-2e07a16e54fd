using System;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class TemporaryProductListTransfer
  {
    public TemporaryProductListTransfer(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Transfers all owned objects (specs and lists) including temporary entities from a given temporary user to the authenticated user.
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Transfer of user data successful.</response>
    /// <response code="400">Transfer failed or owner invalid.</response>
    [FunctionName(nameof(TemporaryProductListTransfer))]
    [SwaggerTag("Temporary Product Lists")]
    [Produces("application/json")]
    [RequiresUserAccessToken]
    [Consumes(typeof(TransferTemporaryUserDataRequest), "application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "user/transfer/temporary")][RequestBodyType(typeof(TransferTemporaryUserDataRequest), "Transfer information")] HttpRequest req, [AzureAdTokenValidation] AzureAdToken token, ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId))
      {
        var data = await req.ReadFromJsonAsync<TransferTemporaryUserDataRequest>();
        if (!Guid.TryParse(data.From, out var fromId))
        {
          return new BadRequestResult();
        }

        var result = await PdhServiceClient.TransferUserData(fromId.ToString("N"), userId, true);
        switch (result)
        {
          case TransferUserResult.Success success:
            return new OkResult();
        }

        return new BadRequestResult();
      }

      return new UnauthorizedResult();
    }
  }
}
