using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;
using Dornbracht.Api.Pdh.Options;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Dornbracht.Api.Pdh.Functions
{
  public class GetProductPrice
  {
    public GetProductPrice(PdhProductsServiceClient serviceClient, IOptions<ProductsApiOptions> options)
    {
      PdhServiceClient = serviceClient;
      Options = options;
    }

    private PdhProductsServiceClient PdhServiceClient { get; }

    private IOptions<ProductsApiOptions> Options { get; }

    /// <summary>
    /// Get price details for a product.
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="productNumber">Only non-formatted product numbers are supported.</param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Product details.</response>
    /// <response code="404">Product not found.</response>
    [FunctionName(nameof(GetProductPrice))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Endpoints")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(ProductPrice), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "products/{market}/{language}/{productNumber}/price")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, string productNumber, ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId))
      {
        var result = await PdhServiceClient.GetProduct(market, language, productNumber, userId);

        switch (result)
        {
          case ProductResult.Success details:
            req.AddMaxAgeHttpCacheHeader(Options.Value.HttpCacheTimeout, HttpCacheType.Private);

            return new OkObjectResult(details.Price);
        }

        return new NotFoundResult();
      }

      return new UnauthorizedResult();
    }
  }
}
