using System.Linq;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.Search;
using Dornbracht.Api.Pdh.Models.Pdh.Search.DocumentBundle;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class SearchDocumentBundles
  {
    public SearchDocumentBundles(PdhSearchServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhSearchServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Searches for document bundles based on search text and facet filters.
    /// </summary>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Product details.</response>
    /// <response code="400">No valid search result.</response>
    [FunctionName(nameof(SearchDocumentBundles))]
    [RequiresUserAccessToken]
    [SwaggerTag("Search")]
    [Produces("application/json")]
    [Consumes(typeof(DocumentBundleSearchRequest), "application/json")]
    [ProducesResponseType(typeof(DocumentBundleSearchResult.Success), StatusCodes.Status200OK)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "documentbundles/{market}/{language}/search")] [RequestBodyType(typeof(DocumentBundleSearchRequest), "Facet filters")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, ILogger log)
    {
      var searchRequest = await req.ReadFromJsonAsync<DocumentBundleSearchRequest>();


      var result = await PdhServiceClient.SearchWebDocumentBundles(language, market, string.IsNullOrEmpty(searchRequest.Text) ? "*" : searchRequest.Text, searchRequest
      .Filters.Select(filter => new DocumentBundleFacetFilter
      {
        Name = filter.Name,
        Values = filter.Values,
        IsLanguageNeutral = searchRequest.IsLanguageNeutral
      })
      .ToArray(), searchRequest.Page, searchRequest.Order);

      switch (result)
      {
        case DocumentBundleSearchResult.Success success:
          return new OkObjectResult(success);
        case DocumentBundleSearchResult.Error _error:
          log.LogError("DocumentBundle Error requesting " + searchRequest.Text);
          return new StatusCodeResult(500);
      }
      log.LogInformation("DocumentBundle BAD REQUEST");
      return new BadRequestResult();
    }
  }
}
