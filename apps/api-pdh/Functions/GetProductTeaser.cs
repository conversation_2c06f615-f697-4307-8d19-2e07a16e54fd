using System;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Api.Pdh.Models.Results.Products;
using Dornbracht.Api.Pdh.Options;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Dornbracht.Api.Pdh.Functions
{
  public class GetProductTeaser
  {
    public GetProductTeaser(PdhProductsServiceClient serviceClient, IOptions<ProductsApiOptions> options)
    {
      PdhServiceClient = serviceClient;
      Options = options;
    }

    private PdhProductsServiceClient PdhServiceClient { get; }

    private IOptions<ProductsApiOptions> Options { get; }

    /// <summary>
    /// Get teaser details for a product.
    /// </summary>
    /// <remarks>
    /// Optional: User authentication via JWT access token. If the token is successfully validated, prices are included in the response.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="productNumber">Only non-formatted product numbers are supported.</param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Product details.</response>
    /// <response code="404">Product not found.</response>
    [FunctionName(nameof(GetProductTeaser))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Endpoints")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Product), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "products/{market}/{language}/{productNumber}/teaser")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, string productNumber, ILogger log)
    {
      bool isValidProduct = true;

      /*
       * validate product number
       */
      try
      {
        log.LogDebug($"GetProductTeaser: validate product for market {market} language {language} and product number {productNumber}.");
        string[] toValidate = { productNumber };
        string[] validated = await PdhServiceClient.PostValidateProduct(market, language, toValidate,
          token != null && token.TryGetUserId(out var userId) ? userId : null);

        log.LogDebug($"GetProductTeaser: validate product for market {market} language {language} and product number {productNumber}: {validated}.");

        if (validated.Length == 0)
        {
          isValidProduct = false;
        }
      }
      catch (Exception ex)
      {
        log.LogError(ex, ex.Message);
      }

      /*
       * request valid product
       */
      if (isValidProduct)
      {
        var result = await PdhServiceClient.GetProduct(market, language, productNumber,
          token != null && token.TryGetUserId(out var userId) ? userId : null);

        switch (result)
        {
          case ProductResult.Success details:
            req.AddMaxAgeHttpCacheHeader(Options.Value.HttpCacheTimeout, HttpCacheType.Private);
            req.AddVaryByAuthorization();

            return new OkObjectResult(new Product(details));
        }
      }
      else
      {
        log.LogDebug($"GetProductTeaser: INVALID product for market {market} language {language} and product number {productNumber}.");
      }

      return new NotFoundResult();
    }
  }
}
