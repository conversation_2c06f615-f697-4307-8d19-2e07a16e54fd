using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class ProductListUpdate
  {
    public ProductListUpdate(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Updates product list metadata (both name and comment are required)
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token. Update is only possible for product lists that the user has access to.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="listId"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">The updated product list.</response>
    /// <response code="401">Missing or invalid access token.</response>
    [FunctionName(nameof(ProductListUpdate))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Lists")]
    [Consumes(typeof(UpdateProductListRequest), "application/json")]
    [ProducesResponseType(typeof(ProductListResult.ProductList), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = "productlists/{market}/{language}/{listId:int}")][RequestBodyType(typeof(UpdateProductListRequest), "Product list head data")] HttpRequest req, [AzureAdTokenValidation] AzureAdToken token, string market, string language, int listId, ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId))
      {
        var requestData = await req.ReadFromJsonAsync<UpdateProductListRequest>();
        var updatedList = await PdhServiceClient.UpdateProductList(market, language, userId, listId, requestData.Name, requestData.Comment);

        return new OkObjectResult(updatedList);
      }

      return new UnauthorizedResult();
    }
  }
}
