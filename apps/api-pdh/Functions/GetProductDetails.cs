using System;
using System.Linq;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Api.Pdh.Models.Results.Products;
using Dornbracht.Api.Pdh.Options;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Dornbracht.Api.Pdh.Functions
{
  public class GetProductDetails
  {
    public GetProductDetails(PdhProductsServiceClient serviceClient, IOptions<ProductsApiOptions> options)
    {
      PdhServiceClient = serviceClient;
      Options = options;
    }

    private PdhProductsServiceClient PdhServiceClient { get; }

    private IOptions<ProductsApiOptions> Options { get; }


    /// <summary>
    /// Get details for a product like variants and attribute texts.
    /// </summary>
    /// <remarks>
    /// Optional: User authentication via JWT access token. If the token is successfully validated, prices are included in the response.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="productNumber">Only non-formatted product numbers are supported.</param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Product details.</response>
    /// <response code="404">Product not found.</response>
    /// <response code="303">Product is historic with replacement or successor.</response>
    [FunctionName(nameof(GetProductDetails))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Endpoints")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(ProductDetails), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status303SeeOther)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "products/{market}/{language}/{productNumber}/details")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, string productNumber, ILogger log)
    {
      log.LogDebug($"called: products/{market}/{language}/{productNumber}/details (auth: {null != token})");
      ProductResult result = await PdhServiceClient.GetProductDetails(
        market,
        language,
        productNumber,
        token != null && token.TryGetUserId(out var userId) ? userId : null);

      switch (result)
      {
        case ProductResult.WithDetails details:
          req.AddMaxAgeHttpCacheHeader(Options.Value.HttpCacheTimeout, HttpCacheType.Private);
          req.AddVaryByAuthorization();

          log.LogDebug($"response with: {details.XtraOptions.Count()} XtraOptions, {details.Attributes.Count()} Attributes, {details.Documents.Count()} Documents");

          return new OkObjectResult(new ProductDetails(details));

        case ProductResult.Historic historic:
          // Wenn Nachfolgeprodukte vorhanden sind und mindestens eines kompatibel ist, 303 zurückgeben
          var compatibleSuccessor = historic.Successors?.FirstOrDefault(s => s.Compatible);
          if (compatibleSuccessor != null)
          {
            // Location-Header für Weiterleitung setzen
            req.HttpContext.Response.Headers.Add("Location", $"/api/products/{market}/{language}/{compatibleSuccessor.Number}/details");
            return new ObjectResult(historic) { StatusCode = StatusCodes.Status303SeeOther };
          }

          // Sonst 404 mit Fehlercode zurückgeben
          return new NotFoundObjectResult(historic);

        case ProductResult.Preview preview:
          return new NotFoundObjectResult(preview);

        case ProductResult.MarketUnavailable marketUnavailable:
          return new NotFoundObjectResult(marketUnavailable);

        case ProductResult.NotFound notFound:
          return new NotFoundObjectResult(notFound);
      }

      return new NotFoundResult();
    }
  }
}
