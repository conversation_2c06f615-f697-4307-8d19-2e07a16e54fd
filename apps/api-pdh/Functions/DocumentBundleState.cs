using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.DocumentBundles;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class DocumentBundleState
  {
    public DocumentBundleState(PdhDocumentBundleServiceClient serviceClient)
    {
      PdhDocumentBundleServiceClient = serviceClient;

    }

    private PdhDocumentBundleServiceClient PdhDocumentBundleServiceClient { get; }

    /// <summary>
    /// Get current bundle state for given bundle id. Authentication required
    /// </summary>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="bundleId"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">The bundle was successfully retrieved</response>
    /// <response code="404">The bundle does not exists</response>
    [FunctionName(nameof(DocumentBundleState))]
    [RequiresUserAccessToken]
    [SwaggerTag("DocumentBundle Endpoints")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(DocumentBundleListEntryResult.Success[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "documentbundles/{market}/{language}/{bundleId:int}")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, int bundleId, ILogger log)
    {
      var result = await PdhDocumentBundleServiceClient.GetDocumentBundleState(market, language, bundleId, token != null && token.TryGetUserId(out var userId) ? userId : null);

      switch (result)
      {
        case DocumentBundleListEntryResult.Success bundleEntry:
          req.AddVaryByAuthorization();

          return new OkObjectResult(bundleEntry);
      }

      return new NotFoundResult();
    }
  }
}
