using System.Threading.Tasks;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Api.Pdh.Models.Pdh.Products.Elements;
using Dornbracht.Api.Pdh.Options;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Dornbracht.Api.Pdh.Functions
{
  public class GetProductCountries
  {
    public GetProductCountries(PdhDataServiceClient serviceClient, IOptions<ProductsApiOptions> options)
    {
      PdhServiceClient = serviceClient;
      Options = options;
    }

    private PdhDataServiceClient PdhServiceClient { get; }

    private IOptions<ProductsApiOptions> Options { get; }

    /// <summary>
    /// Lists all countries in which a product is available.
    /// </summary>
    /// <param name="req"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="productNumber">Only non-formatted product numbers are supported.</param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">List of countries.</response>
    /// <response code="404">Product not found.</response>
    [FunctionName(nameof(GetProductCountries))]
    [SwaggerTag("Product Endpoints")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Country[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "products/{market}/{language}/{productNumber}/countries")] HttpRequest req, string market, string language, string productNumber, ILogger log)
    {
      var result = await PdhServiceClient.GetCountries(productNumber, market, language);

      switch (result)
      {
        case CountryListResult.Success data:
          req.AddMaxAgeHttpCacheHeader(Options.Value.HttpCacheTimeout, HttpCacheType.Private);
          return new OkObjectResult(data.Countries);
      }

      return new NotFoundResult();
    }
  }
}
