using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Threading.Tasks;
using Dornbracht.Api.Pdh.Models.Pdh.Products;
using Dornbracht.Api.Pdh.Options;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Attributes;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Dornbracht.Api.Pdh.Functions
{
  public class DownloadDocumentsZip
  {
    public DownloadDocumentsZip(PdhProductsServiceClient serviceClient, PdhDataServiceClient dataServiceClient, IOptions<ProductsApiOptions> options)
    {
      ProductServiceClient = serviceClient;
      PdhServiceClient = dataServiceClient;
      DataServiceClient = dataServiceClient;
      Options = options;
    }

    private PdhProductsServiceClient ProductServiceClient { get; }

    private PdhDataServiceClient PdhServiceClient { get; }

    private PdhDataServiceClient DataServiceClient { get; }

    private IOptions<ProductsApiOptions> Options { get; }

    /// <summary>
    /// Downloads a zip with all documents for a given product number.
    /// </summary>
    /// <param name="req"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="productNumber"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Zip download.</response>
    /// <response code="404">Documents for zip file not found.</response>
    [FunctionName(nameof(DownloadDocumentsZip))]
    [SwaggerTag("Document Endpoints")]
    [Produces("application/octet-stream")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "documents/download/zip/{market}/{language}/{productNumber}")] HttpRequest req, string market, string language, string productNumber, ILogger log)
    {
      var result = await ProductServiceClient.GetProductDetails(market, language, productNumber);

      if (result is ProductResult.WithDetails product)
      {
        var bundleDocId = product.Documents
          .FirstOrDefault((document) => document.Type == "WebProductBundle")
          ?.Id;
        if (bundleDocId == default)
        {
          return new NotFoundResult();
        }

        var bundleDoc = await PdhServiceClient.GetDocument(market, language, bundleDocId);

        switch (bundleDoc)
        {
          case DocumentResult.Success document:
            var fileName = "CompleteDocumentation.zip";

            if (req.Query.TryGetValue("name", out var name))
              fileName = name;

            req.AddMaxAgeHttpCacheHeader(Options.Value.HttpCacheTimeout, HttpCacheType.Private);
            req.HttpContext.Response.Headers.ContentDisposition = $"inline; filename={fileName}";

            return new FileStreamResult(document.Data, "application/octet-stream");
        }
      }
      return new NotFoundResult();
    }
  }
}
