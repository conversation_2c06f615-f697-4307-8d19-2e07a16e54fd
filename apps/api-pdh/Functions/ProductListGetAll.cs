using System;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class ProductListGetAll
  {
    public ProductListGetAll(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Gets all product lists for the current user.
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">List of all product lists for the current user.</response>
    /// <response code="401">Missing or invalid access token.</response>
    [FunctionName(nameof(ProductListGetAll))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Lists")]
    [ProducesResponseType(typeof(ProductListResult.ProductList[]), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Run(
      [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "productlists/{market}/{language}")] HttpRequest req,
      [SwaggerIgnore] [AzureAdTokenValidation] AzureAdToken token, string market, string language, ILogger log)
    {
      try
      {
        if (token != null && token.TryGetUserId(out var userId))
        {
          var apiResult = await PdhServiceClient.GetProductLists(market, language, userId);

          switch (apiResult)
          {
            case ProductListSearchResult.Success data:
              req.AddNoCacheHeader();
              return new OkObjectResult(data.Value);
          }

          return new OkObjectResult(Array.Empty<ProductListResult.ProductList>());
        }

        return new UnauthorizedResult();
      }
      catch (Exception ex)
      {
        log.LogError("Error in ProductListGetAll", ex);
        throw ex;
      }
    }
  }
}
