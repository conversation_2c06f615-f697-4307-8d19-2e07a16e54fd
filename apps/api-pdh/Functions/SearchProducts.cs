using System.Linq;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.Search;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class SearchProducts
  {
    public SearchProducts(PdhSearchServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhSearchServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Searches for products based on search text and facet filters.
    /// </summary>
    /// <remarks>
    /// Optional: User authentication via JWT access token. If the token is successfully validated, prices are included in the response.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Product details.</response>
    /// <response code="400">No valid search result.</response>
    [FunctionName(nameof(SearchProducts))]
    [RequiresUserAccessToken]
    [SwaggerTag("Search")]
    [Produces("application/json")]
    [Consumes(typeof(ProductSearchRequest), "application/json")]
    [ProducesResponseType(typeof(SearchResult.Success), StatusCodes.Status200OK)]
    public async Task<IActionResult> Run(
               [HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "products/{market}/{language}/search")][RequestBodyType(typeof(ProductSearchRequest), "Facet filters")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, ILogger log)
    {
      var includePrice = token != null && token.TryGetUserId(out var _);
      var searchRequest = await req.ReadFromJsonAsync<ProductSearchRequest>();

      var result = await PdhServiceClient.SearchWebProducts(language, market, string.IsNullOrEmpty(searchRequest.Text) ? "*" : searchRequest.Text, searchRequest
      .Filters.Select(filter => new FacetFilter
      {
        Name = filter.Name,
        Values = filter.Values,
        IsLanguageNeutral = filter.Range == null && searchRequest.IsLanguageNeutral,
        Range = filter.Range
      })
      .ToArray(), searchRequest.Page, includePrice, searchRequest.Order);

      switch (result)
      {
        case SearchResult.Success success:
          return new OkObjectResult(success);
        case SearchResult.Error _error:
          return new StatusCodeResult(500);
      }

      return new BadRequestResult();
    }
  }
}
