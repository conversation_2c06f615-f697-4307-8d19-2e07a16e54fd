using System;
using System.Linq;
using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class ProductListEntriesReorder
  {
    public ProductListEntriesReorder(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Reorder product entries for the given product list.
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token. Product entries can only be reordered for lists that the user has access to.
    /// The order attribute of the given product list entries will be updated to the given value. Then the list is reordered (ASC) by the Order field of the entries and send to PDH
    /// which intern updates the order based on the current order of the list and will generate a new value for the Order field of all entries.
    /// Reordering should therefore only be done for the complete entry list. Non-covered entries will keep their current order value. This will then probably lead to undesired results.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="listId"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Product list details with the reordered entry list. Also contains product prices.</response>
    /// <response code="403">No access to the requested product list.</response>
    /// <response code="404">Product list not found.</response>
    /// <response code="401">Missing or invalid access token.</response>
    [FunctionName(nameof(ProductListEntriesReorder))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Lists")]
    [Consumes(typeof(ProductListEntryOrderRequest[]), "application/json")]
    [ProducesResponseType(typeof(ProductListResult.ProductList), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "put", Route = "productlists/{market}/{language}/{listId:int}/entries")][RequestBodyType(typeof(ProductListEntryOrderRequest[]), "Product list entry order")] HttpRequest req, [SwaggerIgnore][AzureAdTokenValidation] AzureAdToken token, string market, string language, int listId, ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId))
      {
        var requestData = await req.ReadFromJsonAsync<ProductListEntryOrderRequest[]>();
        var apiResult = await PdhServiceClient.ReorderProductEntries(market, language, userId, listId, requestData.ToDictionary(e => e.Id, e => e.Order));

        switch (apiResult)
        {
          case ProductListResult.ProductList list:
            return new OkObjectResult(list);
          case ProductListResult.Forbidden _:
            return new ForbidResult("Bearer");
          case ProductListResult.NotFound _:
            return new NotFoundResult();
        }

        return new BadRequestResult();
      }

      return new UnauthorizedResult();
    }
  }
}
