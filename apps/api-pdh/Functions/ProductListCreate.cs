using System.Threading.Tasks;
using AzureFunctions.Extensions.Swashbuckle.Attribute;
using Dornbracht.Api.Pdh.Models.Pdh.ProductLists;
using Dornbracht.Api.Pdh.Models.Requests;
using Dornbracht.Api.Pdh.Services;
using Dornbracht.Functions.Extensions.Attributes;
using Dornbracht.Functions.Extensions.AzureAd;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.Pdh.Functions
{
  public class ProductListCreate
  {
    public ProductListCreate(PdhProductListsServiceClient serviceClient)
    {
      PdhServiceClient = serviceClient;
    }

    private PdhProductListsServiceClient PdhServiceClient { get; }

    /// <summary>
    /// Creates a new product list for the current user
    /// </summary>
    /// <remarks>
    /// This function requires user authentication via JWT access token. The authenticated user is used as "owner" of the product list.
    /// </remarks>
    /// <param name="req"></param>
    /// <param name="token"></param>
    /// <param name="market"></param>
    /// <param name="language"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">The newly created product list.</response>
    /// <response code="401">Missing or invalid access token.</response>
    [FunctionName(nameof(ProductListCreate))]
    [RequiresUserAccessToken]
    [SwaggerTag("Product Lists")]
    [Consumes(typeof(CreateProductListRequest), "application/json")]
    [ProducesResponseType(typeof(ProductListResult.ProductList), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Anonymous, "post", Route = "productlists/{market}/{language}")][RequestBodyType(typeof(CreateProductListRequest), "Product list head data")] HttpRequest req, [AzureAdTokenValidation] AzureAdToken token, string market, string language, ILogger log)
    {
      if (token != null && token.TryGetUserId(out var userId))
      {
        var requestData = await req.ReadFromJsonAsync<CreateProductListRequest>();
        var list = await PdhServiceClient.CreateProductList(market, language, userId, requestData.Name, requestData.Comment);

        return new OkObjectResult(list);
      }

      return new UnauthorizedResult();
    }
  }
}
