using Dornbracht.Api.Pdh.Services;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Azure.Messaging.EventGrid;
using Microsoft.Azure.WebJobs.Extensions.EventGrid;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Dornbracht.Functions.Extensions.Attributes;

namespace Dornbracht.Api.Pdh.Functions
{
  public class MailSend
  {
    /// <summary>
    /// Testing mail service with given mailjet language and recipient mail address
    /// </summary>
    /// <param name="req"></param>
    /// <param name="language"></param>
    /// <param name="recipient"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    /// <response code="200">Mail was successfully sent</response>
    /// <response code="400">Bad request. Check parameters</response>
    [FunctionName(nameof(MailSend) + "_test")]
    [SwaggerTag("DocumentBundle Endpoints")]
    [Produces("application/json")]
    public IActionResult RunHttp(
      [HttpTrigger(AuthorizationLevel.Function, "GET", Route = "documentbundles/sendTestMail/{language}/{recipient}")] HttpRequest req,
      string language, string recipient, ILogger log)
    {
      log.LogDebug("EmailSend.Run Task...");

      if (string.IsNullOrEmpty(recipient))
      {
        return new BadRequestObjectResult("missing parameter 'recipient'") { StatusCode = 400 };
      }

      if (string.IsNullOrEmpty(language))
      {
        return new BadRequestObjectResult("missing parameter 'language'") { StatusCode = 400 };
      }

      bool success = MailService.SendMailBundleDownloadable(log, recipient, language);

      if (!success)
      {
        return new BadRequestObjectResult("Error sending mail") { StatusCode = 500 };
      }

      return new OkObjectResult("Mail sent.");
    }

    /// <summary>
    /// Sends mail triggered by Dornbracht Event Grid
    /// </summary>
    /// <param name="eventGridEvent"></param>
    /// <param name="log"></param>
    /// <returns></returns>
    [FunctionName(nameof(MailSend))]
    public static void Run([EventGridTrigger] EventGridEvent eventGridEvent, ILogger log)
    {
      log.LogDebug("EmailSend.Run EventGridEvent received...");

      //TODO Ggf. Absicherung über topic in Umgebungsvariable auslagern
      if(eventGridEvent.EventType == "ActivityNotificationChangedEvent" && eventGridEvent.Topic.Contains("pis-"))
      {
          var data = MailService.DeserializeEventGrid(log, eventGridEvent.Data.ToString());

          if(data != null && data.IsSucceeded)
          {
              bool success = MailService.SendMailBundleDownloadable(log, data.RecipientEMail, data.RecipientLanguage);
              if (!success)
              {
                log.LogError("Error sending mail.");
              }
          }
      }
      else
      {
        log.LogError("Error while receiving Event Grid event. Wrong event type or topic: " + eventGridEvent.ToString());
      }
    }
  }
}
