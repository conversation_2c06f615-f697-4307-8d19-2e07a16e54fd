using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Security.Cryptography.X509Certificates;
using AzureFunctions.Extensions.Swashbuckle.Settings;
using Dornbracht.Api.Pdh.Configuration;
using Dornbracht.Api.Pdh.Services;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;
using Dornbracht.Functions.Extensions.Swagger;
using Dornbracht.Api.Pdh.Options;
using Microsoft.Extensions.Configuration;

[assembly: FunctionsStartup(typeof(Dornbracht.Api.Pdh.Startup))]
namespace Dornbracht.Api.Pdh
{
  public class Startup : FunctionsStartup
  {

    public override void Configure(IFunctionsHostBuilder builder)
    {
      var baseUrl = Environment.GetEnvironmentVariable("API_BASE_URL", EnvironmentVariableTarget.Process);
      var hostKey = Environment.GetEnvironmentVariable("SWAGGER_JSON_FUNCTION_KEY", EnvironmentVariableTarget.Process);

      builder.AddFunctionsSwagger(Assembly.GetExecutingAssembly(), baseUrl, "v1/products/swagger/json", hostKey, true, (options) =>
      {
        options.Title = "Dornbracht PDH API";
        options.XmlPath = "Dornbracht.Api.Pdh.xml";
        options.Documents = new[]
                      {
                    new SwaggerDocument
                    {
                        Name = "v1",
                        Title = "Dornbracht PDH API",
                        Description = "Swagger documentation for the Dornbracht PDH API function layer.",
                        Version = "v1"
                    }
          };
      });

      builder.Services.AddHttpClient(HttpClientNames.PDH, client => client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json")))
      .ConfigurePrimaryHttpMessageHandler(() =>
      {
        var cert = Environment.GetEnvironmentVariable("DORNBRACHT_PDH_CLIENT_CERTIFICATE", EnvironmentVariableTarget.Process);
        var password = Environment.GetEnvironmentVariable("DORNBRACHT_PDH_CLIENT_CERTIFICATE_PASSWORD", EnvironmentVariableTarget.Process);

        var certificate = new X509Certificate2(Convert.FromBase64String(cert), string.IsNullOrEmpty(password) ? string.Empty : password, X509KeyStorageFlags.MachineKeySet);

        var handler = new HttpClientHandler();
        handler.ClientCertificates.Add(certificate);
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) =>
              {
                return true;
              };
        return handler;
      });

      builder.Services.AddSingleton<PdhDataServiceClient>();
      builder.Services.AddSingleton<PdhProductListsServiceClient>();
      builder.Services.AddSingleton<PdhSearchServiceClient>();
      builder.Services.AddSingleton<PdhProductsServiceClient>();
      builder.Services.AddSingleton<PdhDocumentBundleServiceClient>();
      builder.Services.AddSingleton<SitemapService>();


      builder.Services.AddOptions<ProductsApiOptions>()
       .Configure<IConfiguration>((settings, configuration) =>
       {
         configuration.GetSection("productsApi").Bind(settings);
       });
    }
  }
}
