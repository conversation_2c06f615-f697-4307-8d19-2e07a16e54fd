using Dornbracht.Api.AzureSearch.Abstractions;
using Dornbracht.Api.AzureSearch.Services;
using Dornbracht.Functions.Extensions.Search;
using Dornbracht.Functions.Extensions.Storyblok;
using Microsoft.Azure.Functions.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection;

[assembly: FunctionsStartup(typeof(Dornbracht.Api.AzureSearch.Startup))]
namespace Dornbracht.Api.AzureSearch
{
  public class Startup : FunctionsStartup
  {
    public override void Configure(IFunctionsHostBuilder builder)
    {
      builder.AddSearchServiceRepository();
      builder.AddStoryblokRepository();
      builder.Services.AddHttpClient();
      builder.Services.AddSingleton<ISearchService, SearchService>();
    }
  }
}
