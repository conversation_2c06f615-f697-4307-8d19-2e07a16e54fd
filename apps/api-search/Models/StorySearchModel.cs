using Azure.Search.Documents.Indexes;

namespace Dornbracht.Api.AzureSearch.Models
{
  public class StorySearchModel
  {
    public const string IndexName = "globalsearch";

    [SimpleField(IsKey = true)]
    public string Id { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public int StoryId { get; set; }

    [SearchableField(IsFilterable = true, IsSortable = true)]
    public string Title { get; set; }

    [SearchableField(IsFilterable = true, IsSortable = true)]
    public string Description { get; set; }

    [SimpleField]
    public string Url { get; set; }

    [SimpleField]
    public string Image { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true)]
    public string Language { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = true, IsFacetable = true)]
    public bool ExcludeFromSearch { get; set; }

    [SimpleField(IsFilterable = true, IsSortable = false, IsFacetable = true)]
    public string[] Markets { get; set; }

    [SearchableField(IsFilterable = true, IsSortable = true)]
    public string Keywords { get; set; }
  }
}
