using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.AzureSearch.Abstractions;
using Dornbracht.Api.AzureSearch.Models;
using System.Web;
using Dornbracht.Functions.Extensions;

namespace Dornbracht.Api.AzureSearch.Functions
{
  public class GetSearchResultByText
  {
    public GetSearchResultByText(ISearchService searchService)
    {
      SearchService = searchService;
    }

    private ISearchService SearchService { get; init; }

    [FunctionName(nameof(GetSearchResultByText))]
    [Produces("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Function, "get", Route = "search/getsearchresult/{lang}/{text}")] HttpRequest req, string lang, string text, ILogger log)
    {
      if (string.IsNullOrEmpty(text))
      {
        return new BadRequestResult();
      }

      var searchText = HttpUtility.UrlDecode(text);
      var searchResult = await SearchService.SearchByText(searchText, lang);
      var results = searchResult.GetResultsAsync();

      var data = new List<StorySearchModel>();

      await foreach (var result in results)
      {
        data.Add(result.Document);
      }

      req.AddMaxAgeHttpCacheHeader(TimeSpan.FromHours(1));

      return new OkObjectResult(data);
    }
  }
}
