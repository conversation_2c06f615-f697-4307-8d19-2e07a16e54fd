using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Dornbracht.Api.AzureSearch.Abstractions;
using Microsoft.Extensions.Logging;
using Dornbracht.Functions.Extensions.Messaging;

namespace Dornbracht.Api.AzureSearch.Functions
{
  public class UpdateIndexOnPublishListener
  {
    public UpdateIndexOnPublishListener(ISearchService searchService)
    {
      SearchService = searchService;
    }
    private ISearchService SearchService { get; init; }

    [FunctionName(nameof(UpdateIndexOnPublishListener))]
    public async Task Run([ServiceBusTrigger("%ASB_PUBLISH_TOPIC_NAME%", "%ASB_PUBLISH_TOPIC_INDEX_SUBSCRIPTION_NAME%", Connection = "ASB_PUBLISH_TOPIC_CONNECTION_STRING")] PublishMessage message, ILogger log)
    {
      await SearchService.UpdateIndexAsync(message.StoryId, message.Action, log);
    }
  }
}
