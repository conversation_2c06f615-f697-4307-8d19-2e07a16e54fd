using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.AzureSearch.Abstractions;

namespace Dornbracht.Api.AzureSearch.Functions
{
  public class FullIndexRebuild
  {
    public FullIndexRebuild(ISearchService searchService)
    {
      SearchService = searchService;
    }

    private ISearchService SearchService { get; init; }

    /// <summary>
    /// Runs full search index rebuild daily at 1am
    /// </summary>
    [FunctionName(nameof(FullIndexRebuild))]
    public async Task Run([TimerTrigger("0 0 1 * * *")] TimerInfo timer, ILogger log)
    {
      if (timer.IsPastDue)
      {
        log.LogInformation("Full index search rebuild is running late!");
      }

      await SearchService.RebuildStoryIndexAsync(log);
    }
  }
}
