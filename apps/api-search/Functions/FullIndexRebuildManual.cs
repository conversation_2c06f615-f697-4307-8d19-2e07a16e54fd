using System.Threading.Tasks;
using Microsoft.Azure.WebJobs;
using Microsoft.Extensions.Logging;
using Dornbracht.Api.AzureSearch.Abstractions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.AspNetCore.Http;
using Dornbracht.Functions.Extensions;

namespace Dornbracht.Api.AzureSearch.Functions
{
  public class FullIndexRebuildManual
  {
    public FullIndexRebuildManual(ISearchService searchService)
    {
      SearchService = searchService;
    }

    private ISearchService SearchService { get; init; }

    /// <summary>
    /// Runs full search index rebuild manually
    /// </summary>
    [FunctionName(nameof(FullIndexRebuildManual))]
    public async Task<IActionResult> Run([HttpTrigger(AuthorizationLevel.Admin, "get", Route = "search/fullindexrebuild")] HttpRequest req, ILogger log)
    {
      log.LogInformation("Full index search rebuild was triggered manually!");

      await SearchService.RebuildStoryIndexAsync(log);
      req.AddNoCacheHeader();

      return new OkObjectResult("") { StatusCode = 202 };
    }
  }
}
