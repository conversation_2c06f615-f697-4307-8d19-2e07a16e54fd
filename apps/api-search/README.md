# Search Azure Functions

## Prerequisites

- Azure Functions VS Code Extensions
- [Azurite](https://marketplace.visualstudio.com/items?itemName=Azurite.azurite). This is required for running a non-HTTP-Trigger Function and [Azure Blob Storage](https://docs.microsoft.com/en-us/dotnet/api/overview/azure/Storage.Blobs-readme) locally!
- Add a firewall rule to allow communication over AMQP port (5671) with the Azure Service Bus.
- Start Azurite Blob Service.

## Configuration

- Create `local.settings.json` file with the following properties:

  ```json
  {
    "IsEncrypted": false,
    "Values": {
      "AzureWebJobsStorage": "UseDevelopmentStorage=true",
      "FUNCTIONS_WORKER_RUNTIME": "dotnet",
      "API_BASE_URL": "http://localhost:7074",
      "STORYBLOK_PREVIEW_API_TOKEN": "",
      "SEARCH_SERVICES_MANAGEMENT_KEY": "{search service management key}",
      "SEARCH_SERVICES_QUERY_KEY": "{search service query key}",
      "SEARCH_SERVICES_ENDPOINT": "https://{search service name}.search.windows.net",
      "ASB_PUBLISH_TOPIC_NAME": "web-publish-asbt-0-d-d50",
      "ASB_PUBLISH_TOPIC_INDEX_SUBSCRIPTION_NAME": "search_index_update",
      "ASB_PUBLISH_TOPIC_CONNECTION_STRING": "Endpoint=sb://{hostname}/;SharedAccessKeyName=PublishTopicAccessKey;SharedAccessKey={accessKey};EntityPath=web-publish-asbt-0-d-d50"
    },
    "Host": {
      "CORS": "*"
    }
  }
  ```

- Fill the environment variables with the appropriate values.
