using System.Threading.Tasks;
using Dornbracht.Api.AzureSearch.Abstractions;
using System.Collections.Generic;
using Dornbracht.Api.AzureSearch.Models;
using System.Text.Json;
using System.Linq;
using Azure.Search.Documents.Models;
using Dornbracht.Functions.Extensions.Languages;
using Azure.Search.Documents;
using Dornbracht.Functions.Extensions.Abstraction;
using Dornbracht.Functions.Extensions.Models.Storyblok;
using System;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.AzureSearch.Services
{
  public class SearchService : ISearchService
  {
    private const int StoriesPerPage = 100;
    private const int MaxPages = 5;
    protected const string DefaultHomePage = "home";

    public SearchService(ISearchRepository azureSearchServiceRepository, IStoryblokRepository storyblokRepository)
    {
      AzureSearchRepository = azureSearchServiceRepository;
      StoryblokRepository = storyblokRepository;
    }

    private ISearchRepository AzureSearchRepository { get; init; }
    private IStoryblokRepository StoryblokRepository { get; init; }

    /// <summary>
    /// Runs full text search on global search index
    /// </summary>
    public async Task<SearchResults<StorySearchModel>> SearchByText(string text, string language)
    {
      var locale = language.Split('-');
      var market = locale.Length > 1 ? locale[1].ToLower() : "xy";

      await AzureSearchRepository.CreateOrUpdateIndexAsync<StorySearchModel>(StorySearchModel.IndexName);
      var searchClient = AzureSearchRepository.GetSearchClient(StorySearchModel.IndexName, true);
      var searchOptions = new SearchOptions() { Filter = $"Language eq '{language}' and ExcludeFromSearch eq false and (not Markets/any() or Markets/any(market: market eq '{market}'))" };

      var result = await searchClient.SearchAsync<StorySearchModel>(text, searchOptions);

      return result;
    }

    /// <summary>
    /// Executes full index rebuild by all available Storyblok Stories
    /// </summary>
    public async Task RebuildStoryIndexAsync(ILogger log)
    {
      await AzureSearchRepository.CreateOrUpdateIndexAsync<StorySearchModel>(StorySearchModel.IndexName);

      var space = await StoryblokRepository.GetSpace();
      if (space is SpaceResult.Success spaceResult)
      {
        var client = AzureSearchRepository.GetSearchClient(StorySearchModel.IndexName, true);
        var batch = IndexDocumentsBatch.Create<StorySearchModel>();
        var modelsList = new List<StorySearchModel>();

        foreach (var language in LanguageRepository.StoryblokLanguages)
        {
          var stories = await FetchAllStories(language, spaceResult.Space);
          modelsList.AddRange(MapSearchModels(stories, log));
        }

        foreach (var model in modelsList)
        {
          batch.Actions.Add(IndexDocumentsAction.MergeOrUpload(model));
        }

        var storyIds = await AzureSearchRepository.GetFieldFromAllDocumentsAsync<StorySearchModel, string>(client, story => story.Id);
        foreach (var id in storyIds)
        {
          if (!modelsList.Any(r => r.Id == id))
          {
            batch.Actions.Add(IndexDocumentsAction.Delete(new StorySearchModel { Id = id }));
          }
        }

        await AzureSearchRepository.IndexDocumentsAsync(client, batch);
      }
    }

    /// <summary>
    /// Updates index asnychronously by Storyblok webhook action and given story ID. Runs for all Storyblok languages
    /// </summary>
    public async Task UpdateIndexAsync(long storyId, string action, ILogger log)
    {
      await AzureSearchRepository.CreateOrUpdateIndexAsync<StorySearchModel>(StorySearchModel.IndexName);
      var searchClient = AzureSearchRepository.GetSearchClient(StorySearchModel.IndexName, true);

      foreach (var language in LanguageRepository.StoryblokLanguages)
      {
        if (action == "published")
        {
          var story = await StoryblokRepository.GetStory(storyId, language);

          if (story is StoryResult.Success storySuccess && storySuccess.Story.IsValidPageType)
          {
            var searchModels = MapSearchModels(storySuccess.Story, log);
            await searchClient.MergeOrUploadDocumentsAsync(searchModels);
          }
        }
        else if (action == "unpublished" || action == "deleted")
        {
          var searchModel = new StorySearchModel() { Id = GetStorySearchModelId(storyId, language) };
          var models = new List<StorySearchModel>() { searchModel };

          await searchClient.DeleteDocumentsAsync(models);
        }
      }
    }

    private async Task<List<Story>> FetchAllStories(string storyblokLanguage, Space space)
    {
      if (!LanguageRepository.StoryblokLanguages.Contains(storyblokLanguage))
      {
        return null;
      }

      var storyList = new List<Story>();
      for (int page = 1; page <= MaxPages; page++)
      {
        await Task.Delay(TimeSpan.FromSeconds(1));
        var filter = $"starts_with={(storyblokLanguage == LanguageRepository.StoryblokDefaultUrlLanguage ? $"[{LanguageRepository.StoryblokDefaultLanguage}]" : storyblokLanguage)}/*&version=published&cv={space.Version}&per_page={StoriesPerPage}&page={page}";
        var stories = await StoryblokRepository.GetStories(filter);

        if (stories is Stories.Success storiesSuccess && storiesSuccess.StoriesList.Any())
        {
          storyList.AddRange(storiesSuccess.StoriesList.Where(story => story.IsValidPageType));
        }
        else
        {
          break;
        }
      }

      return storyList;
    }

    private static List<StorySearchModel> MapSearchModels(List<Story> stories, ILogger log)
    {
      var models = new List<StorySearchModel>();
      foreach (var story in stories)
      {
        models.AddRange(MapSearchModels(story, log));
      }

      return models;
    }

    private static List<StorySearchModel> MapSearchModels(Story story, ILogger log)
    {
      var models = new List<StorySearchModel>();

      if (!story.IsValidPageType)
      {
        return models;
      }

      var language = story.Language == LanguageRepository.StoryblokDefaultLanguage ? LanguageRepository.StoryblokDefaultUrlLanguage : story.Language;
      var urlLanguages = LanguageRepository.GetUrlLanguages(language);

      foreach (var urlLanguage in urlLanguages)
      {
        var metaData = new MetaTitle { Title = string.Empty, Description = string.Empty };

        if (story.Content.ContainsKey("meta_title") && story.Content["meta_title"].ValueKind == JsonValueKind.Object)
        {
          try
          {
            metaData = story.Content["meta_title"].Deserialize<MetaTitle>();
          }
          catch (Exception ex)
          {
            log.LogWarning(ex, "Not able to deserialize meta_title field for story id:" + story.Id);
          }
        }

        string url = urlLanguage;
        if (!story.Slug.ToLower().Equals(DefaultHomePage))
        {
          if (story.Language == LanguageRepository.StoryblokDefaultLanguage)
          {
            url = string.Concat(url, "/", story.FullSlug);
          }
          else
          {
            url = string.Concat(url, story.FullSlug.AsSpan(story.FullSlug.IndexOf('/')));
          }
        }

        models.Add(new StorySearchModel
        {
          Id = GetStorySearchModelId(story.Id, urlLanguage),
          StoryId = story.Id,
          Title = metaData.Title,
          Description = metaData.Description,
          Url = EnsureBeginningSlash(url),
          Image = story.Content.ContainsKey("meta_image") ? story.Content["meta_image"].ToString() : string.Empty,
          Language = urlLanguage,
          ExcludeFromSearch = story.Content.ContainsKey("exclude_from_search") ? story.Content["exclude_from_search"].GetBoolean() : false,
          Markets = story.Content.ContainsKey("markets") ? story.Content["markets"].EnumerateArray().Select(json => json.GetString()).ToArray() : null,
          Keywords = story.Content.ContainsKey("meta_keywords") ? story.Content["meta_keywords"].ToString() : string.Empty
        });
      }

      return models;
    }

    private static string GetStorySearchModelId(long storyId, string storyLanguage)
    {
      return $"{storyId}_{storyLanguage}";
    }

    protected static string EnsureBeginningSlash(string value)
    {
      return value.StartsWith('/') ? value : $"/{value}";
    }
  }
}
