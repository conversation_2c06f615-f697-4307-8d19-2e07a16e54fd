using System.Threading.Tasks;
using Azure.Search.Documents.Models;
using Dornbracht.Api.AzureSearch.Models;
using Microsoft.Extensions.Logging;

namespace Dornbracht.Api.AzureSearch.Abstractions
{
  public interface ISearchService
  {
    public Task<SearchResults<StorySearchModel>> SearchByText(string text, string language);

    public Task RebuildStoryIndexAsync(ILogger log);

    public Task UpdateIndexAsync(long storyId, string action, ILogger log);
  }
}
