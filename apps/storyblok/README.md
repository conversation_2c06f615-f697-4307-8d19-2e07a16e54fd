# Dornbracht Storyblok

This project manages all Storyblok components. This is based on the [Storyblok CLI](https://github.com/storyblok/storyblok).

The component sync process is currently a manual step on each release. (Missing feature, see [Github issue](https://github.com/storyblok/storyblok/issues/577))

## Storyblok commands

Install storyblok globally.

```bash
npm install -g storyblok
```
Install dependencies.

```bash
npm install
```

### Pull components

```bash
npm run pull
```

Pulls all components from DEV space to file system.

### Push components

```bash
npm run push-dev
```

```bash
npm run push-test
```

```bash
npm run push-prod
```

Pushes components from file system to the respective environment. This overwrites all manual changes that are not pull to file system.

### Sync content

```bash
npm run sync-content-dev
```

```bash
npm run sync-content-test
```

Synchronizes **all** content (folders, stories, datasources and roles) from PROD to DEV or TEST environment.

## Storyblok spaces

### Required storyblok apps

The following apps are required in all spaces:

* seo-metatags: https://www.storyblok.com/apps/seo (DOCOR-68)

## Known issues with the Storyblok CLI

If you receive a storyblok cli error message such as "Request failed with status code 401", you should log out and then log in again.

* Logout from the Storyblok cli

  ```bash
  storyblok logout
  ```

* Login to the Storyblok cli

  ```bash
  storyblok login
  ```

See: https://github.com/storyblok/storyblok
