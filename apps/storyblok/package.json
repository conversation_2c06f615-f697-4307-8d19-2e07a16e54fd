{"name": "storyblok", "version": "1.0.0", "license": "MIT", "scripts": {"pull": "storyblok pull-components --space 156805 && renamer --force --find .156805.json --replace .json *", "push-dev": "storyblok push-components ./components.json --presets-source ./presets.json --space 156805", "push-test": "storyblok push-components ./components.json --presets-source ./presets.json --space 156806", "push-prod": "storyblok push-components ./components.json --presets-source ./presets.json --space 156807", "sync-content-dev": "storyblok sync --type folders,stories,datasources,roles --source 156807 --target 156805", "sync-content-test": "storyblok sync --type folders,stories,datasources,roles --source 156807 --target 156806", "login": "storyb<PERSON>k login", "logout": "storyblok logout"}, "devDependencies": {"renamer": "~4.0.0"}}