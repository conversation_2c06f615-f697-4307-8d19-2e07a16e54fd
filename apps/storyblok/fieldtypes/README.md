# Storyblok Field-Type Plugins

This directory contains all project specific field-type plugins:
* [picturepark-asset](./picturepark-asset/README.md)

## Prerequisites
```
npm install
```

## Maintain existing plugin
### Local development
1. Run local server
    1.  ```
        npm run serve
        ```
    1. Copy the url of the locally started development server from the terminal (eg. http://localhost:8081/).
    1. Navigate to the custom field type in storyblok (Partner Portal > Field-Types > picturepark-asset).
    1. Set "Enable local development mode" to true
    1. Paste the url into the input field "Local Development URL"
    1. From now on you can check the source code modifications live

Tip: The tab "Output" shows the raw data of the field at the story at any time, this is also a good option to debug return values of Picturepark.

### Deployment
1. Compile and minify
    ```
    npm run build
    ```
1. Navigate in the file system to /dist/export.js
1. Copy the complete content of export.js into the custom field type of storyblok (Partner Portal > Field-Types > picturepark-asset)
1. Save and publish the new/update field-type. 

Attention: The field type is automatically releases for all environments.

## Create new plugin
To create a new field plugin, open a terminal and run
```
npx @storyblok/field-plugin-cli@beta
```