<template>
  <div class="uk-position-relative">
    <div class="assets_picturepark_validation" v-if="model.validationMsg != null">
      <div class="assets__item uk-flex sortable__handle">
        {{ model.validationMsg }}
      </div>
    </div>

    <div class="assets__wrap uk-margin-bottom-remove uk-margin-top-remove">
      <div class="assets sortable" v-if="model.assets == null || model.assets.length == 0">
        <div class="assets__item uk-flex sortable__handle">No asset selected</div>
      </div>

      <div class="assets sortable" v-if="model.assets != null && model.assets.length > 0">
        <div class="assets__item uk-flex sortable__handle" v-for="asset in model.assets" :key="asset.sortorder">
          <div class="assets__picturepark_col_left">
            <a class="link link--inverse" @click.prevent="editAsset(asset)">
              <img class="assets__item-icon--grab" v-bind:src="getThumbnail(asset)"/>
            </a>
          </div>
          <div class="assets__picturepark_col_center">
            <div class="assets__filename">
              <a class="link link--inverse" @click.prevent="editAsset(asset)">
                {{ getFilename(asset) }}
              </a>
            </div>
            <div class="assets__metadata">
              {{ getMetadata(asset) }}
              <a
                class="assets__item-trash"
                title="Share"
                v-bind:href="options.pictureparkUrl + '/shares/' + asset.shareId + '/items'"
                target="_blank"
                ><i class="uk-icon-search"></i
              ></a>
            </div>
          </div>
          <div class="assets__picturepark_col_right">
            <a title="Remove" @click.prevent="removeAsset(asset)">
              <i class="uk-icon-minus-circle"></i>
            </a>
            <a title="Edit" @click.prevent="editAsset(asset)">
              <i class="uk-icon-edit"></i>
            </a>
          </div>
        </div>
      </div>

      <div class="uk-flex">
        <a class="uk-form-file assets__btn" @click.prevent="clearSelection">
          <span class="util__nobreak"> <i class="uk-icon-minus-circle uk-margin-small-right"></i> Clear </span>
          <div></div>
        </a>
        <a class="assets__btn" @click.prevent="openSelection"> <i class="uk-icon-image"></i> Asset Picker </a>
      </div>
      <div></div>
    </div>
  </div>
</template>

<script>
import { showCustomContentPicker } from './utils/contentEditor.js';
import { unlockShare } from './utils/share.js';
import { createMissingConversionPresets, loadConversionPresetTemplates } from './utils/conversionPreset.js';
import { loadAssets, updateAssets } from './utils/content.js';
import { validateSelection } from './utils/validation.js';

import { showContentPicker } from '@picturepark/sdk-v1-pickers';
import { AccessTokenAuthClient } from '@picturepark/sdk-v1-fetch';

/************** CONFIG ***************/
const config = {
  embedName: 'storyblok',
  sourceFormatThumbnail: 'ThumbnailSmall',
  sourceFormatOriginal: 'Original',
  sourceFormatPreview: 'Preview',  
  layer: {
    seo: {
      id: 'namingsUndSeo',
      alt: 'altText',
      filename: 'seoTitle',
      schemaId: 'NamingsUndSeo'
    },
    imageAnalytics: {
      id: 'imageAnalytics',
      schemaId: 'ImageAnalytics'
    }
  },
  assetTypes: {
    image: {
      identifier: 'image',
      extensions: ['.tif', '.jpg', '.webp', '.png'],
      filter:
        '?searchMode=and'+
        '&filters=[{"aggregationName":"contentSchemaId","filter":{"field":"contentSchemaId","term":"ImageMetadata","kind":"TermFilter"},"kind":"AggregationFilter"}]'+
        '&sort=[{"field":"audit.modificationDate","direction":"Desc"}]',
      outputFormats: ["DownloadOriginalsRGB"],
      outputFormatsUnlockable: ['DownloadOriginalsRGB']
    },
    file: {
        identifier:"file",
        extensions: ['.pdf', '.zip'],
        filter:
        '?searchMode=and'+
        '&filters=[{"aggregationName":"contentSchemaId","filter":{"field":"contentSchemaId","term":"DocumentMetadata","kind":"TermFilter"},"kind":"AggregationFilter"},'+
        '{"aggregationName":"contentSchemaId","filter":{"field":"contentSchemaId","term":"FileMetadata","kind":"TermFilter"},"kind":"AggregationFilter"}]'+
        '&sort=[{"field":"audit.modificationDate","direction":"Desc"}]',
        outputFormats: ["Pdf", 'Preview', 'Original'], //Original=PDF
        outputFormatsUnlockable: ['Preview'],
    }
  },
  fieldTypes: {
    single: { identifier: 'single' },
    list: { identifier: 'list' }
  },
  monthDiffExpiration: 2,
  picturparkLanguages: ['en', 'de'],
  languageMappings: {
    en: 'en',
    'de-de': 'de'
  }
};

const pickerSetting = {
  debug: false,
  width: 1200,
  height: 700,
  embedName: config.embedName,
  returnType: 'embed',
  enableMediaEditor: false
};

/************** ENV ***************/
function initEnvironment(spaceId, storyId, model, options) {
  options.spaceId = spaceId;

  //PROD=156807,DEV=156805,Test=156806
  if (spaceId == '156807') {
    options.env = 'prod';
    options.pictureparkUrl = 'https://media.dornbracht.com';
    options.authClient = new AccessTokenAuthClient(
      'https://dornbrachtgroup.api.picturepark.com',
      'dornbrachtgroup',
      '225F43B4649008F2E7FA73A128C5A362789E6F464B7D778B3857724F274C41B0'
    );
  } else {
    options.env = storyId == null ? 'debug' : spaceId == '156806' ? 'test' : 'dev';
    options.pictureparkUrl = 'https://dornbrachtgroup.current-picturepark.com';
    options.authClient = new AccessTokenAuthClient(
      'https://dornbrachtgroup.api.current-picturepark.com',
      'dornbrachtgroup',
      '0ED9B5E7F73CA0F739933F5053A09FA5BCEAFC6DDD3BD08282635046FE6B7CF0'
    );
  }

  loadConversionPresetTemplates(options, model);
}

export default {
  mixins: [window.Storyblok.plugin],
  methods: {
    initWith() {
      // These are the values which our custom field will return.
      return {
        plugin: 'picturepark-asset', // needs to be equal to your storyblok plugin name
        assets: [],
        validationMsg: null
      };
    },
    pluginCreated() {
      //some defaults
      if (this.options.fieldType != config.fieldTypes.list.identifier) {        
        this.options.fieldType = config.fieldTypes.single.identifier;
        this.options.limit = 1;
      }
      if(this.options.assetType == null){
        this.options.assetType = config.assetTypes.image.identifier;
      }          
      
      if (this.options.assetType === config.assetTypes.image.identifier) {
        this.options.assetTypeSettings = config.assetTypes.image;
      }
      else if (this.options.assetType === config.assetTypes.file.identifier) {
        this.options.assetTypeSettings = config.assetTypes.file;
      }
      
      initEnvironment(this.spaceId, this.storyId, this.model, this.options);
    },
    clearSelection() {
      this.model.assets = [];
      this.model.validationMsg = null;
    },
    removeAsset(asset) {
      const index = this.model.assets.indexOf(asset);
      if (index > -1) {
        this.model.assets.splice(index, 1);
      }
    },
    editAsset(asset) {
      var currentOptions = this.options;
      var currentModel = this.model;

      const index = this.model.assets.indexOf(asset);
      if (index > -1) {
        var urlContentPicker = currentOptions.pictureparkUrl + '/contents/marketing/' + asset.contentId + '/metadata';

        showCustomContentPicker(urlContentPicker, pickerSetting).then(function () {
          updateAssets(currentModel.assets, currentOptions, config);
        });
      }
    },
    openSelection() {
      var currentModel = this.model;
      var currentStoryId = this.storyId;
      var currentOptions = this.options;

      //1. Open asset picker
      var urlContentPicker = currentOptions.pictureparkUrl + '/contentPicker/' + currentOptions.channel + '/' + currentOptions.assetTypeSettings.filter;
      showContentPicker(urlContentPicker, pickerSetting).then(function (result) {
        if (result) {
          var embed = result.embed;
          // console.log("Embed", embed)

          //2. Load relevant asset data and write to story
          var sortorder =
            currentOptions.fieldType == config.fieldTypes.single
              ? 1
              : currentModel.assets.length == 0
              ? 1
              : currentModel.assets[currentModel.assets.length - 1].sortorder + 1;
          loadAssets(embed, sortorder, currentOptions, config).then((selectedAssets) => {
            //3. Validate
            currentModel.validationMsg = validateSelection(
              selectedAssets,
              embed.contents,
              currentModel.assets.length,
              currentOptions,
              config
            );
            if (currentModel.validationMsg == null) {
              //4. Update share (unlock source format and persist story reference)
              unlockShare(embed.id, embed.contents, currentStoryId, currentOptions, config, currentModel).then(() => {
                if (currentModel.validationMsg == null) {
                  //5. In case of single selection clear current selection to replace it
                  if (currentOptions.fieldType == config.fieldTypes.single.identifier) {
                    currentModel.assets = [];
                  }

                  //6. update model
                  currentModel.assets = currentModel.assets.concat(selectedAssets);

                  //7. Create Conversion Presets
                  if(currentOptions.assetType === config.assetTypes.image.identifier){
                    createMissingConversionPresets(selectedAssets, currentModel, currentOptions, config);
                  }
                }
              });
            }
          });
        }
      });
    },
    getFilename(asset) {
      var lang = this.sbLanguage == 'default' ? 'en' : this.sbLanguage;
      if(lang === 'de'){
        lang = 'de-de'; // unfortunatly FE language is 'de-de' and BE is 'de'
      }
      return asset.filename[lang] || asset.source.filename;
    },
    getMetadata(asset){
      var metadata = asset.source.extension;
      if(asset.source.width != null && asset.source.height != null) {
        metadata = metadata + ', ' +asset.source.width+' x '+asset.source.height;
      }
      return metadata;
    },
    getThumbnail(asset){
      var url = "";
      if(asset.source.thumbnail){
        url = asset.source.thumbnail + '?width=60&height=40'
      }
      return url;
    }
  },
  watch: {
    model: {
      handler: function (value) {
        this.$emit('changed-model', value);
      },
      deep: true
    }
  }
};
</script>

<style>
/* TODO FHE https://getuikit.com/docs/introduction */
.assets__picturepark_col_left {
  margin-right: 5px;
  margin-top: 4px;
  width: 60px;
}

.assets__picturepark_col_center {
  font-size: 14px;
  width: 100%;
  height: 50px;
}

.assets__picturepark_col_right {
  width: 20px;
}

.assets__metadata {
  color: #9d9fa7;
}

.assets__filename {
  height: 20px;
  overflow: hidden;
}

.assets_picturepark_validation {
  color: red;
  font-weight: bold;
}
</style>
