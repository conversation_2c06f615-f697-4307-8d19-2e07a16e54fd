import { ContentClient, ConversionPresetTemplateClient } from '@picturepark/sdk-v1-fetch';

/************** Create Missing Conversion Presets ***************/
function createMissingConversionPresets(selectedAssets, model, options, config){      
  try{
    for(var i=0;i<selectedAssets.length;i++){
      const asset = selectedAssets[i]     

      var conversionsToAdd = [];
      var existingConversion = [];
      for (var ii=0;ii< options.requiredConversions.length;ii++) {
        const requiredConversion = options.requiredConversions[ii];
        if(!asset.conversions[requiredConversion.identifier]) {
          try {                               
            let calculatedConversionString = generateConversionString(requiredConversion, asset);
            if(calculatedConversionString!=null){
              conversionsToAdd.push({
                  conversionPresetTemplateId:requiredConversion.templateId,
                  conversionString: calculatedConversionString,
                  outputFormatId:requiredConversion.outputFormatId,
                  names: {"x-default":requiredConversion.identifier,"de":requiredConversion.identifier,"en":requiredConversion.identifier}
              });

              //update model - don't want to wait for real update because of preview performance.
              //and the conversion string works even if it was not persisted at content item
              asset.conversions[requiredConversion.identifier] = cleanupConversionString(calculatedConversionString);
            }
          } catch (error) {
            console.log(error);
            model.validationMsg = "Error generating conversion for '"+requiredConversion.identifier+"': "+error.message;
          }        
        } else {
          existingConversion.push({
              conversionString:asset.conversions[requiredConversion.identifier],
              outputFormatId:requiredConversion.outputFormatId,
              names: {"x-default":requiredConversion.identifier,"de":requiredConversion.identifier,"en":requiredConversion.identifier}
          });
        }
      }

      if(conversionsToAdd.length > 0){
        var contentClient = new ContentClient(options.authClient);
        var updateRequest = {
          layerSchemaIds:[config.layer.imageAnalytics.schemaId],
          layerSchemasUpdateOptions:"Merge",
          metadata: {
            imageAnalytics:{
              conversionPresets: conversionsToAdd.concat(existingConversion)
            } 
          }
        }        
        contentClient.updateMetadata(asset.contentId, updateRequest, ["Metadata"], true).catch((ex) =>{ 
          var msg =  "Conversion presets could not be updated...";
          if(ex.exceptionMessage){
            msg = ex.exceptionMessage;
          }          
          console.log(msg, ex)
          model.validationMsg = msg;
          model.assets = [];
        })
      }
    }
  } catch (error) {
    console.log(error);
    model.validationMsg = "Unexpected error when creating conversion presets...";
    model.assets = [];
  }
}

/**
 * !!! Attention !!! Changes you make here must also be made in the sync metadata webjob
 * '/crop:681x681,fp:1051,320/UPDATED-animal.jpg#aspectRatio:1x1'  -->  '/crop:681x681,fp:1051,320'
 */
function cleanupConversionString(conversionString){              
  //remove #aspectRatio
  var indexOfHash = conversionString.indexOf('#');
  if(indexOfHash > 0) {
    conversionString = conversionString.substring(0, indexOfHash);        
  }

  //remove filename
  var lastIndexOf = conversionString.lastIndexOf('.');
  if(lastIndexOf > 0) {
    lastIndexOf = conversionString.lastIndexOf('/');
    conversionString = conversionString.substring(0, lastIndexOf)
  }

  //TODO FHE
  if(conversionString.indexOf('/')!=0){
    conversionString = '/'+conversionString
  }
  return conversionString;
}

/**
 * E.g. crop:4000x1715,fp:0,0/#aspectRatio:21x9
 */
function generateConversionString(conversionPreset, asset){
    var conversionString = null;

      if(asset.source.width == 0 || asset.source.height == 0) {
        throw new Error("Asset dimension not valid (w="+asset.source.width+", h="+asset.source.height+")");
      }

      // 1. width and height to crop
      var cropX = asset.source.width;
      var cropY = asset.source.height;      
      var ratioOriginal = asset.source.width / asset.source.height;
      if (ratioOriginal > conversionPreset.aspectRatio.factor) {
        cropX = Math.round(asset.source.height * conversionPreset.aspectRatio.factor);
      }
      else if (ratioOriginal < conversionPreset.aspectRatio.factor){
        cropY =  Math.round(asset.source.width / conversionPreset.aspectRatio.factor)
      }

      if(cropX == 0 || cropY == 0) {
        throw new Error("Cropping coordinates not valid ("+cropX+"x"+cropY+")");
      }

      //2. Starting cordinates
      var coordinateX = getCroppingCoordinate(cropX, asset.source.width);    
      var coordinateY = getCroppingCoordinate(cropY, asset.source.height);
   
      //3. compose string
      conversionString = "/crop:"+cropX+"x"+cropY+",fp:"+coordinateX+","+coordinateY+"/#aspectRatio:"+conversionPreset.aspectRatio.x+"x"+conversionPreset.aspectRatio.y;
    return  conversionString;
}

function getCroppingCoordinate(croppingWidthOrHeight, originalWidthOrHeight)
{
    var coordinate = originalWidthOrHeight/2 - croppingWidthOrHeight/2;    
    if (coordinate < 0){
      //Fallback if crop box is out of range
      coordinate = 0;
    }else if ((parseFloat(coordinate) + parseFloat(croppingWidthOrHeight)) > originalWidthOrHeight){
        coordinate = originalWidthOrHeight - croppingWidthOrHeight;
    }
    return Math.round(coordinate);
}

/************** Load by Conversion Preset Templates ***************/
/**
 * load conversion preset templates from picturepark
 */
function loadConversionPresetTemplates(options, model){
  new ConversionPresetTemplateClient(options.authClient).search( { limit:20 }).then(result => {
    options.requiredConversions = [];
    
    var converstionPresetTemplates = result.results;
    for (var j = 0; j < converstionPresetTemplates.length; j++) {
      var converstionPresetTemplate = converstionPresetTemplates[j];
      var identifier = converstionPresetTemplate.names['x-default'];
      options.requiredConversions.push({
        identifier: identifier,
        outputFormatId: converstionPresetTemplate.outputFormatId,
        templateId:converstionPresetTemplate.id,
        aspectRatio:extractAspectRatio(converstionPresetTemplate.template, model),
      });      
    }
  });
}

/**
 * Expects a conversion string like 'crop:4000x4000,fp:0,0/#aspectRatio:1x1'
 */
function extractAspectRatio(conversionString, model){    
  try{
    var aspectRatio = conversionString.split("#")[1].replace("aspectRatio:","").split("x"); //TODO absichern  

    return {
      x:aspectRatio[0],
      y:aspectRatio[1],
      factor:aspectRatio[0]/aspectRatio[1]
    };  
  }
  catch(error){    
    console.log(error);
    model.validationMsg = "Invalid Conversion Preset Template configured: '"+conversionString+"'.";    
  }
}

export { cleanupConversionString, loadConversionPresetTemplates, createMissingConversionPresets }