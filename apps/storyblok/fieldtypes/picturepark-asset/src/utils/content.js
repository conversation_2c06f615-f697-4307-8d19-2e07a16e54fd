import { ContentClient } from '@picturepark/sdk-v1-fetch';
import { cleanupConversionString } from './conversionPreset.js'

async function loadAssets(embed, sortorder, options, config) {
  var selectedAssets = [];  
  for (let i = 0; i < embed.contents.length; i++) {
    var outputs = embed['contentSelections'][i]['outputs'];
    var asset = extractAsset(outputs, sortorder, options, config);    

    await loadAsset(asset, embed.contents[i].contentId, embed.id, embed.data.token, options, config);    

    selectedAssets.push(asset);
    sortorder++;
  }
  return selectedAssets;
}

async function updateAssets(assets, options, config) {
  for (let i = 0; i < assets.length; i++) {
    await loadAsset(assets[i], assets[i].contentId, assets[i].shareId, assets[i].shareToken, options, config);    
  }
}

async function loadAsset(asset, contentId, shareId, shareToken, options, config) {
  await new ContentClient(options.authClient).get(contentId, ['Metadata']).then((content) => {
    if (content) {         
      //seo layer
      if (content.metadata[config.layer.seo.id]) {
        var seoLayer = content.metadata[config.layer.seo.id];                    
        asset.filename = transformLanguages(seoLayer[config.layer.seo.filename], config);
        asset.alt = transformLanguages(seoLayer[config.layer.seo.alt], config);
      }

      //set filename fallback to original filename
      if(!asset.filename) {
        asset.filename = {};
        for (let language in config.languageMappings) {
          asset.filename[language] = asset.source.filename;
        }
      }

      //presets layer
      asset.conversions = {};
      if (content.metadata[config.layer.imageAnalytics.id]) {
        let conversionPresets = content.metadata[config.layer.imageAnalytics.id].conversionPresets;
        if (conversionPresets) {            
          for (var j = 0; j < conversionPresets.length; j++) {
            var conversion = conversionPresets[j];
            var identifier = conversion.names['x-default'];
            
            asset.conversions[identifier] = cleanupConversionString(conversion.conversionString);
          }
        }
      }

      asset.shareId = shareId;
      asset.shareToken = shareToken;
      asset.contentId = content.id;
      asset.modificationDate = content.audit.modificationDate;
    }
  });
  return asset;
}

function extractAsset(outputs, sortorder, options, config) {
  // console.log("Outputs:",outputs)
  var asset = {
    sortorder: sortorder,
    source:{},
    url:undefined
  };
  for (var i = 0; i < outputs.length; i++) {
    var output = outputs[i];
     
    //Thumbnail
    if (output.outputFormatId == config.sourceFormatThumbnail) {
      asset.source.thumbnail = output.viewUrl;
    }

    //Original
    if (output.outputFormatId == config.sourceFormatOriginal) { //Temporäre Lösung: DOCOR-231
      asset.source.height = output.detail.height;
      asset.source.width = output.detail.width;
      asset.source.fileSizeInBytes = output.detail.fileSizeInBytes;
    }

    //Preview
    if (output.outputFormatId == config.sourceFormatPreview) {
      asset.source.preview = output.viewUrl;
    }

    //Pdf, Original (Zip) or DownloadOriginalsRGB
    if(options.assetTypeSettings.outputFormats.indexOf(output.outputFormatId)!=-1) {
      //remove extension
      var filename = output.detail.originalFileName;
      var lastIndexOf = filename.lastIndexOf('.');
      if(lastIndexOf > 0) {        
        filename = filename.substring(0, lastIndexOf)
      }
      asset.source.filename = filename;

      asset.source.extension = output.detail.fileExtension;
      asset.url = output.viewUrl.replace(/\/$/, ""); //remove trailing slash      
    }  
  }
  return asset;
}

function transformLanguages(value, config){
  var result = null;
  if(value){
      //set fallback to x-default
      for(let language in config.picturparkLanguages){
        value[language] = value[language] || value["x-default"];
      }

      //map picturepark language to storyblok language
      result = {}  
      for (let key in config.languageMappings) {
        result[key] = value[config.languageMappings[key]]; 
      }    
  }
  return result;
}

export { loadAssets, updateAssets }