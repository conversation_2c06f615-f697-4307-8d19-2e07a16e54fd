// Extract from the original asset picker of picturepark https://github.com/Picturepark/Picturepark.SDK.TypeScript/blob/master/src/picturepark-sdk-v1-pickers/src/index.ts
export function showCustomContentPicker(serverUrl, settings) {
    return new Promise(function(resolve) {
      const w = settings.width==null? 1281:settings.width;
      const h = settings.height==null? 800 :settings.height;
  
      var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : (screen).left;
      var dualScreenTop = window.screenTop != undefined ? window.screenTop : (screen).top;
  
      var width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
      var height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;
  
      var left = ((width / 2) - (w / 2)) + dualScreenLeft;
      var top = ((height / 2) - (h / 2)) + dualScreenTop;
    
      var popup = window.open(serverUrl,
        '_blank', 'width=' + w + ', height=' + h + ', top=' + top + ', left=' + left + ',status=no,location=no,toolbar=no');
  
      var callbackCalled = false;
      const checkClosed = () => {
        if (popup.closed) {
          if (!callbackCalled) {
            callbackCalled = true;
            resolve(undefined);
          }
        } else {
          setTimeout(() => checkClosed(), 100)
        }
      }

      checkClosed();
    });
  }