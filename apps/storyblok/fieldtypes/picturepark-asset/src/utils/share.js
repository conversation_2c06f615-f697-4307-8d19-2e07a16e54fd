import { ShareClient } from '@picturepark/sdk-v1-fetch';

/************** Initialize share ***************/
/**
 * Make resizing in fronted possible: locked = false: 
 */
async function unlockShare(shareId, contents, storyId, options, config, model) {
    var outputFormats = getOutputFormats(contents, options)
    for(var i=0;i<outputFormats.length;i++){
        if(options.assetTypeSettings.outputFormatsUnlockable.indexOf(outputFormats[i].outputFormatId)!=-1){  
            outputFormats[i].locked = false;
        }
    }
    await updateShare(shareId, contents, storyId, options, config, model);
}

/**
 * expiration = today + x month
 * description = story json
 */
 async function updateShare(shareId, contents, storyId, options, config, model){
    var shareRequest = {
        name: config.embedName,
        description: JSON.stringify({
                                    story:storyId,
                                    space:options.spaceId,
                                    env:options.env
                                    }),
        contents: contents,    
        outputAccess: 'Full',
        kind: 'ShareEmbedUpdateRequest',
    };

    var expDate = new Date();
    if(storyId){
        expDate.setMonth(expDate.getMonth() + config.monthDiffExpiration);
    }
    else{
        expDate.setDate(expDate.getDate()+1)
    }
    shareRequest.expirationDate = expDate;

    var shareClient = new ShareClient(options.authClient);
    var promise = shareClient.update(shareId, shareRequest).catch(function (error) {
        console.log(error);
    });
    let result = await promise;
    await promise;
    
    for(let i=0;i<500;i++){
        await setTimeout(() => { }, 50)
        if((await checkIfShareContentsAreUnlocked(shareClient, shareId, options))){
            return result
        }
    }

    model.validationMsg = "Could not unlock all share contents. Please try again.";
}

async function checkIfShareContentsAreUnlocked(shareClient, shareId, options){
    return await shareClient.get(shareId).then((share) => {                    
        var outputFormats = getOutputFormats(share.contents, options)
        var countUnlocked = 0;
        for(var i=0;i<outputFormats.length;i++){
            if(!outputFormats[i].locked){
                countUnlocked += 1;
            }
        }
        return countUnlocked==share.contents.length;
    });
}

function getOutputFormats(contents, options){
    var outputFormats = []
    for (var ci = 0; ci < contents.length; ci++) {
        for (var pi = 0; pi < contents[ci].conversionPresets.length; pi++) {
            var conversionPreset = contents[ci].conversionPresets[pi];   
            if(options.assetTypeSettings.outputFormats.indexOf(conversionPreset.outputFormatId)!=-1){  
                outputFormats.push(conversionPreset); 
            }
        }
    }
    return outputFormats;
}

/************** Cleanup shares ***************/
function deleteShare(asset, options) {  
    deleteShares([asset], options);  
}
  
function deleteShares(assets, options) {  
    var uniqueIds = [];
    for(var i=0;i<assets.length;i++){
        var id = assets[i].shareId
        if(uniqueIds.indexOf(id)==-1) {
        uniqueIds.push(id)
        }
    }

    var shareClient = new ShareClient(options.authClient);   
    shareClient.deleteMany({ids:uniqueIds}).catch(error =>{ console.error(error) });      
}
  
function deleteContentFromShare (asset, storyId, options) {    
    var shareClient = new ShareClient(options.authClient);  
    shareClient.get(asset.shareId).then((share) => {    
        if(share.contents.length>1){  
        for (var i = 0; i < share.contents.length; i++) {
            var content = share.contents[i];
            if(content.contentId==asset.contentId){                    
            share.contents.splice(i, 1);
            break;
            }
        }     
            updateShare(share.id, share.contents, storyId, options);
        }      
        else if(share.contents.length==1){
            deleteShare(asset, options);
        }
    });
}

export { unlockShare,  deleteShares, deleteShare, deleteContentFromShare, getOutputFormats}