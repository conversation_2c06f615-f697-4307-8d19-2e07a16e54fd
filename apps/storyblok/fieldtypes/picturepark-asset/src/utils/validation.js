import { deleteShares, getOutputFormats } from './share.js'

function validateSelection(selectedAssets, contents, selectedBefore, options, config){
    var validationMsg = null;

    //fieldtype (list vs single)
    var selectedAssetsOverall = selectedAssets.length    
    if(options.fieldType != config.fieldTypes.single.identifier){
        selectedAssetsOverall += selectedBefore;
    }
    if(options.limit && selectedAssetsOverall > options.limit){
        validationMsg = "Please select max. "+options.limit+" asset(s)."
    }

    //assettype (image vs document)
    if(validationMsg == null){
        for(var i=0;i<selectedAssets.length;i++) {
            var asset = selectedAssets[i];
            if(options.assetTypeSettings.extensions.indexOf(asset.source.extension)==-1){
                validationMsg = "Please select valid asset types ("+options.assetTypeSettings.extensions.join(", ")+")."
            }
        }
    }

    //validate outputformats of newly created share (bug in picturepark)
    if(validationMsg == null){
        var outputFormats = getOutputFormats(contents, options)
        var countShareContents = outputFormats?outputFormats.length:0;
        if(countShareContents < selectedAssets.length){
            validationMsg = "OutputFormats of share not loaded, please try again."
        }
    }

    if(validationMsg != null){
        //cleanup shares in picturepark
        deleteShares(selectedAssets, options);
    }
    return validationMsg;
}

export { validateSelection }