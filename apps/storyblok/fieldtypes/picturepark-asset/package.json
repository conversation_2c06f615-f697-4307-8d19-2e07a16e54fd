{"name": "storyblok-fieldtype", "description": "A Storyblok Field-Type Plugin Project", "author": {"name": "comspace", "email": "<EMAIL>", "url": "https://www.comspace.de/"}, "contributors": [{"name": "Team Nibbler", "email": "<EMAIL>", "url": "https://www.comspace.de/"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://www.comspace.de/"}], "version": "1.1.0", "private": true, "scripts": {"dev": "npm run serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@picturepark/sdk-v1-fetch": "^11.7.0", "@picturepark/sdk-v1-pickers": "^11.7.0", "axios": "^0.27.2", "qs": "^6.10.3", "vue": "^2.6.14"}, "devDependencies": {"@babel/core": "^7.15.5", "@babel/eslint-parser": "^7.15.4", "@vue/cli-plugin-babel": "^3.3.0", "@vue/cli-plugin-eslint": "^3.1.1", "@vue/cli-service": "^3.12.1", "eslint": "^7.32.0", "eslint-plugin-vue": "^7.17.0", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "@babel/eslint-parser"}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}