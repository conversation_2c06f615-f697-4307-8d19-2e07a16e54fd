/* BASE */
input,
button,
select {
  font: inherit;
}

/* FORM LINE */
.form-line {
  padding: 8px;
  margin: 0 8px;
  border-radius: 0px;
}

li#id_38 {
  display: none;
}

.form-line-active {
  background-color: #e5e5e5;
}

.form-line-error {
  background-color: #ffc9c9;
}

.form-error-message {
  background-color: #b52626;
  color: #fff;
  font-size: 14px;
  font-weight: 300;
  margin-top: 8px;
  border-radius: 2px;
  display: inline-block;
  width: auto;
  padding: 4px 4px 4px 28px;
}

/* TOP BAR*/
.error-navigation-container {
  background-color: #b52626;
  font-size: 14px;
  font-weight: 300;
  line-height: 1.43;
  padding: 0 8px;
}

.error-navigation-inner {
  max-width: 343px;
  padding: 7px 0;
}

.error-navigation-container.is-success {
  color: #333;
  background-color: #cbffda;
}

.error-navigation-container button {
  height: 56px;
  padding: 16px 28px;
  border-radius: 2px;
  font-size: 18px;
  font-weight: 300;
  cursor: pointer;
  background-color: #fff;
  color: #333;
}

button.error-navigation-done-button {
  background-color: #106427;
  color: #fff;
}

/* FORM */
.form-all {
  font-size: 18px;
  color: #333;
  font-weight: 300;
  max-width: 359px;
  width: 100%;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.12), 0 0 2px 0 rgba(0, 0, 0, 0.14);
}

ul.page-section {
  padding: 0;
  gap: 28px;
}

li.form-line:not(.form-line-column) .form-input-wide[data-layout='half'] {
  width: 100%;
}

/* HEADER */
.form-header-group {
  padding: 28px 0;
  margin: 0 16px;
}

.header-default .form-header {
  font-size: 28px;
  font-weight: 200;
  line-height: 1.14;
  margin-bottom: 0px;
}

/* LABEL */
.form-label-top {
  margin-left: 0px;
  margin-bottom: 8px;
  color: #333;
  font-size: 18px;
  font-weight: 300;
}
.form-label.form-label-auto {
  display: block;
  text-align: left;
  width: 100%;
  font-size: 18px;
  font-weight: 300;
  color: #333;
  line-height: 1.33;
}

/* SUB-LABEL */
.form-sub-label {
  font-size: 14px;
  font-weight: 300;
  line-height: 1.43;
  color: #333;
  margin-top: 8px;
  margin-left: 0;
  display: block;
  word-break: break-word;
}

/* INPUT */
input[type='email'],
input[type='text'] {
  font-size: 18px;
  font-weight: 300;
  line-height: 24px;
  height: 56px;
  border-radius: 2px;
  border: 1px solid #333;
  padding: 16px 20px;
  color: #333;
  background-color: #fff;
  outline: none;
}
input[type='email'],
input[type='text']::placeholder {
  color: #666;
}

input[type='email']:hover,
input[type='text']:hover {
  background-color: #e5e5e5;
  border: 1px solid #000;
  color: #000;
}

.form-dropdown,
.form-textarea,
.form-textbox:not(#productSearch-input),
.signature-wrapper {
  font-size: 18px;
  border-radius: 2px;
  display: block;
}

.form-line-error .form-validation-error:hover {
  border-color: #000;
  box-shadow: none;
  color: #000;
}

.form-line-error .form-validation-error {
  border-color: #333;
}

.form-dropdown,
.form-textarea,
.form-textbox,
.signature-pad-passive,
.signature-wrapper {
  background-color: #fff;
  border: 1px solid #333;
  color: #333;
}

.form-dropdown:hover,
.form-textarea:hover,
.form-textbox:hover,
.signature-pad-passive:hover,
.signature-wrapper:hover {
  background-color: #e5e5e5;
  border: 1px solid #000;
  box-shadow: none;
}

.form-dropdown:focus,
.form-textarea:focus,
.form-textbox:focus,
.signature-pad-passive:focus,
.signature-wrapper:focus {
  outline: #4b96fe 2px solid;
  outline-offset: 2px;
  border-radius: 2px;
  box-shadow: none;
}

/* CHECKBOX */
input[type='checkbox']:hover {
  outline: none;
}
input[type='checkbox']:focus {
  outline: #4b96fe 2px solid;
  outline-offset: 2px;
  border-radius: 2px;
  box-shadow: none;
}

.form-checkbox-item label {
  color: #333;
  font-size: 18px;
  font-weight: 300;
  line-height: 24px;
}

.form-checkbox:checked + label:before {
  background-color: #333;
}

.form-checkbox:hover + label:before {
  background-color: #e5e5e5;
  box-shadow: 0 0 0 4px #e5e5e5;
  border: 1px solid #333;
}

.form-checkbox:checked:hover + label:before {
  background-color: #333;
}

.form-checkbox:checked + label:before:focus {
  outline: #4b96fe 2px solid;
  outline-offset: 2px;
  border-radius: 2px;
  box-shadow: none;
}

/* RADIO */
.form-single-column .form-radio-item {
  color: #333;
  font-size: 18px;
  font-weight: 300;
  line-height: 24px;
}

.form-radio:focus + label:before {
  outline: #4b96fe 2px solid;
  outline-offset: 2px;
  border-radius: 50%;
  box-shadow: none;
}

.form-radio + label:after {
  background-color: #000;
}

.form-radio:checked + label:before {
  border-color: #000;
}

.form-radio:hover + label:before {
  border-color: #000;
  background-color: #e5e5e5;
  box-shadow: 0 0 0 4px #e5e5e5;
}

.form-radio:checked:hover + label:before {
  background-color: #e5e5e5;
  box-shadow: 0 0 0 4px #e5e5e5;
}

/* TEXT-ARIA */
textarea {
  color: #333;
  font-size: 18px;
  font-weight: 300;
}

textarea::placeholder {
  color: #666;
  font-size: 18px;
  font-weight: 300;
}

.form-dropdown:not(.time-dropdown):not(:required),
.form-dropdown:not(:required),
.form-dropdown:required:invalid {
  color: #333;
}

/* LINK */
a:link,
a:visited {
  color: #333;
  font-size: 18px;
  font-weight: 300;
  line-height: 24px;
  text-decoration: underline;
}

a:hover {
  color: #000;
}

a:focus {
  outline: #4b96fe 2px solid;
  outline-offset: 2px;
  border-radius: 2px;
  box-shadow: none;
}

/* SELECT */
.form-dropdown:not([size]),
.form-textbox {
  height: 56px;
}

select {
  height: 56px;
  border-radius: 2px;
  border: 1px solid #333;
  padding: 16px 20px;
  color: #333;
  background-color: #fff;
  outline: none;
}

select option {
  font-size: 18px;
  font-weight: 300;
  color: #333;
}

select:hover {
  background-color: #e5e5e5;
  border: 1px solid #333;
  color: #000;
}

.form-line-error .form-validation-error:focus {
  box-shadow: none;
  border: 1px solid #333333;
  outline: #4b96fe 2px solid;
  outline-offset: 2px;
  border-radius: 2px;
}

.form-dropdown.is-active {
  color: #333;
}

.form-textbox::placeholder,
.form-dropdown:not(.time-dropdown):not(:required),
.form-dropdown:not(:required),
.form-dropdown:required:invalid {
  color: #333;
  font-size: 18px;
  font-weight: 300;
  line-height: 1.33;
}

.form-dropdown {
  padding: 16px 20px;
}

/* BUTTON */
.form-buttons-wrapper {
  margin: 0px;
  padding: 28px 0px;
  width: 100%;
}

.jf-form-buttons:not(.form-pagebreak-back) {
  margin: 0 16px;
}

.submit-button {
  background-color: #d0ff00;
  height: 56px;
  padding: 16px 28px;
  border-radius: 2px;
  color: #333;
  line-height: 1.33;
  font-size: 18px;
  font-weight: 300;
  width: 100%;
  margin: 0;
  border: 1px solid transparent;
  outline: none;
}

.submit-button:hover {
  background-color: #d0ff00;
  border: 1px solid #000;
  color: #000;
}

.submit-button:active {
  background-color: #d0ff00;
  border: 2px solid #000;
  color: #000;
}

.submit-button:focus {
  outline: #4b96fe 2px solid;
  outline-offset: 2px;
  border-radius: 2px;
  box-shadow: none;
}

.page-section li .form-line-active[data-type='control_button'] {
  background-color: #fff !important;
}

/* TERMS AND CONDITIONS */
iframe[data-client-id='52948fb29322cd302b00000c'] {
  height: 91px !important;
}

div[data-widget-name='Nutzungsbedingungen'] {
  overflow-x: unset !important;
  height: 80px !important;
}

/* MEDIA QUERY */
@media (min-width: 768px) {
  .form-all {
    max-width: 648px;
  }

  .error-navigation-inner {
    max-width: 648px;
  }

  iframe[data-client-id='52948fb29322cd302b00000c'] {
    height: 44px !important;
  }

  div[data-widget-name='Nutzungsbedingungen'] {
    height: 44px !important;
  }
}

@media screen and (max-width: 480px) {
  .jf-form-buttons.submit-button:only-child {
    margin-left: 16px;
  }
}

@media (min-width: 1920px) {
  .form-all {
    max-width: 648px;
  }

  .error-navigation-inner {
    max-width: 772px;
  }
}

/*Paragraph. Relevant for follow forms:
    03 Spare Parts Request Form
    04 Service Request Form
    06 Xtra Service Form
    */
/* li[data-type='control_text'] {
  font-size: 18px;
  line-height: 1.33;
  font-weight: 300;
  word-break: break-word;
} */
