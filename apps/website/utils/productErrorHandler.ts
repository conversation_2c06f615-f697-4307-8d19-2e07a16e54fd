/**
 * Hilfsfunktion zur einheitlichen Behandlung von Produktfehlern
 * Wird sowohl von der productShortLink.ts Middleware als auch von der [productnumber].vue Komponente verwendet
 */

import { createError } from 'nuxt/app';

export interface ProductErrorData {
  errorCode: string;
  productNumber: string;
  marketName: string;
  replacementType?: string;
  replacementProductNumber?: string;
  replacementProductSlug?: string;
}

export interface ProductErrorOptions {
  product: any;
  productNumber: string;
  market: string;
  route?: any;
  language?: string;
  productsPath?: string;
}

/**
 * Behandelt Produktfehler und gibt entweder ein Error-Objekt zurück oder leitet zu einem Ersatzprodukt weiter
 *
 * @param options Optionen für die Fehlerbehandlung
 * @returns Error-Objekt oder null, wenn eine Weiterleitung erfolgt
 */
export function handleProductError(options: ProductErrorOptions) {
  const { product, productNumber, market, route, language, productsPath } = options;

  // Wenn kein Produkt gefunden wurde oder ein Fehlercode zurückgegeben wurde
  if (!product || product.details?.statusCode) {
    const errorData: any = {
      statusCode: 404,
      statusMessage: 'Product Not Found',
      fatal: false
    };

    // Wenn ein Produkt gefunden wurde, aber einen Fehlercode hat
    if (product && product.details) {
      // Extrahiere den Fehlercode aus der Antwort
      const type = product.details.type || 'Nil';
      // Konvertiere den Typ in den erwarteten errorCode (kleingeschrieben)
      const errorCode = type.toLowerCase();

      errorData.data = {
        errorCode,
        productNumber: productNumber.toString(),
        marketName: market
      };

      // Wenn es sich um einen historischen Artikel mit Nachfolgeprodukten handelt
      if (errorCode === 'historic' && product.details.successors && product.details.successors.length > 0) {
        // Suche nach einem kompatiblen Nachfolgeprodukt
        const compatibleSuccessor = product.details.successors.find((s: any) => s.compatible);

        if (compatibleSuccessor) {
          errorData.data.replacementType = compatibleSuccessor.compatible ? 'Ersatz' : 'Nachfolge';
          errorData.data.replacementProductNumber = compatibleSuccessor.number;
          errorData.data.replacementProductSlug = compatibleSuccessor.slug;

          // Bei 300/303 Statuscode direkt weiterleiten, wenn eine URL vorhanden ist
          if ((product.details.statusCode === 300 || product.details.statusCode === 303) &&
              route && language && productsPath) {
            if (compatibleSuccessor.number && compatibleSuccessor.slug) {
              const nuxtApp = useNuxtApp();
              const target = nuxtApp?.$products?.buildProductUrlWithPrefix(
                language,
                market,
                productsPath,
                compatibleSuccessor.number,
                compatibleSuccessor.slug
              );

              if (target) {
                const queryString = route.fullPath?.split('?')[1];
                const redirectUrl = queryString ? `${target}?${queryString}` : target;
                return {
                  redirect: true,
                  url: redirectUrl,
                  code: product.details.statusCode
                };
              }
            }
          }
        }
      }
    }

    return {
      error: true,
      errorData
    };
  }

  return null;
}
