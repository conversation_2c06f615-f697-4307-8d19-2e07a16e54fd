<template>
  <UiMain class="product-error-page">
    <div class="layout-boxed">
      <div class="product-error-container">
        <h1 class="product-error-title">{{ errorTitle }}</h1>
        <p class="product-error-message">{{ errorMessage }}</p>
        
        <div class="product-error-actions">
          <!-- Suchlink für alle Fehlerfälle -->
          <UiButton 
            v-if="showSearchLink" 
            :label="$t('productsErrorSearchButton')" 
            type="primary" 
            icon="search" 
            :show-icon="true" 
            :link="{ url: searchUrl }"
          />
          
          <!-- Mark<PERSON><PERSON><PERSON>hl-Link für "market" Fehlerfall -->
          <UiButton 
            v-if="errorCode === 'market' && showMarketLink" 
            :label="$t('productsErrorMarketButton')" 
            type="secondary" 
            icon="globe" 
            :show-icon="true" 
            :link="{ url: marketSelectionUrl }"
          />
          
          <!-- Link zum Ersatz- oder Nachfolgeartikel für "historic" Fehlerfall -->
          <UiButton 
            v-if="errorCode === 'historic' && replacementProductUrl" 
            :label="replacementButtonLabel" 
            type="primary" 
            icon="arrow-right" 
            :show-icon="true" 
            :link="{ url: replacementProductUrl }"
          />
        </div>
      </div>
    </div>
  </UiMain>
</template>

<script setup lang="ts">
import UiMain from '@dornbracht/ui/src/components/atoms/Main.vue';
import UiButton from '@dornbracht/ui/src/components/atoms/Button.vue';

const props = defineProps({
  errorCode: {
    type: String,
    required: true,
    validator: (value: string) => ['nil', 'preview', 'historic', 'market'].includes(value)
  },
  productNumber: {
    type: String,
    default: ''
  },
  marketName: {
    type: String,
    default: ''
  },
  replacementType: {
    type: String,
    default: '',
    validator: (value: string) => ['', 'Ersatz', 'Nachfolge'].includes(value)
  },
  replacementProductNumber: {
    type: String,
    default: ''
  },
  replacementProductSlug: {
    type: String,
    default: ''
  }
});

const { $i18n, $localesAndMarkets } = useNuxtApp();
const locale = $i18n?.locale.value;
const language = $localesAndMarkets?.getContentLanguage(locale);
const market = $localesAndMarkets?.getMarket(locale);
const productsPath = $i18n?.t('productsProductsPath');

// Berechne die URLs für die verschiedenen Aktionen
const searchUrl = computed(() => `/${locale}/search`);
const marketSelectionUrl = computed(() => `/${locale}/market-selection`);

// Berechne die URL für Ersatz- oder Nachfolgeartikel
const replacementProductUrl = computed(() => {
  if (props.errorCode === 'historic' && props.replacementProductNumber && props.replacementProductSlug) {
    return `/${locale}/${productsPath}/${props.replacementProductNumber}/${props.replacementProductSlug}`;
  }
  return '';
});

// Bestimme den Titel basierend auf dem Fehlercode
const errorTitle = computed(() => {
  switch (props.errorCode) {
    case 'nil':
      return $i18n.t('productsErrorTitleNotExist');
    case 'preview':
      return $i18n.t('productsErrorTitlePreview');
    case 'historic':
      return $i18n.t('productsErrorTitleHistoric');
    case 'market':
      return $i18n.t('productsErrorTitleMarket');
    default:
      return $i18n.t('productsErrorTitleGeneric');
  }
});

// Bestimme die Fehlermeldung basierend auf dem Fehlercode
const errorMessage = computed(() => {
  switch (props.errorCode) {
    case 'nil':
      return $i18n.t('productsErrorMessageNotExist', { productNumber: props.productNumber });
    case 'preview':
      return $i18n.t('productsErrorMessagePreview', { productNumber: props.productNumber });
    case 'historic':
      if (props.replacementType === 'Ersatz') {
        return $i18n.t('productsErrorMessageHistoricReplacement', { productNumber: props.productNumber });
      } else if (props.replacementType === 'Nachfolge') {
        return $i18n.t('productsErrorMessageHistoricSuccessor', { productNumber: props.productNumber });
      } else {
        return $i18n.t('productsErrorMessageHistoricNoReplacement', { productNumber: props.productNumber });
      }
    case 'market':
      return $i18n.t('productsErrorMessageMarket', { productNumber: props.productNumber, marketName: props.marketName });
    default:
      return $i18n.t('productsErrorMessageGeneric');
  }
});

// Bestimme den Button-Text für Ersatz- oder Nachfolgeartikel
const replacementButtonLabel = computed(() => {
  if (props.replacementType === 'Ersatz') {
    return $i18n.t('productsErrorReplacementButton');
  } else if (props.replacementType === 'Nachfolge') {
    return $i18n.t('productsErrorSuccessorButton');
  }
  return '';
});

// Zeige den Such-Link für alle Fehlerfälle
const showSearchLink = computed(() => true);

// Zeige den Marktauswahl-Link nur für den "market" Fehlerfall
const showMarketLink = computed(() => props.errorCode === 'market');
</script>

<style lang="scss" scoped>
@use '@dornbracht/ui/src/styles/variables.scss' as variables;
@use '@dornbracht/ui/src/styles/mixins' as mixins;

.product-error-page {
  padding-block: variables.$distance-static-64;
}

.product-error-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.product-error-title {
  @include mixins.font-headline('l');
  margin-bottom: variables.$distance-static-24;
}

.product-error-message {
  @include mixins.font-copy('l');
  margin-bottom: variables.$distance-static-40;
}

.product-error-actions {
  display: flex;
  flex-direction: column;
  gap: variables.$distance-static-16;
  align-items: center;
  
  @media (min-width: variables.$breakpoint-tablet) {
    flex-direction: row;
    justify-content: center;
  }
}
</style>
