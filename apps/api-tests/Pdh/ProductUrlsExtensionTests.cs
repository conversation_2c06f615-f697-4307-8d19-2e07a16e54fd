using Dornbracht.Api.Pdh.Extensions;
namespace Dornbracht.Api.Tests.Pdh;

public class ProductUrlsExtensionTests
{
    [Test]
    public void TestEscapeProductUrl()
    {
      var slugFromApi = "/HOT & COLD WATER DISPENSER ! ' ( ) [ ] _ . \\ ~ , : ; * ? \" < > |  # @ = & + % $ test ä ö ü â à";
      var result = ProductUrlExtension.EscapeProductUrl(slugFromApi);

      Assert.That(result, Is.EqualTo("/hot-cold-water-dispenser-test-%C3%A4-%C3%B6-%C3%BC-%C3%A2-%C3%A0"));
    }
}
