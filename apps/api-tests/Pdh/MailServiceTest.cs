using Dornbracht.Api.Pdh.Models.Pdh;
using Dornbracht.Api.Pdh.Services;
namespace Dornbracht.Api.Tests.Pdh;

public class MailServiceTest
{
  [Test]
  public void CreateMessatgesTest()
  {
    // prepare
    string from = "from";
    string to = "to";
    int templateId = 1;
    string language = "de";
    string uri = "uri";

    // act
    MailjetMessages messages = MailService.CreateMessage(templateId, from, to, language, uri);

    // assert
    Assert.That(messages.messages.Length, Is.EqualTo(1));
    Assert.That(messages.messages[0].from.name, Is.EqualTo(from));
    Assert.That(messages.messages[0].to.Length, Is.EqualTo(1));
    Assert.That(messages.messages[0].to[0].name, Is.EqualTo(to));

    Assert.That(messages.messages[0].templateLanguage, Is.True);
    Assert.That(messages.messages[0].variables.ContainsKey("download_link"), Is.True);
    Assert.That(messages.messages[0].variables.ContainsKey("download_language"), Is.True);
  }

  [Test]
  public void EndcodeCredentialsTest()
  {
    // prepare
    string apiKey = "api";
    string secretKey = "secret";

    // act
    String encoded = MailService.EncodeCredentials(apiKey, secretKey);

    // assert
    Assert.That(encoded, Is.EqualTo("YXBpOnNlY3JldA=="));
  }
}
