# E2E Testing with Playwright and Lighthouse CI

This project includes end-to-end (E2E) testing using Playwright, 
as well as performance, accessibility, best practices, and SEO audits using Lighthouse CI.

## Prerequisites
- VS Code Extension [Playwright Test for VSCode] (https://marketplace.visualstudio.com/items?itemName=ms-playwright.playwright)
- [Yarn] (https://yarnpkg.com/getting-started/install)

## Project setup

```bash
yarn install
```

### Run Playwright Tests

```bash
npx playwright test
```

### Run Lighthouse CI Audit
 ```bash
   # Run the Lighthouse CI Audit for Desktop
   npx lhci autorun 

   # Run the Lighthouse CI Audit for Mobile with a specific configuration file
   npx lhci autorun --config=lighthouserc-mobile.json
```