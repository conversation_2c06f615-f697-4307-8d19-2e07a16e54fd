// @ts-check
import { test, expect } from '@playwright/test';

test('product short link with preselected language', async ({ page }) => {
  await page.goto('./de-de');
  await page.goto('./p/33500782-00');
  await expect(page).toHaveURL(/.*\/de-de\/produkte\/33500782-00\/.*/);
});

test('product short link without preselected language', async ({ page }) => {
  await page.goto('./p/33500782-00');
  await expect(page).toHaveURL(/.*\/en\/products\/33500782-00\/.*/);
});

test('product short link with url params with preselected language', async ({ page }) => {
  await page.goto('./de-de');
  await page.goto('./p/33500782-00?utm_term=test');
  await expect(page).toHaveURL(/.*\/de-de\/produkte\/33500782-00\/.*/);
  expect(page.url()).toContain('?utm_term=test');
});

test('product short link with url params without preselected language', async ({ page }) => {
  await page.goto('./p/33500782-00?utm_term=test');
  await expect(page).toHaveURL(/.*\/en\/products\/33500782-00\/.*/);
  expect(page.url()).toContain('?utm_term=test');
});

test('product short link with US suffix', async ({ page }) => {
  await page.goto('./en-us');
  await page.goto('./p/33500782-00');
  await expect(page).toHaveURL(/.*\/en-us\/products\/33500782-000010\/.*/);
});

test('product short link without US suffix', async ({ page }) => {
  await page.goto('./en-us');
  await page.goto('./p/13715811-00');
  await expect(page).toHaveURL(/.*\/en-us\/products\/13715811-00\/.*/);
});
